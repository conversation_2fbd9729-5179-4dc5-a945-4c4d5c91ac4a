<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.w3.org/1999/xhtml" lang="en">

<head>
    <meta charset="UTF-8">
    <title>设备协议清单管理</title>
    <th:block th:include="include::header('设备协议清单管理')" />
    <style>
        #febs-porDeviceProtocolListManger .layui-input-inline {
            width: 120px;
        }

        #febs-porDeviceProtocolListManger .yfs-list {
            background-color: #f7f7f7;
            height: 100%;
        }

        #febs-porDeviceProtocolListManger .ztree li span.button.add {
            margin-left: 2px;
            margin-right: -1px;
            background-position: -144px 0;
            vertical-align: top;
        }

        #febs-porDeviceProtocolListManger .ztree li a {
            height: 25px;
        }

        #febs-porDeviceProtocolListManger .ztree * {
            padding: 0;
            margin: 0;
            font-size: 16px;
            font-family: Verdana, Arial, Helvetica, AppleGothic, sans-serif;
        }

        #febs-porDeviceProtocolListManger .button .ico_open {
            width: 20px;
            height: 20px;
        }

        #febs-porDeviceProtocolListManger .tree-selected {
            color: #2F9688;
            font-weight: bold;
        }

        .layui-table tbody tr:hover,
        .layui-table-hover {
            background-color: #F8F8F8;
        }

        .layui-table-checked.layui-table-hover {
            background-color: #74b9ff !important;
        }

        #febs-porDeviceProtocolListManger .viewBgColor {
            background-color: #9df50a !important;
        }

        #detail .layui-form-item {
            margin-bottom: 0;
            padding-top: 10px;
            padding-bottom: 5px;
        }

        #febs-porDeviceProtocolListManger .search-form {
            padding-bottom: 0;
            box-sizing: border-box;
        }

        #febs-porDeviceProtocolListManger .jhg-body-search {
            background-color: #F8F8F8;
        }

        #febs-porDeviceProtocolListManger .edited {
            background-color: #74b9ff !important;
        }

        #mainTable .layui-table-tool-temp {
            padding-right: 0;
        }

        #febs-porDeviceProtocolListManger .updateColorClass {
            background-color: #efc680
        }

        .layui-anim .layui-anim-upbit {
            z-index: 9999;
        }

        .layui-table-header {
            height: 36px;
        }

        .shipNoText {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 180px;
            position: absolute;
            right: 10px;
        }

        .jhg-body-search .layui-form-item .layui-input-inline .layui-input,
        .jhg-body-search .layui-form-item .layui-input-inline .layui-select {
            width: 182px;
        }

        /* 为隔行变色定义CSS */
        .layui-table tbody tr:nth-child(odd) {
            background-color: #ffffff;
            /* 奇数行背景色 */
        }

        .layui-table tbody tr:nth-child(even) {
            background-color: #f2f2f2;
            /* 偶数行背景色 */
        }

        .jhg-body-search .layui-form-item .layui-form-label {
            padding: 5px 10px;
            width: auto;
        }

        .icon-img {
            width: 16px;
            height: 15px;
            margin-left: 2px;
            margin-bottom: 6px;
        }

        .layui-btn-container .layui-btn {
            margin-right: 5px;
        }

        .layui-form-item .layui-inline {
            margin-right: 0 !important;
        }

        .layui-table-tool {
            position: relative;
            width: 100%;
            min-height: 45px;
            line-height: 30px;
            padding: 5px 15px;
            border-width: 0;
            border-bottom-width: 1px;
        }

        .jhg-body-search .layui-form-item {
            padding-bottom: 0
        }

        .btnRemark {
            cursor: pointer;
            position: fixed;
            right: 154px;
            top: 58px;
            width: 35px;
            height: 35px;
            background: #1b83f0;
            border-radius: 35px;
            line-height: 34px;
            text-align: center;
            display: inline-block;
            color: #ffff;
            z-index: 100000;
        }

        .btnRemarkContentClass {
            position: fixed;
            z-index: 100000;
            top: 50px;
            right: 50px;
            width: 190px;
            padding: 20px;
            cursor: move;
            background: #9dd5ebcc;
            animation: fadenumstyle .5s;
            display: none;
        }

        .transferMaterialTipsClass {
            cursor: pointer;
            position: fixed;
            right: 218px;
            top: 58px;
            width: 100px;
            height: 35px;
            background: #1b83f0;
            border-radius: 35px;
            line-height: 34px;
            text-align: center;
            display: inline-block;
            color: #ffff;
            z-index: 100000;
        }

        .transferMaterialTipsContentClass {
            position: fixed;
            z-index: 100000;
            top: 50px;
            right: 120px;
            width: 1100px;
            height: 600px;
            padding: 20px;
            /*cursor: move;*/
            background: #9dd5ebcc;
            animation: fadenumstyle .5s;
            display: none;
        }

        #dstShipNoSelect xm-select>.xm-body {
            width: 350px;
        }

        #febs-porDeviceProtocolListManger .jhg-body-search .layui-form-item .layui-input-inline .layui-input,
        #febs-porDeviceProtocolListManger .jhg-body-search .layui-form-item .layui-input-inline .layui-select {
            width: 172px;
        }

        #febs-porDeviceProtocolListManger #searchForm .layui-form-item .layui-input-inline {
            width: 172px !important;
        }

        /* 隐藏滚动条但保持滚动功能 */
        .layui-row {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .layui-row::-webkit-scrollbar {
            display: none;
        }
    </style>
    <link rel="stylesheet" th:href="@{/febs/views/css/commonZs.css}" media="all">
</head>

<body>
    <div class="layui-fluid layui-anim febs-anim page-body" id="febs-porDeviceProtocolListManger" lay-title="设备协议清单管理">
        <div class="layui-row" style="display: flex;flex-direction: column; height:100vh; overflow-y: auto;">
            <form class="layui-form search-form" style="background-color: #ffffff;width: 100%; flex-shrink: 0;"
                id="searchForm">
                <div class="jhg-body-search" style="background-color: #ffffff;">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm">船号:</label>
                            <div class="layui-input-inline" style="width: 170px">
                                <div id="shipNoSelect"></div>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm">专业划分:</label>
                            <div class="layui-input-inline">
                                <div id="proTypeList"></div>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm">
                                设计专业:</label>
                            <div class="layui-input-inline">
                                <div id="professions"></div>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm" for="basicMaterialNo">物资编码:</label>
                            <div class="layui-input-inline">
                                <input type="text" name="basicMaterialNo" id="basicMaterialNo" autocomplete="off"
                                    class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm" for="basicMaterialDesc">物资描述:</label>
                            <div class="layui-input-inline" style="width: 120px">
                                <input type="text" name="basicMaterialDesc" id="basicMaterialDesc" autocomplete="off"
                                    class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline" shiro:hasPermission="porDeviceManageView:view">
                            <div id="headQuery" class="layui-btn searchBlue layui-btn-sm">
                                <em class="layui-icon">&#xe615;</em> 检索
                            </div>
                            <div id="queryOther1" class="layui-hide">检索(假)</div>
                        </div>
                        <div class="layui-inline">
                            <div id="moreQuery" class="layui-btn searchBlue layui-btn-sm">
                                更多查询
                            </div>
                        </div>
                        <div class="layui-inline">
                            <div class="btnRemark" id="btnRemark">备注</div>
                            <div id="btnRemarkContent" class="btnRemarkContentClass">
                                <div style="margin-bottom: 2px">备注:</div>
                                <div style="display: flex;margin-bottom: 2px">
                                    <div style="height: 19px;width: 30px;background-color: #9df50a;margin-right: 20px">
                                    </div>
                                    <div>: 行点击</div>
                                </div>
                                <div style="display: flex;margin-bottom: 2px">
                                    <div style="height: 19px;width: 30px;background-color: #74b9ff;margin-right: 20px">
                                    </div>
                                    <div>: 行选中</div>
                                </div>
                                <div style="display: flex;margin-bottom: 2px">
                                    <div style="height: 19px;width: 30px;background-color: #efc680;margin-right: 20px;">
                                    </div>
                                    <div>: 等待二次上传SAP</div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <div class="transferMaterialTipsClass" id="transferMaterialTips"
                                shiro:hasPermission="porDeviceManageView:transferMaterial">
                                <i class="layui-icon layui-icon-login"></i>
                                物资移库
                            </div>
                            <div id="transferMaterialTipsContent" class="transferMaterialTipsContentClass">
                                <div id="searchDiv"
                                    style="background-color: white;margin-bottom: 10px;padding-top: 10px;">
                                    <!--                                <div class="layui-inline">-->
                                    <!--                                    <label class="layui-form-label layui-form-label-sm">船号:</label>-->
                                    <!--                                    <div class="layui-input-inline" style="width: 220px">-->
                                    <!--                                        <div id="porShipNoSelect"></div>-->
                                    <!--                                    </div>-->
                                    <!--                                </div>-->
                                    <div class="layui-inline">
                                        <label class="layui-form-label layui-form-label-sm">目标船:</label>
                                        <div class="layui-input-inline" style="width: 220px">
                                            <div id="dstShipNoSelect"></div>
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label layui-form-label-sm"
                                            style="padding-left: 40px;">专业划分:</label>
                                        <div class="layui-input-inline">
                                            <div id="proTypeListTransfer"></div>
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label layui-form-label-sm">设计专业:</label>
                                        <div class="layui-input-inline">
                                            <div id="professionsTransfer"></div>
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label layui-form-label-sm"
                                            for="porHeadNoTransfer">POR:</label>
                                        <div class="layui-input-inline">
                                            <input type="text" id="porHeadNoTransfer" autocomplete="off"
                                                class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label layui-form-label-sm" style="width: 100px;"
                                            for="basicMaterialNoOrDesc">
                                            物资编码/描述:
                                        </label>
                                        <div class="layui-input-inline">
                                            <input type="text" id="basicMaterialNoOrDesc" autocomplete="off"
                                                class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-inline" shiro:hasPermission="porDeviceManageView:view">
                                        <div id="transferQuery" class="layui-btn searchBlue layui-btn-sm">
                                            <em class="layui-icon">&#xe615;</em> 检索
                                        </div>
                                    </div>
                                    <div class="layui-inline" shiro:hasPermission="porDeviceManageView:edit">
                                        <div class="layui-btn layui-btn-sm layui-bg-red" id="deleteStorge">
                                            <i class="layui-icon layui-icon-delete"></i> 删除
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div id="applyStorge"
                                        style="width:100%;background-color: white;height: 475px;overflow: hidden;">
                                        <div class="jhg-body-table">
                                            <table class="layui-hide" id="applyStorgeList" lay-filter="applyStorgeList">
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="layui-inline" style="left: 45%;margin-top: 10px;">
                                        <div id="transferSubmit" class="layui-btn searchBlue layui-btn-sm">
                                            <em class="layui-icon"></em> 提交
                                        </div>
                                        <div id="transferCancle" class="layui-btn layui-btn-sm">
                                            <em class="layui-icon"></em> 取消
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <input id="editFlagWithLine" hidden="" />
                        <input id="editFlagWithPre" hidden="" />
                    </div>
                    <div class="layui-form-item layui-hide" id="moreQueryDiv">
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm" for="directorName">负责人:</label>
                            <div class="layui-input-inline">
                                <input type="text" name="directorName" id="directorName" autocomplete="off"
                                    class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm" for="sapStatus">SAP状态:</label>
                            <div class="layui-input-inline" style="width: 120px">
                                <select id="sapStatus" lay-filter="codeType">
                                    <option value="">所有</option>
                                    <option value="0">未处理</option>
                                    <option value="1">部分处理</option>
                                    <option value="2">全部处理</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm">
                                船型区域:</label>
                            <div class="layui-input-inline" style="width: 220px">
                                <div id="regions"></div>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm" for="porHeadNo">POR编号:</label>
                            <div class="layui-input-inline">
                                <input type="text" name="porHeadNo" id="porHeadNo" autocomplete="off"
                                    class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm" for="codeType">POR状态:</label>
                            <div class="layui-input-inline">
                                <select id="codeType" lay-filter="codeType">
                                    <option value="">所有</option>
                                    <option value="0">未上传</option>
                                    <option value="1">已上传</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
            <div class="layui-form col-md-3 " style="display: flex;height: 43.4vh; flex-shrink: 0;">
                <div id="mainTable" style="width: 100%;background-color: white; margin-bottom: 3px; overflow: hidden;">
                    <div class="jhg-body-table">
                        <table class="layui-hide" lay-filter="porHeadMaterialTable" id="protocolList">
                        </table>
                    </div>
                </div>
            </div>
            <form class="layui-form search-form" style="flex-shrink: 0;">
                <div class="jhg-body-search">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm" style="width: 70px"
                                for="basicMaterialNoLine">物资编码:</label>
                            <div class="layui-input-inline" style="width:180px;">
                                <input type="text" name="basicMaterialNoLine" id="basicMaterialNoLine"
                                    autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm" style="width: 70px"
                                for="basicMaterialDescLine">物资描述:</label>
                            <div class="layui-input-inline" style="width:180px;">
                                <input type="text" name="basicMaterialDescLine" id="basicMaterialDescLine"
                                    autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline" style="margin-left: -5px">
                            <div id="lineQuery" class="layui-btn searchBlue layui-btn-sm">
                                <em class="layui-icon">&#xe615;</em> 检索
                            </div>
                            <div id="queryOther2" class="layui-hide">检索(假)</div>
                        </div>
                        <div class="layui-inline" shiro:hasPermission="porDeviceManageView:edit">
                            <div class="layui-btn layui-btn-sm blueBtm" id="addLine" lay-event="addLine">
                                <i class="layui-icon layui-icon-add-1"></i> 新增
                            </div>
                        </div>
                        <div class="layui-inline" shiro:hasPermission="porDeviceManageView:edit">
                            <div class="layui-btn layui-btn-sm blueBtm" id="importLine" lay-event="importLine">
                                <em class="layui-icon">&#xe7aa;</em> 导入
                            </div>
                        </div>
                        <!--                    <div class="layui-inline" shiro:hasPermission="porDeviceManageView:edit">-->
                        <!--                        <div class="layui-btn layui-btn-sm blueBtm" id="saveBatchLine" lay-event="saveBatchLine">-->
                        <!--                            <em class="layui-icon">&#xe7a9;</em> 保存-->
                        <!--                        </div>-->
                        <!--                    </div>-->
                        <div class="layui-inline" shiro:hasPermission="porDeviceManageView:edit">
                            <div class="layui-btn layui-btn-sm layui-bg-red" id="deleteLine" lay-event="deleteLine">
                                <i class="layui-icon layui-icon-delete"></i> 删除
                            </div>
                        </div>
                        <div class="layui-inline" style="float: right;">
                            <div id="lineTotalCount"
                                style="width: 140px !important;text-align: right;padding-top: 6px;padding-right: 15px;">
                            </div>
                        </div>
                        <div class="layui-inline" shiro:hasPermission="porDeviceManageView:edit">
                            <div class="layui-btn layui-btn-sm blueBtm febs-hide" id="saveBatchLineExit"
                                lay-event="saveBatchLineExit">
                                <em class="layui-icon">&#xe7a9;</em> 保存
                            </div>
                        </div>
                        <div class="layui-inline" shiro:hasPermission="porDeviceManageView:transferMaterial">
                            <div class="layui-btn layui-btn-sm blueBtm" id="transferMaterialForApply"
                                lay-event="transferMaterialForApply">
                                <i class="layui-icon">&#xe60a;</i> 物资移库
                            </div>
                        </div>
                        <!--                    <button class="layui-btn layui-btn-sm" id="addLine" lay-event="addLine" shiro:hasPermission="porDeviceManageView:edit">-->
                        <!--                        <i class="layui-icon layui-icon-add-1"></i>新增-->
                        <!--                    </button>-->
                        <!--                    <button class="layui-btn layui-btn-sm layui-bg-red" id="deleteLine" lay-event="deleteLine" shiro:hasPermission="porDeviceManageView:edit">-->
                        <!--                        <i class="layui-icon layui-icon-delete"></i>删除-->
                        <!--                    </button>-->
                        <!--                    <button type="button" class="layui-btn layui-btn-sm layui-bg-green" id="importLine" lay-event="importLine" shiro:hasPermission="porDeviceManageView:edit"><i-->
                        <!--                            class="layui-icon">&#xe7aa;</i>导入-->
                        <!--                    </button>-->
                        <!--                    <button type="button" class="layui-btn layui-btn-sm blueBtm" id="saveBatchLine" lay-event="saveBatchLine" shiro:hasPermission="porDeviceManageView:edit">-->
                        <!--                        <i class="layui-icon">&#xe7a9;</i>保存-->
                        <!--                    </button>-->
                        <!--                    <button type="button" class="layui-btn layui-btn-sm blueBtm febs-hide" id="saveBatchLineExit" lay-event="saveBatchLineExit">-->
                        <!--                        <i class="layui-icon">&#xe7a9;</i>保存-->
                        <!--                    </button>-->
                    </div>
                </div>
            </form>
            <div class="layui-form col-md-1" style="display: flex; height: 43.4vh; flex-shrink: 0;">
                <div id="detail" style="width:100%;background-color: white;height: 100%;overflow: hidden;">
                    <div class="jhg-body-table">
                        <table class="layui-hide" id="protocolLineList" lay-filter="protocolLineList">
                        </table>
                    </div>
                </div>
            </div>
            <form class="layui-form search-form" style="flex-shrink: 0;">
                <div class="jhg-body-search">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm" style="width: 70px"
                                for="basicMaterialNoPrevious">物资编码:</label>
                            <div class="layui-input-inline" style="width:180px;">
                                <input type="text" name="basicMaterialNoPrevious" id="basicMaterialNoPrevious"
                                    autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm" style="width: 70px"
                                for="basicMaterialDescPrevious">物资描述:</label>
                            <div class="layui-input-inline" style="width:180px;">
                                <input type="text" name="basicMaterialDescPrevious" id="basicMaterialDescPrevious"
                                    autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline" style="margin-left: -5px">
                            <div id="previousQuery" class="layui-btn searchBlue layui-btn-sm">
                                <em class="layui-icon">&#xe615;</em> 检索
                            </div>
                        </div>
                        <div class="layui-inline" style="float: right;">
                            <div id="previousTotalCount"
                                style="width: 140px !important;text-align: right;padding-top: 6px;padding-right: 15px;">
                            </div>
                        </div>
                    </div>
                </div>
            </form>
            <div class="layui-form col-md-1" style="display: flex; height: 43.4vh; flex-shrink: 0; margin-top: 10px;">
                <div id="previousShipMaterialDiv"
                    style="width:100%;background-color: white;height: 100%;overflow: hidden;">
                    <div class="jhg-body-table">
                        <table class="layui-hide" id="previousShipMaterialTable" lay-filter="previousShipMaterialTable">
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Head工具栏模板 -->
    <script type="text/html" id="deviceProtocolListMangerToolbar">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm blueBtm" id="add" lay-event="add"
                shiro:hasPermission="porDeviceManageView:edit">
            <i class="layui-icon layui-icon-add-1"></i>新增
        </button>
        <button type="button" class="layui-btn layui-btn-sm blueBtm" id="import" lay-event="import"
                shiro:hasPermission="porDeviceManageView:edit">
            <i class="layui-icon">&#xe7aa;</i>导入
        </button>
        <button type="button" class="layui-btn layui-btn-sm blueBtm" id="export" lay-event="export"
                shiro:hasPermission="porDeviceManageView:view">
            <i class="layui-icon layui-icon-export"></i>导出
        </button>
        <button type="button" class="layui-btn layui-btn-sm blueBtm" id="copy" lay-event="copy"
                shiro:hasPermission="porDeviceManageView:copy">
            <i class="layui-icon">&#xe7ef;</i>项目复制
        </button>
        <button type="button" class="layui-btn layui-btn-sm blueBtm" id="confirmSap" lay-event="confirmSap"
                shiro:hasPermission="porDeviceManageView:confirmSap">
            <i class="layui-icon">
                <img data-th-src="@{/img/icon/comfirm.png}" class="icon-img">
            </i>确认
        </button>
        <button class="layui-btn layui-btn-sm blueBtm" id="selectLeftInfos" lay-event="selectLeftInfos"
                shiro:hasPermission="porDeviceManageView:leftInfos">
            <i class="layui-icon">
                <img data-th-src="@{/img/icon/total.png}" class="icon-img">
            </i>统计
        </button>
        <button class="layui-btn layui-btn-sm layui-bg-red" id="delete" lay-event="delete"
                shiro:hasPermission="porDeviceManageView:edit">
            <i class="layui-icon layui-icon-delete"></i>删除
        </button>
        <button type="button" class="layui-btn layui-btn-sm layui-bg-red" id="confirmSapBack" lay-event="confirmSapBack"
                shiro:hasPermission="porDeviceManageView:confirmSapBack">
            <i class="layui-icon">
                <img data-th-src="@{/img/icon/revoke.png}" class="icon-img">
            </i>撤销确认
        </button>
        <!--        <button type="button" class="layui-btn layui-btn-sm blueBtm" id="saveBatch" lay-event="saveBatch">-->
        <!--            <i class="layui-icon">&#xe7a9;</i>保存-->
        <!--        </button>-->
        <button type="button" class="layui-btn layui-btn-sm blueBtm" id="up" lay-event="up"
                shiro:hasPermission="porDeviceManageView:up">
            <i class="layui-icon">&#xe828;</i>上传SAP
        </button>
        <button type="button" class="layui-btn layui-btn-sm blueBtm" id="updatePurchase" lay-event="updatePurchase"
                shiro:hasPermission="porDeviceManageView:updatePurchase">
            <i class="layui-icon">&#xe828;</i>同步采购状态
        </button>
        <div class="layui-inline" style="float: right; width: 125px !important;">
            <div id="headTotalCount" style="text-align: right;padding-top: 10px;word-spacing: 0;">

            </div>
        </div>
    </div>
</script>
    <!-- Line工具栏模板 -->
    <script type="text/html" id="lineToolbar">
    <div class="layui-btn-container">
        <!--        <button class="layui-btn layui-btn-sm" id="addLine" lay-event="addLine" shiro:hasPermission="porDeviceManageView:edit">-->
        <!--            <i class="layui-icon layui-icon-add-1"></i>新增-->
        <!--        </button>-->
        <!--        <button class="layui-btn layui-btn-sm layui-bg-red" id="deleteLine" lay-event="deleteLine" shiro:hasPermission="porDeviceManageView:edit">-->
        <!--            <i class="layui-icon layui-icon-delete"></i>删除-->
        <!--        </button>-->
        <!--        <button type="button" class="layui-btn layui-btn-sm layui-bg-green" id="importLine" lay-event="importLine" shiro:hasPermission="porDeviceManageView:edit"><i-->
        <!--                class="layui-icon">&#xe7aa;</i>导入-->
        <!--        </button>-->
        <!--        <button type="button" class="layui-btn layui-btn-sm blueBtm" id="saveBatchLine" lay-event="saveBatchLine" shiro:hasPermission="porDeviceManageView:edit">-->
        <!--            <i class="layui-icon">&#xe7a9;</i>保存-->
        <!--        </button>-->
        <!--        <button type="button" class="layui-btn layui-btn-sm blueBtm febs-hide" id="saveBatchLineExit" lay-event="saveBatchLineExit">-->
        <!--            <i class="layui-icon">&#xe7a9;</i>保存-->
        <!--        </button>-->
        <!--      2024-05-09  隐藏此导出功能  bwj  -->
        <!--        <button type="button" class="layui-btn layui-btn-sm blueBtm" id="exportLine" lay-event="exportLine"><i-->
        <!--                class="layui-icon">&#xe601;</i>导出-->
        <!--        </button>-->
    </div>
</script>
    <th:block th:include="include::foot" />
    <script data-th-inline="none" type="text/javascript">
        layui.config({
            base: ctx + 'febs/'
        }).extend({
            commonJS: 'lay/extends/common',
            febs: 'lay/modules/febs',
            validate: 'lay/modules/validate',
            formSelects: 'lay/extends/formSelects-v4.min',
            jqueryui: 'lay/extends/jquery-ui.min',
            echarts: 'lay/extends/echarts.min'
        }).use(['jquery', 'validate', 'table', 'form', 'commonJS', 'febs', 'rpcJs', 'jqueryui'], function () {
            var $ = layui.jquery,
                $view = $('#febs-porDeviceProtocolListManger'),
                $mainTable = $('#mainTable'),
                febs = layui.febs,
                form = layui.form,
                commonJS = layui.commonJS,
                table = layui.table,
                rpcJs = layui.rpcJs,
                element = layui.element,
                //选择的船号
                shipIds,
                shipNoSelect,
                professions,
                regions,
                strids,
                proIds,
                // porShipNoSelect,
                dstShipNoSelect,
                professionsTransfer,
                selectedShipId,
                units,
                unitSelect,
                shipTypeId,
                proTypeList,
                proTypeListTransfer,
                $headQuery = $view.find('#headQuery'),
                $lineQuery = $view.find('#lineQuery'),
                proData,
                detailArr = [],
                shipArr = [],
                detailProMap = new Map(),
                proMap = new Map(),
                xsorssMap = new Map(),
                proNoToIdMap = new Map(),
                proNoToTypeMap = new Map(),
                regionsMap = new Map(), permissionList = currentUser.permissionSet,
                allShips = [],
                tableIns,
                transferTableIns,
                tableDetailIns,
                previousTableIns,
                linePhdpId,
                headUploadStatus,
                linePhDesc,
                porId,
                porType,
                headStatus,
                onRowHaveEditShiro,
                xsProfessionUserList = parent.xsProfessionUserList,
                ssProfessionUserList = parent.ssProfessionUserList;

            form.render();

            autoHeight();
            getProfessions();
            setShipNo();
            setProType();
            setProfessions();
            setRegions([]);
            $(window).resize(function () {
                // let bodyTableHeight = $view.find("#mainTable").height()-137;
                // $view.find(".layui-table-body.layui-table-main").css("height",bodyTableHeight);
                autoHeight();
            })
            setUnits();
            initTable();
            let permiFlag = permissionList.indexOf('porDeviceManageView:edit') > -1 ? true : false;
            let permiRestFlag = permissionList.indexOf('porDeviceManageView:restFlg') > -1 ? true : false;
            let uploadFlag = permissionList.indexOf('porDeviceManageView:upload') > -1 ? true : false;

            function autoHeight() {
                //选项卡tab高度
                var tabHeight = $(".febs-tabs-wrap").height() == null ? "0" : $(".febs-tabs-wrap").height();
                //标题头的高度
                var appHeight = $("#app-header").height() == null ? "0" : $("#app-header").height();
                var diff = tabHeight == 0 ? 16 : 52;
                var tempheight = $(window).height() - tabHeight - appHeight - diff;
                $view.find('.yfs-body').height(tempheight);

            }

            function setUnits() {
                febs.getSyn(ctx + 'basic/basicUnitItemDict/all', {}, function (e) {
                    if (e.code == 200) {
                        units = e.data
                    }
                })
            }

            function getRegions(shipTypeId) {
                let arr = []
                febs.getSyn(ctx + 'basic/shipTypeArea/all', { shipTypeId: shipTypeId }, function (e) {
                    if (e.code == 200) {
                        $.each(e.data, function (i, v) {
                            regionsMap.set(v.strId, v.strCodeName);
                            arr.push({
                                name: v.strCodeName,
                                value: v.strId,
                                showname: v.strCode
                            })
                        })
                    }
                })
                return arr
            }

            function getProfessions() {
                febs.getSyn(ctx + 'plan/produceProfessionTypeDict/tree', {}, function (e) {
                    if (e.code == 200) {
                        proData = e.data
                        $.each(e.data, function (i, v) {
                            $.each(v.children, function (index, child) {
                                let name = child.name;
                                let desc = child.desc;
                                let str = name.replaceAll(desc, "").replaceAll(" ", "");
                                proMap.set(parseInt(child.value), str);
                            })
                        })

                    }
                })
                febs.getSyn(ctx + 'plan/detailDesignProfessionTypeDict', null, function (data) {// 详设
                    if (data.code == 200) {
                        $.each(data.data, function (i, v) {
                            detailProMap.set(v.ddptId, v.ddptCodeName);
                            detailArr.push({
                                name: v.ddptCodeName,
                                value: v.ddptId,
                                showname: v.ddptCode
                            })
                        })
                    }
                })
            }

            function setProType() {
                let proArr = [{ name: '详细设计专业', value: '0' }, { name: '生产设计专业', value: '1' }]
                proTypeList = xmSelect.render({
                    el: '#proTypeList',
                    data: proArr,
                    filterable: true,
                    template({ item }) {
                        return item.name
                    },
                    radio: true,
                    clickClose: true,
                    on: function (data) {
                        let isAdd = data.isAdd;
                        if (!isAdd) {
                            professions = xmSelect.render({
                                el: '#professions',
                                data: [],
                                filterable: true,
                                template({ item }) {
                                    return item.name + '<span class="shipNoText" title="' + item.showname + '">' + item.showname + '</span>'
                                },
                                radio: true,
                                clickClose: true,
                            })
                            return false;
                        }
                        if (data.change[0].value == 0) {
                            professions = xmSelect.render({
                                el: '#professions',
                                data: detailArr,
                                filterable: true,
                                template({ item }) {
                                    return item.name + '<span class="shipNoText" title="' + item.showname + '">' + item.showname + '</span>'
                                },
                                radio: true,
                                clickClose: true,
                            })
                        } else {
                            professions = xmSelect.render({
                                el: '#professions',
                                data: proData,
                                radio: true,
                                clickClose: true,
                                filterable: true,
                                height: '150px',
                                tree: {
                                    show: true,
                                    strict: false,

                                },
                                template({ item }) {
                                    return item.name + '<span style="position: absolute;right: 10px;color: #90a4ae">' + item.desc + '</span>'
                                },
                                // iconfont : {
                                //     parent : 'hidden' //隐藏父节点图标
                                // },
                            })
                        }
                    }
                })

                proTypeListTransfer = xmSelect.render({
                    el: '#proTypeListTransfer',
                    data: proArr,
                    filterable: true,
                    template({ item }) {
                        return item.name
                    },
                    radio: true,
                    clickClose: true,
                    on: function (data) {
                        let isAdd = data.isAdd;
                        if (!isAdd) {
                            professionsTransfer = xmSelect.render({
                                el: '#professionsTransfer',
                                data: [],
                                filterable: true,
                                template({ item }) {
                                    return item.name + '<span class="shipNoText" title="' + item.showname + '">' + item.showname + '</span>'
                                },
                                radio: true,
                                clickClose: true,
                            })
                            return false;
                        }
                        if (data.change[0].value == 0) {
                            professionsTransfer = xmSelect.render({
                                el: '#professionsTransfer',
                                data: detailArr,
                                filterable: true,
                                template({ item }) {
                                    return item.name + '<span class="shipNoText" title="' + item.showname + '">' + item.showname + '</span>'
                                },
                                radio: true,
                                clickClose: true,
                            })
                        } else {
                            professionsTransfer = xmSelect.render({
                                el: '#professionsTransfer',
                                data: proData,
                                radio: true,
                                clickClose: true,
                                filterable: true,
                                height: '150px',
                                tree: {
                                    show: true,
                                    strict: false,

                                },
                                template({ item }) {
                                    return item.name + '<span style="position: absolute;right: 10px;color: #90a4ae">' + item.desc + '</span>'
                                },
                                // iconfont : {
                                //     parent : 'hidden' //隐藏父节点图标
                                // },
                            })
                        }
                    }
                })
            }

            function setRegions(arr) {
                regions = xmSelect.render({
                    el: '#regions',
                    data: arr,
                    filterable: true,
                    tips: arr.length === 0 ? "请先选择船号" : "请选择",
                    template({ item }) {
                        return item.name + '<span class="shipNoText" title="' + item.showname + '">' + item.showname + '</span>'
                    },
                    radio: true,
                    clickClose: true,
                })
            }

            function setProfessions() {
                professions = xmSelect.render({
                    el: '#professions',
                    data: [],
                    filterable: true,
                    tips: "请选择",
                    template({ item }) {
                        return item.name + '<span class="shipNoText" title="' + item.showname + '">' + item.showname + '</span>'
                    },
                    radio: true,
                    clickClose: true,
                })
                if (localStorage.getItem("shipMaterialPredictAndPorShipNo", proIds) != null) {
                    proIds = localStorage.getItem("shipMaterialPredictAndPorShipNo", proIds)
                    professions.setValue(proIds.split(','))
                }

            }

            //船号选择
            function setShipNo() {
                let arr = [];
                shipArr = [];
                let resp = rpcJs.getShipDataList();
                if (resp.code == 200) {
                    allShips = resp.data;
                    $.each(resp.data, function (i, v) {
                        shipArr.push({
                            name: v.shipNo,
                            value: v.shipId,
                            showname: v.showName,
                            typeId: v.typeId
                        })
                    })
                }
                shipNoSelect = xmSelect.render({
                    el: '#shipNoSelect',
                    data: shipArr,
                    filterable: true,
                    template({ item }) {
                        return item.name + '<span class="shipNoText" title="' + item.showname + '">' + item.showname + '</span>'
                    },
                    radio: true,
                    clickClose: true,
                    model: {
                        label: {
                            block: {
                                template(item, sels) {
                                    return item.name + '(' + item.showname + ')'
                                }
                            }
                        }
                    },
                    on: function (data) {
                        if (data.arr[0] != undefined) {
                            selectedShipId = data.arr[0].value;
                            shipTypeId = data.arr[0].typeId

                            // 物资移库有数据时只允许选择单船 提示
                            if (null != localStorage.getItem('porDeviceInfos') && selectedShipId != JSON.parse(localStorage.getItem('porDeviceInfos'))[0].porShipId) {
                                febs.modal.confirm("注意", "当前船：" + data.arr[0].name + "还存在物资移库数据未提交，请确认是否切换船号", function () {
                                    let arr = getRegions(shipTypeId)
                                    setRegions(arr)
                                    // 切换船号 删除缓存
                                    localStorage.removeItem('porDeviceInfos')
                                }, function () {
                                    shipNoSelect.setValue([JSON.parse(localStorage.getItem('porDeviceInfos'))[0].porShipId])
                                    let arr = getRegions(shipNoSelect.getValue()[0].typeId)
                                    setRegions(arr)
                                })
                            } else {
                                let arr = getRegions(shipTypeId)
                                setRegions(arr)
                            }

                        }
                    }
                })
                if (localStorage.getItem("shipMaterialPredictAndPorShipNo", shipIds) != null) {
                    shipIds = localStorage.getItem("shipMaterialPredictAndPorShipNo", shipIds)
                    shipNoSelect.setValue(shipIds.split(','))
                }
                // porShipNoSelect = xmSelect.render({
                //     el: '#porShipNoSelect',
                //     data: shipArr,
                //     filterable: true,
                //     template({item}) {
                //         return item.name + '<span class="shipNoText" title="' + item.showname + '">' + item.showname + '</span>'
                //     },
                //     radio: true,
                //     clickClose: true,
                //     model: {
                //         label: {
                //             block: {
                //                 template(item, sels) {
                //                     return item.name + '(' + item.showname + ')'
                //                 }
                //             }
                //         }
                //     },
                //     on: function (data) {
                //     }
                // })
                dstShipNoSelect = xmSelect.render({
                    el: '#dstShipNoSelect',
                    data: shipArr,
                    filterable: true,
                    template({ item }) {
                        return item.name + '<span class="shipNoText" title="' + item.showname + '">' + item.showname + '</span>'
                    },
                    radio: true,
                    clickClose: true,
                    model: {
                        label: {
                            block: {
                                template(item, sels) {
                                    return item.name + '(' + item.showname + ')'
                                }
                            }
                        }
                    },
                    on: function (data) {
                    }
                })
            }

            // Head查询按钮
            $view.find('#headQuery').on('click', function () {
                linePhdpId = null;
                linePhDesc = null;
                porId = null;
                porType = null;
                let params = getHeadQueryParams();
                if (!params.shipId) {
                    febs.alert.warn("请选择对应船号")
                } else {
                    tableIns.reload({
                        where: getHeadQueryParams(),
                        url: ctx + 'por/porHeadDeviceProtocol/page',
                        // page: {curr: 1}
                    });
                    if (params.basicMaterialDesc || params.basicMaterialDesc !== "" || params.basicMaterialNo || params.basicMaterialNo !== "") {
                        tableDetailIns.reload({
                            where: getHeadQueryParams(),
                            url: ctx + 'por/porLineDeviceProtocol/page',
                            // page: {curr: 1}
                        });
                    } else {
                        createTable2([])
                    }
                    febs.get(ctx + 'por/previousMaterialApplyDetailInfo/getPreviousMaterialApplyDetailInfoList', getPreviousQueryParams(), function (e) {
                        const newData = e.data ? e.data : [];
                        previousTableIns.reload({ data: newData });
                    });
                }
            });

            // 更多查询点击事件
            $view.find('#moreQuery').on('click', function () {
                if ($view.find("#moreQueryDiv").hasClass("layui-hide")) {
                    $view.find("#moreQueryDiv").removeClass("layui-hide");
                } else {
                    $view.find("#moreQueryDiv").addClass("layui-hide");
                }
            });

            // 移库查询按钮点击事件
            $view.find('#previousQuery').on('click', function () {
                febs.get(ctx + 'por/previousMaterialApplyDetailInfo/getPreviousMaterialApplyDetailInfoList', getPreviousQueryParams2(), function (e) {
                    const newData = e.data ? e.data : [];
                    previousTableIns.reload({ data: newData });
                });
            });

            //Head查询条件
            function getHeadQueryParams() {
                // let ssIds = [];
                // let xsIds = [];
                // $.each(ssProfessionUserList,function (i,v) {
                //     ssIds.push(v.professionId);
                // })
                // $.each(xsProfessionUserList,function (i,v) {
                //     xsIds.push(v.professionId);
                // })
                if ($view.find('#codeType').val() != '') {
                    // if ($view.find('input[name= basicMaterialNo]').val() !== '' || $view.find('input[name= basicMaterialDesc]').val() !== ''){
                    //     return {
                    //         shipId: shipNoSelect.getValue('value').length === 0 ? null:shipNoSelect.getValue('value')[0],
                    //         basicMaterialDesc: $view.find('input[name= basicMaterialDesc]').val(),
                    //         basicMaterialNo: $view.find('input[name= basicMaterialNo]').val(),
                    //         // ssProfessionUserList: ssIds,
                    //         // xsProfessionUserList: xsIds
                    //     }
                    // }else {
                    //     return {
                    //         shipId: shipNoSelect.getValue('value').length === 0 ? null:shipNoSelect.getValue('value')[0],
                    //         porHeadNo : $view.find('input[name = porHeadNo]').val(),
                    //         directorName : $view.find('input[name = directorName]').val(),
                    //         status: $view.find('#codeType').val(),
                    //         sapStatus: $view.find('#sapStatus').val(),
                    //         basicMaterialDesc: '',
                    //         basicMaterialNo: '',
                    //         strId: regions.getValue('value').length === 0 ? null:regions.getValue('value')[0],
                    //         proId: professions.getValue('value').length === 0 ? null:professions.getValue('value')[0],
                    //         proType: proTypeList.getValue('value').length === 0 ? null:proTypeList.getValue('value')[0],
                    //         // ssProfessionUserList: ssIds,
                    //         // xsProfessionUserList: xsIds
                    //     };
                    // }
                    return {
                        shipId: shipNoSelect.getValue('value').length === 0 ? null : shipNoSelect.getValue('value')[0],
                        porHeadNo: $view.find('input[name = porHeadNo]').val(),
                        directorName: $view.find('input[name = directorName]').val(),
                        status: $view.find('#codeType').val(),
                        sapStatus: $view.find('#sapStatus').val(),
                        basicMaterialDesc: $view.find('input[name= basicMaterialDesc]').val(),
                        basicMaterialNo: $view.find('input[name= basicMaterialNo]').val(),
                        strId: regions.getValue('value').length === 0 ? null : regions.getValue('value')[0],
                        proId: professions.getValue('value').length === 0 ? null : professions.getValue('value')[0],
                        proType: proTypeList.getValue('value').length === 0 ? null : proTypeList.getValue('value')[0],
                        // ssProfessionUserList: ssIds,
                        // xsProfessionUserList: xsIds
                    };
                } else {
                    // if ($view.find('input[name= basicMaterialNo]').val() !== '' || $view.find('input[name= basicMaterialDesc]').val() !== ''){
                    //     return {
                    //         shipId: shipNoSelect.getValue('value').length === 0 ? null:shipNoSelect.getValue('value')[0],
                    //         basicMaterialDesc: $view.find('input[name= basicMaterialDesc]').val(),
                    //         basicMaterialNo: $view.find('input[name= basicMaterialNo]').val(),
                    //         // ssProfessionUserList: ssIds,
                    //         // xsProfessionUserList: xsIds
                    //     }
                    // }else {
                    //     return {
                    //         shipId: shipNoSelect.getValue('value').length === 0 ? null:shipNoSelect.getValue('value')[0],
                    //         porHeadNo : $view.find('input[name = porHeadNo]').val(),
                    //         directorName : $view.find('input[name = directorName]').val(),
                    //         sapStatus: $view.find('#sapStatus').val(),
                    //         basicMaterialDesc: '',
                    //         basicMaterialNo: '',
                    //         strId: regions.getValue('value').length === 0 ? null:regions.getValue('value')[0],
                    //         proId: professions.getValue('value').length === 0 ? null:professions.getValue('value')[0],
                    //         proType: proTypeList.getValue('value').length === 0 ? null:proTypeList.getValue('value')[0],
                    //         // ssProfessionUserList: ssIds,
                    //         // xsProfessionUserList: xsIds
                    //     };
                    // }
                    return {
                        shipId: shipNoSelect.getValue('value').length === 0 ? null : shipNoSelect.getValue('value')[0],
                        porHeadNo: $view.find('input[name = porHeadNo]').val(),
                        directorName: $view.find('input[name = directorName]').val(),
                        sapStatus: $view.find('#sapStatus').val(),
                        basicMaterialDesc: $view.find('input[name= basicMaterialDesc]').val(),
                        basicMaterialNo: $view.find('input[name= basicMaterialNo]').val(),
                        strId: regions.getValue('value').length === 0 ? null : regions.getValue('value')[0],
                        proId: professions.getValue('value').length === 0 ? null : professions.getValue('value')[0],
                        proType: proTypeList.getValue('value').length === 0 ? null : proTypeList.getValue('value')[0],
                        // ssProfessionUserList: ssIds,
                        // xsProfessionUserList: xsIds
                    };
                }
            }

            // 移库查询条件
            function getPreviousQueryParams() {
                return {
                    dstShipId: shipNoSelect.getValue('value').length === 0 ? null : shipNoSelect.getValue('value')[0],
                    proType: proTypeList.getValue('value').length === 0 ? null : proTypeList.getValue('value')[0],
                    principalUserName: $view.find('input[name = directorName]').val(),
                    strIds: regions.getValue('value').length === 0 ? null : regions.getValue('value')[0],
                    porHeadNo: $view.find('input[name = porHeadNo]').val(),
                    porUploadStatus: $view.find('#codeType').val(),
                    basicMaterialNo: $view.find('input[name= basicMaterialNo]').val(),
                    basicMaterialDesc: $view.find('input[name= basicMaterialDesc]').val(),
                    professionId: professions.getValue('value').length === 0 ? null : professions.getValue('value')[0],
                };
            }

            // 下方移库查询条件
            function getPreviousQueryParams2() {
                return {
                    dstShipId: shipNoSelect.getValue('value').length === 0 ? null : shipNoSelect.getValue('value')[0],
                    proType: proTypeList.getValue('value').length === 0 ? null : proTypeList.getValue('value')[0],
                    principalUserName: $view.find('input[name = directorName]').val(),
                    strIds: regions.getValue('value').length === 0 ? null : regions.getValue('value')[0],
                    porHeadNo: $view.find('input[name = porHeadNo]').val(),
                    porUploadStatus: $view.find('#codeType').val(),
                    basicMaterialNo: $view.find('#basicMaterialNoPrevious').val(),
                    basicMaterialDesc: $view.find('#basicMaterialDescPrevious').val(),
                    professionId: professions.getValue('value').length === 0 ? null : professions.getValue('value')[0],
                };
            }

            //Line查询按钮
            $view.find('#lineQuery').on('click', function () {
                if (getLineQueryParams().phdpId !== undefined) {
                    tableDetailIns.reload({
                        where: getLineQueryParams(),
                        url: ctx + 'por/porLineDeviceProtocol/page',
                        // page: {curr: 1}
                    });
                } else {
                    febs.alert.warn("请选择对应POR_HEAD");
                }
            })

            //Line查询条件
            function getLineQueryParams() {
                return {
                    phdpId: linePhdpId,
                    basicMaterialNo: $view.find('input[name = basicMaterialNoLine]').val(),
                    basicMaterialDesc: $view.find('input[name = basicMaterialDescLine]').val(),
                };
            }

            function initTable() {
                localStorage.removeItem('porDeviceInfos');
                createTable([]);
                createTable2([]);
                // 初始化移库表格
                createTable3();
            }

            //创建上方表格
            function createTable(data) {
                tableIns = febs.table.init({
                    elem: $view.find('#protocolList'),
                    id: 'protocolList',
                    data: data,
                    autoSort: true,
                    sort: true,
                    toolbar: '#deviceProtocolListMangerToolbar',
                    defaultToolbar: [],
                    height: $view.find("#mainTable").height(),
                    cols: [
                        [
                            { fixed: 'left', type: 'checkbox' },
                            {
                                fixed: 'left',
                                field: 'porHeadNo',
                                title: 'POR编号',
                                align: 'center',
                                width: 90,
                                sort: true,
                                templet: function (d) {
                                    return '<div style="text-align: left">' + d.porHeadNo + '</div>'
                                }
                            },
                            {
                                fixed: 'left',
                                field: 'porHeadDesc',
                                title: '<span style="color: #1b83f0;font-weight: bold">POR描述</span>',
                                width: 150,
                                align: 'center',
                                edit: function (d) {
                                    if (d.sapStatus == 0) {
                                        if (!permiFlag) {
                                            return null;
                                        }
                                        return "text"
                                    }
                                },
                                templet: function (d) {
                                    return '<div style="text-align: left">' + d.porHeadDesc + '</div>'
                                }
                            },
                            { field: 'l', title: 'L', minWidth: 30, align: 'center' },
                            {
                                field: 'proId', title: 'POR专业', width: 88, align: 'center', templet: function (d) {
                                    if (d.proType == "0") {
                                        //详细设计专业
                                        return detailProMap.get(d.proId) == null ? '未知' : '详设-' + detailProMap.get(d.proId);
                                    } else if (d.proType == "1") {
                                        //生产设计专业
                                        return proMap.get(d.proId) == null ? '未知' : '生设-' + proMap.get(d.proId);
                                    } else {
                                        return "未知";
                                    }
                                }
                            },
                            {
                                field: 'strId', title: '船型区域', width: 82, align: 'center', templet: function (d) {
                                    return regionsMap.get(d.strId) == null ? '未知' : regionsMap.get(d.strId);
                                }
                            },
                            { field: 'deptName', title: 'POR部门', width: 95, align: 'center' },
                            { field: 'directorName', title: 'POR负责人', width: 95, align: 'center' },
                            { field: 'phoneNumber', title: '电话', minWidth: 90, align: 'center' },
                            {
                                field: 'produceDate',
                                title: '生产需求日',
                                sort: true,
                                minWidth: 100,
                                align: 'center',
                                templet: function (d) {
                                    if (d.produceDate != null) {
                                        return commonJS.formatDate(new Date(d.produceDate), 'yyyy-MM-dd')
                                    } else {
                                        return ''
                                    }
                                }
                            },
                            {
                                field: 'protocolDate',
                                title: '计划发放日',
                                sort: true,
                                minWidth: 100,
                                align: 'center',
                                templet: function (d) {
                                    if (d.protocolDate != null) {
                                        return commonJS.formatDate(new Date(d.protocolDate), 'yyyy-MM-dd')
                                    } else {
                                        return ''
                                    }
                                }
                            },
                            { field: 'statusStr', title: 'POR状态', minWidth: 75, align: 'center' },
                            { field: 'sapStatusStr', title: 'SAP状态', minWidth: 80, align: 'center' },
                            {
                                field: 'uploadDate',
                                title: '实际发放日',
                                sort: true,
                                minWidth: 100,
                                align: 'center',
                                templet: function (d) {
                                    if (d.uploadDate != null) {
                                        return commonJS.formatDate(new Date(d.uploadDate), 'yyyy-MM-dd')
                                    } else {
                                        return ''
                                    }
                                }

                            },
                            {
                                field: 'remark',
                                title: '<span style="color: #1b83f0;font-weight: bold">备注</span>',
                                width: 80,
                                align: 'center',
                                edit: function (d) {
                                    if (d.sapStatus == 0) {
                                        if (!permiFlag) {
                                            return null;
                                        }
                                        return "text"
                                    }
                                },
                                templet: function (d) {
                                    return '<div style="text-align: left">' + (d.remark != null ? d.remark : "") + '</div>'
                                }
                            },
                            {
                                field: 'attachmentFlg',
                                title: '附图标识',
                                width: 60,
                                align: 'center',
                                templet: function (d) {
                                    if (d.attachmentFlg == 1) {
                                        //是
                                        return `<span class="layui-badge" style="background-color: #4dbeba">是</span>`
                                    } else {
                                        //否
                                        return `<span class="layui-badge" style="background-color: #e7d9cc">否</span>`
                                    }


                                }
                            },
                            {
                                fixed: 'right', title: '操作', width: 95, align: 'center', templet: function (d) {
                                    if (d.sapStatus == '2') {
                                        return '    <div class="layui-clear-space">\n' +
                                            // '        <a class="layui-btn layui-btn-xs layui-bg-lime" lay-event="view">查看</a>\n' +
                                            '        <a class="layui-btn layui-btn-xs layui-bg-orange" lay-event="uploadView" >协议单</a>\n' +
                                            '    </div>'
                                    } else {
                                        if (uploadFlag) {
                                            let haveEditShiro = false;
                                            if (d.proType == '0') {// 详设
                                                $.each(xsProfessionUserList, function (i, dd) {
                                                    if (d.proId == dd.professionId) {
                                                        haveEditShiro = true;
                                                    }
                                                })
                                            } else {// 生设
                                                $.each(ssProfessionUserList, function (i, dd) {
                                                    if (d.proId == dd.professionId) {
                                                        haveEditShiro = true;
                                                    }
                                                })
                                            }
                                            if ((null == ssProfessionUserList || ssProfessionUserList.length == 0) && (null == xsProfessionUserList || xsProfessionUserList.length == 0)) {
                                                haveEditShiro = true;
                                            }
                                            if (!haveEditShiro) {
                                                return '    <div class="layui-clear-space">\n' +
                                                    // '        <a class="layui-btn layui-btn-xs layui-bg-lime" lay-event="view">查看</a>\n' +
                                                    '        <a class="layui-btn layui-btn-xs layui-bg-orange" lay-event="uploadView" >协议单</a>\n' +
                                                    '    </div>'
                                            }
                                            return '    <div class="layui-clear-space">\n' +
                                                // '        <a class="layui-btn layui-btn-xs layui-bg-lime" lay-event="view">查看</a>\n' +
                                                // '        <a class="layui-btn layui-btn-xs blueBtm" lay-event="save">保存</a>\n' +
                                                '        <a class="layui-btn layui-btn-xs layui-bg-orange" lay-event="upload" >协议单上传</a>\n' +
                                                '    </div>'
                                        } else {
                                            return '    <div class="layui-clear-space">\n' +
                                                // '        <a class="layui-btn layui-btn-xs layui-bg-lime" lay-event="view">查看</a>\n' +
                                                // '        <a class="layui-btn layui-btn-xs blueBtm" lay-event="save">保存</a>\n' +
                                                '        <a class="layui-btn layui-btn-xs lay-event="upload"" >协议单上传</a>\n' +
                                                '    </div>'
                                        }
                                    }
                                }
                            },
                        ]
                    ],
                    page: false,
                    done: function (res, curr, count, origin) {
                        $view.find('#headTotalCount').text('检索 ' + count + ' 条数据')
                        $.each(res.data, function (i, val) {
                            if (val.proType == '0') {// 详设
                                $.each(xsProfessionUserList, function (i, d) {
                                    if (val.proId == d.professionId) {
                                        xsorssMap.set(val.proType + "@" + val.proId, true);
                                    }
                                })
                            } else {// 生设
                                $.each(ssProfessionUserList, function (i, d) {
                                    if (val.proId == d.professionId) {
                                        xsorssMap.set(val.proType + "@" + val.proId, true);
                                    }
                                })
                            }
                            proNoToIdMap.set(val.porHeadNo, val.proId);
                            proNoToTypeMap.set(val.porHeadNo, val.proType);
                            if (val.status == '1') {
                                $('tr[data-index="' + i + '"]').addClass('updateColorClass');
                            }
                        })
                    }
                    // limits: [10,20,30, 50, 100],
                    // limit: 10
                })
            }

            table.on('sort(porHeadMaterialTable)', function (d) {
                let param = d.config.where;
                let serverUrl = d.config.url;
                param.order = d.type;
                param.orderField = d.field;
                let shipId = shipNoSelect.getValue('valueStr');
                if (shipId === '') {
                    febs.alert.warn('请选择船号');
                    return false;
                } else {
                    let shipId = shipNoSelect.getValue('value')[0];
                    tableIns.reload(
                        {
                            url: serverUrl,
                            where: param
                        }
                        // ,
                        // page: {curr: 1}}
                    );

                }
            })


            table.on('row(porHeadMaterialTable)', function (d) {
                let tr = d.tr;
                $view.find('.viewBgColor').removeClass('viewBgColor')
                tr.removeClass('viewBgColor')
                tr.addClass('viewBgColor')
                headStatus = d.data.sapStatus;
                let params = getHeadQueryParams();
                params.phdpId = d.data.phdpId;
                linePhdpId = d.data.phdpId;
                headUploadStatus = d.data.status;
                linePhDesc = d.data.porHeadDesc;
                porId = d.data.proId;
                porType = d.data.proType;
                if (xsorssMap.size == 0) {
                    onRowHaveEditShiro = true;
                } else {
                    onRowHaveEditShiro = xsorssMap.get(d.data.proType + "@" + d.data.proId) != true ? false : true;
                }
                tableDetailIns.reload({
                    where: params,
                    url: ctx + 'por/porLineDeviceProtocol/page',
                    // page:{curr:1}
                })

            })

            table.on('edit(porHeadMaterialTable)', function (d) {
                let data = d.data; // 获得当前行数据
                data.produceDate = null; //后台转换报错
                data.protocolDate = null;//后台转换报错
                data.uploadDate = null;//后台转换报错
                let haveEditShiro = false;
                if (xsorssMap.size == 0) {
                    haveEditShiro = true;
                } else {
                    haveEditShiro = xsorssMap.get(data.proType + "@" + data.proId) != true ? false : true;
                }
                if (!haveEditShiro) {
                    var field = d.field;
                    var oldValue = d.oldValue;
                    var update = {};
                    update[field] = oldValue;
                    d.update(update, true)
                    layer.tips('当前数据您没有操作权限', this, { tips: 1 });
                } else {
                    febs.postArray(ctx + 'por/porHeadDeviceProtocol/update', data, function (da) {
                        if (da.code != 200) {
                            febs.alert.fail('修改失败');
                        }
                        $(d.tr[0]).removeClass('edited')
                    })
                }
            })

            //创建下方表格
            function createTable2(data) {
                tableDetailIns = febs.table.init({
                    elem: $view.find('#protocolLineList'),
                    id: 'protocolLineList',
                    // toolbar: '#lineToolbar',
                    defaultToolbar: [],
                    data: data,
                    height: $view.find("#detail").height(),
                    cols: [
                        [
                            { fixed: 'left', type: 'checkbox' },
                            {
                                field: 'porHeadNo',
                                fixed: 'left',
                                title: 'POR编号',
                                align: 'center',
                                width: 90,
                                templet: function (d) {
                                    return '<div style="text-align: left">' + d.porHeadNo + '</div>'
                                }
                            },
                            { field: 'lineNo', fixed: 'left', title: '行项号', align: 'center', width: 60, sort: true },
                            {
                                field: 'basicMaterialNo',
                                fixed: 'left',
                                title: '物资编码',
                                align: 'center',
                                width: 160,
                                templet(d) {
                                    return '<div style="text-align: left;">' + d.basicMaterialNo + '</div>'
                                }
                            },
                            {
                                field: 'materialDesc',
                                fixed: 'left',
                                title: '名称及规格',
                                width: 250,
                                align: 'center',
                                edit: function (d) {
                                    if (d.sapStatus != '1' && d.type == 1) {
                                        return 'text'
                                    }
                                },
                                templet(d) {
                                    return '<div style="text-align: left;">' + d.materialDesc + '</div>'
                                }
                            },
                            {
                                field: 'weightUnit', title: '单位', width: 110, align: 'center', templet: function (d) {
                                    if (d.type == 1) {
                                        return '<div id="XM-' + d.pldpId + '" class="por-weight"></div>'
                                    } else {
                                        return d.porUnitItemCode ? d.porUnitItemCode : ""
                                    }
                                }
                            },
                            {
                                field: 'weight', title: '单位重量', width: 100, align: 'center', edit: function (d) {
                                    if (d.sapStatus != '1' && d.type == 1) {
                                        return 'text'
                                    }
                                }
                            },
                            {
                                field: 'amountDb',
                                title: '<span lay-event="showAppendix" style="color: #1b83f0;font-weight: bold">POR数量</span>',
                                width: 90,
                                align: 'center',
                                edit: function (d) {
                                    if (d.sapStatus != '1') {
                                        if (!permiFlag || d.appendixFlg == '1') {
                                            return null;
                                        }
                                        return 'text'
                                    }
                                },
                                templet: function (d) {
                                    if (d.appendixFlg == '0') {
                                        if (d.useAmount == 0) {
                                            return '<span lay-event="showAppendix" style="text-decoration: underline;">' + (d.amountDb) + '</span>'
                                        } else {
                                            return d.amountDb
                                        }
                                    } else {
                                        return '<span lay-event="showAppendix" style="text-decoration: underline;">' + (d.amountDb) + '</span>'
                                    }

                                }
                            },
                            {
                                field: 'appendixFlg',
                                title: '附件号标记',
                                align: 'center',
                                width: 80,
                                minWidth: 70,
                                templet: function (d) {
                                    if (d.appendixFlg == 1) {
                                        return `<span class="layui-badge" style="background-color: #4dbeba" >是</span>`
                                    } else {
                                        return `<span class="layui-badge" style="background-color: #e7d9cc">否</span>`

                                    }
                                }
                            },
                            { field: 'uploadStatusStr', title: '上传状态', minWidth: 80, align: 'center' },
                            { field: 'sapStatusStr', title: 'SAP状态', minWidth: 80, align: 'center' },
                            { field: 'purchaseStatus', title: '订单采购状态', minWidth: 110, align: 'center' },
                            { field: 'purchaseNo', title: '订单采购编号', minWidth: 110, align: 'center' },
                            { field: 'purchaseLineNo', title: '订单采购行项号', minWidth: 130, align: 'center' },
                            { field: 'purchasePerson', title: '订单采购人', minWidth: 110, align: 'center' },
                            { field: 'companyName', title: '中标厂家', minWidth: 100, align: 'center' },
                            { field: 'awardTime', title: '中标时间', minWidth: 100, align: 'center' },
                            {
                                field: 'remark',
                                title: '<span style="color: #1b83f0;font-weight: bold">备注</span>',
                                width: 100,
                                align: 'center',
                                edit: function (d) {
                                    if (d.sapStatus != '1') {
                                        if (!permiFlag) {
                                            return null;
                                        }
                                        return 'text'
                                    }
                                }
                            },
                            { field: 'purchaseAmount', title: '订货数量', width: 80, align: 'center' },
                            { field: 'stockAmount', title: '入库数量', width: 80, align: 'center' },
                            {
                                field: 'useAmount',
                                title: '使用数量(PML)',
                                style: 'color:#1b83f0;cursor:pointer',
                                width: 130,
                                align: 'center',
                                templet: function (d) {
                                    return '<span lay-event="detailTray" style="text-decoration: underline;">' + (d.useAmount) + '</span>'
                                }
                            },
                            {
                                title: '使用剩余数量', width: 110, align: 'center', templet: function (d) {
                                    if (null != d.amountDb && Number(d.amountDb) > 0) {
                                        //总数 - 使用数量 = 剩余数量
                                        return Number(d.amountDb) - Number(d.useAmount) - Number(d.restAmount) - Number(d.previousAmount);
                                    } else {
                                        return 0;
                                    }
                                }
                            },
                            {
                                field: 'restAmount', title: '替代物资数量', width: 90, align: 'center',
                                templet: function (d) {
                                    if (d.appendixFlg == '1') {
                                        return '<span lay-event="showAppendixBdNum" style="text-decoration: underline;">' + (d.restAmount) + '</span>'
                                    } else {
                                        return d.restAmount
                                    }
                                }
                            },
                            { field: 'previousAmount', title: '移库数量', width: 90, align: 'center' },
                        ]
                    ],
                    page: false,
                    // limits: [10,20,30, 50, 100],
                    // limit: 10,
                    done: function (res, curr, count) {
                        $view.find('#lineTotalCount').text('检索 ' + count + ' 条数据')
                        let disable;
                        if (headStatus == 1) {
                            disable = true
                        } else {
                            disable = false
                        }
                        var cells = document.querySelectorAll('div[lay-id="protocolLineList"] .layui-table-cell');
                        cells.forEach(item => {
                            item.style.height = '35px';
                        })
                        var xmCells = document.querySelectorAll('div[lay-id="protocolLineList"] .por-weight');
                        xmCells.forEach(item => {
                            item.parentElement.style.overflow = 'visible';
                        })
                        res.data.forEach(t => {
                            if (t.type == 1) {
                                let arr = [];
                                $.each(units, function (i, v) {
                                    if (t.weightUnit == v.id) {
                                        arr.push({
                                            name: v.unitItemCode + ' ' + v.unitItemName,
                                            value: v.id,
                                            selected: true
                                        })
                                    } else {
                                        arr.push({
                                            name: v.unitItemCode + ' ' + v.unitItemName,
                                            value: v.id,
                                        })
                                    }

                                })
                                unitSelect = xmSelect.render({
                                    el: '#XM-' + t.pldpId,
                                    radio: true,
                                    clickClose: true,
                                    direction: 'auto',
                                    height: '100px',
                                    disabled: disable,
                                    filterable: true,
                                    model: {
                                        label: { type: 'text' }
                                    },
                                    data: arr,
                                    template({ item }) {
                                        return item.name
                                    },
                                    on: function (data) {
                                        t.weightUnit = data.change[0].value
                                        let index = $view.find('#XM-' + t.pldpId).closest('tr').data('index');
                                        table.setRowChecked('protocolLineList', {
                                            index: index,
                                            checked: true
                                        })
                                    }
                                })
                            }
                        })
                        var options = this;
                        // 获取当前行数据
                        table.getRowData = function (tableId, elem) {
                            var index = $(elem).closest('tr').data('index');
                            return table.cache[tableId][index] || {};
                        };
                        // 原生 select 事件
                        var tableViewElem = this.elem.next();
                        tableViewElem.find('.select-demo-primary').on('change', function () {
                            $(this).closest('tr').addClass('edited')
                            let index2 = $(this).closest('tr').data('index');
                            table.setRowChecked('protocolLineList', {
                                index: index2,
                                checked: true
                            })
                            var value = this.value; // 获取选中项 value
                            var data = table.getRowData(options.id, this); // 获取当前行数据(如 id 等字段，以作为数据修改的索引)
                            // 更新数据中对应的字段
                            data.weightUnit = value;
                        });
                    }
                })
            }

            // 初始化移库表格
            function createTable3() {
                previousTableIns = febs.table.init({
                    elem: $view.find('#previousShipMaterialTable'),
                    height: $view.find("#previousShipMaterialDiv").height(),
                    id: 'previousShipMaterialTable',
                    css: [ // 重设当前表格样式
                        '.layui-table-tool-temp{padding-right: 145px;}'
                    ].join(''),
                    cellMinWidth: 80,
                    data: [],
                    cols: [[
                        { type: 'checkbox' },
                        { field: 'pmadiId', hide: true },
                        { field: 'porShipNo', title: '当前船', align: 'center', minWidth: 120 },
                        { field: 'applyNo', title: '移库单', align: 'center', minWidth: 120 },
                        {
                            field: 'uploadStatus',
                            title: '移库状态',
                            align: 'center',
                            minWidth: 120,
                            templet: function (d) {
                                if (d.uploadStatus) {
                                    if (String(d.uploadStatus) === '0') {
                                        return '未移库';
                                    } else if (String(d.uploadStatus) === '1') {
                                        return '已移库';
                                    } else if (String(d.uploadStatus) === '2') {
                                        return '移库失败';
                                    }
                                } else {
                                    return '';
                                }
                            }
                        },
                        { field: 'dstShipNo', title: '目标船', align: 'center', minWidth: 120 },
                        { field: 'porHeadNo', title: 'porNo', align: 'center', minWidth: 120 },
                        { field: 'porHeadDesc', title: 'por描述', align: 'center', minWidth: 120 },
                        { field: 'pptdCodeName', title: 'por专业', align: 'center', minWidth: 120 },
                        { field: 'lineNo', title: 'LineNo', align: 'center', minWidth: 120 },
                        { field: 'basicMaterialNo', title: '物资编码', align: 'center', minWidth: 120 },
                        { field: 'basicMaterialDesc', title: '物资描述', align: 'center', minWidth: 120 },
                        { field: 'amount', title: '转移数量', align: 'center', minWidth: 120 },
                        {
                            field: 'useAmount', title: '使用数量', align: 'center', minWidth: 120, templet: function (d) {
                                if (d.useAmount != null && d.useAmount !== '' && d.useAmount !== undefined && d.useAmount > 0) {
                                    return `<span style="text-decoration: underline;" lay-event="trayUseDetail">${d.useAmount}</span>`;
                                } else {
                                    return '0';
                                }
                            }
                        },
                        { field: 'principalUserName', title: '负责人', align: 'center', minWidth: 120 },
                    ]],
                    page: false,
                    done: function (res, curr, count) {
                        $view.find('#previousTotalCount').text(`检索 ${count} 条数据`);
                    }
                })
            }

            //触发排序事件
            table.on('sort(protocolLineList)', function (obj) {
                if (getLineQueryParams().phdpId !== undefined && getLineQueryParams().phdpId != null) {
                    let param = getLineQueryParams()
                    param.field = obj.field;
                    param.order = obj.type;//desc;asc;null
                    tableDetailIns.reload({
                        initSort: obj,
                        where: param,
                        url: ctx + 'por/porLineDeviceProtocol/page',
                        // page: {curr: 1}
                    });
                } else {// 应对物资编码和描述有值的场景
                    var data = table.cache['protocolLineList'];
                    data.sort(function (a, b) {
                        if (obj.type == 'asc') {
                            return a[obj.field] - b[obj.field];
                        } else {
                            return b[obj.field] - a[obj.field];
                        }
                    })
                    table.renderData('protocolLineList');
                }
            })


            //switch事件
            form.on('switch(restFlgChange)', function (obj) {

                let pldpId = $(this).attr("pldpId");
                let amount = $(this).attr("amount");
                let restAmount = $(this).attr("restAmount");
                let remark = $(this).attr("remark");
                let restFlg = $(this).attr("restFlg");
                if (restFlg != 0) {
                    restFlg = 0;
                } else {
                    restFlg = 1;
                }

                data = {
                    pldpId, amount, restAmount, remark, restFlg
                }
                porLineRestAmount({ data });
            })


            // Head头部工具栏事件
            table.on('toolbar(porHeadMaterialTable)', function (obj) {
                if (obj.event == 'import') {
                    deviceProtocolImport(obj)
                } else if (obj.event == 'add') {
                    deviceProtocolAdd(obj);
                } else if (obj.event == 'delete') {
                    deviceProtocolDelete(obj);
                } else if (obj.event == 'export') {
                    exportProtocol(obj);
                } else if (obj.event == 'saveBatch') {
                    deviceProtocolSaveBatch(obj);
                } else if (obj.event == 'copy') {
                    copyProtocol(obj);
                } else if (obj.event == 'up') {
                    upSap(obj);
                } else if (obj.event == 'updatePurchase') {
                    updatePurchase(obj)
                } else if (obj.event == 'confirmSap') {
                    confirmSap(obj);
                } else if (obj.event == 'confirmSapBack') {
                    confirmSapBack(obj);
                } else if (obj.event == 'selectLeftInfos') {
                    selectLeftInfos(obj);
                }
            })

            // 匹配
            function selectLeftInfos(obj) {
                if (shipNoSelect.getValue('value').length === 0) {
                    febs.alert.warn('请选择船号后在查询');
                    return false
                }
                // 查询所有托盘未匹配篇POR
                febs.modal.open('未匹配POR的托盘统计', 'por/porMaterialManagerView/leftByProInfos', {
                    btn: ['生成POR'],
                    area: ['70%', '80%'],
                    data: {
                        shipId: shipNoSelect.getValue('value')[0],
                        shipArr,
                        ssProfessionUserList: ssProfessionUserList,
                        xsProfessionUserList: xsProfessionUserList,
                        type: 'device'
                    },
                    yes: function (e) {
                        $('#leftByProInfos').find('#leftByPorSubmit').trigger('click');
                    }
                })
            }

            //撤销确认
            function confirmSapBack(obj) {
                if (shipNoSelect.getValue('value').length === 0) {
                    febs.alert.warn('请选择船号后再撤销确认SAP');
                    return false
                }
                let checkStatus = table.checkStatus('protocolList');
                if (checkStatus.data.length == 0) {
                    febs.alert.warn('请选择数据后再撤销确认SAP');
                    return false
                }
                let flg = true;
                let infos = [];
                $.each(checkStatus.data, function (i, v) {
                    if (v.statusStr != "已上传") {
                        flg = false;
                        febs.alert.warn('未上传的POR编号不能撤销确认SAP');
                        return false
                    } else if (v.sapStatus != "1") {
                        flg = false;
                        febs.alert.warn('未确认的POR编号不能撤销确认SAP');
                        return false
                    }

                    let haveEditShiro = false;
                    haveEditShiro = xsorssMap.get(v.proType + "@" + v.proId) != true ? false : true;
                    if (xsorssMap.size == 0) {
                        haveEditShiro = true;
                    }
                    if (!haveEditShiro) {
                        febs.alert.warn(v.porHeadNo + "数据您没有操作权限");
                        flg = false;
                        return false;
                    }
                    let fv = {};
                    fv.phdpId = v.phdpId;
                    fv.sapStatus = "0";
                    infos.push(fv)
                })
                if (flg) {
                    febs.modal.confirm('确认', '请确认是否更改选中数据的SAP状态', function () {
                        febs.postArray(ctx + 'por/porHeadDeviceProtocol/updateBatch', infos, function () {
                            febs.alert.success('修改成功');
                            // $(obj.tr[0]).removeClass('edited')
                            $headQuery.trigger("click")
                        })
                    })
                }
            }

            //确认
            function confirmSap(obj) {
                if (shipNoSelect.getValue('value').length === 0) {
                    febs.alert.warn('请选择船号后再确认SAP');
                    return false
                }
                let checkStatus = table.checkStatus('protocolList');
                if (checkStatus.data.length == 0) {
                    febs.alert.warn('请选择数据后再确认SAP');
                    return false
                }
                let flg = true;
                let infos = [];
                $.each(checkStatus.data, function (i, v) {
                    if (v.statusStr != "已上传") {
                        flg = false;
                        febs.alert.warn('未上传的POR编号不能确认SAP');
                        return false
                    }
                    let haveEditShiro = false;
                    haveEditShiro = xsorssMap.get(v.proType + "@" + v.proId) != true ? false : true;
                    if (xsorssMap.size == 0) {
                        haveEditShiro = true;
                    }
                    if (!haveEditShiro) {
                        febs.alert.warn(v.porHeadNo + "数据您没有操作权限");
                        flg = false;
                        return false;
                    }
                    let fv = {};
                    fv.phdpId = v.phdpId;
                    fv.sapStatus = "1";
                    infos.push(fv)
                })
                if (flg) {
                    febs.modal.confirm('确认', '请确认是否更改选中数据的SAP状态', function () {
                        febs.postArray(ctx + 'por/porHeadDeviceProtocol/updateBatch', infos, function () {
                            febs.alert.success('修改成功');
                            // $(obj.tr[0]).removeClass('edited')
                            $headQuery.trigger("click")
                        })
                    })
                }
            }

            //上传SAP
            function upSap(obj) {
                if (shipNoSelect.getValue('value').length === 0) {
                    febs.alert.warn('请选择船号后再上传SAP');
                    return false
                }
                let checkStatus = table.checkStatus('protocolList');
                if (checkStatus.data.length == 0) {
                    febs.alert.warn('请选择数据后再上传SAP');
                    return false
                }
                let flg = true;
                let sapParam = {};
                let headIds = [];
                $.each(checkStatus.data, function (i, v) {
                    if (v.produceDate == null) {
                        flg = false;
                        febs.alert.warn('选中的POR编号里有生产需求日为空不能上传SAP');
                        return false
                    } else if (v.statusStr == "全部上传" || v.status == "2") {
                        flg = false;
                        febs.alert.warn('该POR下物资SAP已全部上传，无需重新上传SAP');
                        return false
                    }
                    let haveEditShiro = false;
                    haveEditShiro = xsorssMap.get(v.proType + "@" + v.proId) != true ? false : true;
                    if (xsorssMap.size == 0) {
                        haveEditShiro = true;
                    }
                    if (!haveEditShiro) {
                        febs.alert.warn(v.porHeadNo + "数据您没有操作权限");
                        flg = false;
                        return false;
                    }
                    headIds.push(v.phdpId)
                })
                if (flg) {
                    sapParam = {
                        porMainIdList: headIds,
                        dataType: "1"
                    }
                    febs.post(ctx + 'sap/porSapValid', sapParam, function (res) {
                        let index = layer.msg('SAP已在上传中，请耐心等待', { icon: 16, shade: 0.01, time: 10800000 })
                        if (res.code == 200) {
                            let validRes = res.data;
                            if (validRes.errorList.length > 0 && validRes.errorList != null) {
                                layer.close(index);
                                let html = '<div style="display: flex;flex-direction: column;">';
                                validRes.errorList.forEach(item => {
                                    html += '<label>POR编号:' + item.porNo + ',错误原因：' + item.errMessage + '</label>'
                                })
                                html = html + '</div>';
                                layer.open({
                                    title: 'SAP上传验证',
                                    content: html
                                });
                            } else {
                                febs.post(ctx + 'sap/sapRecordSync', sapParam, function (data) {
                                    if (data.code == 200) {
                                        if (data.data.resultFlg) {
                                            layer.close(index)
                                            // febs.alert.success(data.message);
                                            layer.alert(data.data.msg)
                                            $headQuery.trigger("click")
                                        } else {
                                            layer.close(index)
                                            // febs.alert.error(data.message);
                                            layer.open({
                                                title: '上传SAP失败信息',
                                                type: 1,
                                                area: ['20%', '40%'],
                                                content: '<div style="padding: 16px;">' + data.data.msg + '</div>',
                                                success: function () {
                                                },
                                            });
                                        }

                                    }
                                });
                            }
                        } else {
                            layer.close(index);
                            febs.alert.error(res.message);
                        }
                    });

                }
            }

            function updatePurchase(obj) {
                if (shipNoSelect.getValue('value').length === 0) {
                    febs.alert.warn('请选择船号后再同步采购状态');
                    return false
                }
                let checkStatus = table.checkStatus('protocolList');
                if (checkStatus.data.length == 0) {
                    febs.alert.warn('请选择数据同步采购状态');
                    return false
                }
                let flg = true;

                let headIds = [];
                let errList = [];
                $.each(checkStatus.data, function (i, v) {

                    let haveEditShiro = false;
                    haveEditShiro = xsorssMap.get(v.proType + "@" + v.proId) != true ? false : true;
                    if (xsorssMap.size == 0) {
                        haveEditShiro = true;
                    }
                    if (!haveEditShiro) {
                        febs.alert.warn(v.porHeadNo + "数据您没有操作权限");
                        flg = false;
                        return false;
                    }
                    if (v.status == null || v.status == '0') {
                        errList.push(v.phdpId)
                    } else {
                        headIds.push(v.phdpId)
                    }

                })
                if (flg) {
                    if (errList.length > 0) {
                        febs.modal.confirm("是否继续同步", '勾选数据中存在未上传的POR，是否继续同步?', function () {
                            updatePurchaseSap(headIds)
                        }, function () {
                            layer.closeAll();
                        });
                    } else {
                        updatePurchaseSap(headIds)
                    }
                }
            }

            function updatePurchaseSap(headIds) {
                let sapParam = {
                    porMainIdList: headIds,
                    dataType: "8"
                }
                febs.post(ctx + 'sap/porSapValid', sapParam, function (res) {
                    let index = layer.msg('同步采购状态中，请耐心等待', { icon: 16, shade: 0.01, time: 10800000 })
                    if (res.code == 200) {
                        febs.post(ctx + 'sap/sapRecordSync', sapParam, function (data) {
                            if (data.code == 200) {
                                if (data.data.resultFlg) {
                                    layer.close(index)
                                    // febs.alert.success(data.message);
                                    layer.alert(data.data.msg)
                                    $headQuery.trigger("click")
                                } else {
                                    layer.close(index)
                                    // febs.alert.error(data.message);
                                    layer.open({
                                        title: '上传SAP失败信息',
                                        type: 1,
                                        area: ['20%', '40%'],
                                        content: '<div style="padding: 16px;">' + data.data.msg + '</div>',
                                        success: function () {
                                        },
                                    });
                                }
                            }
                        });
                    } else {
                        layer.close(index);
                        febs.alert.error(res.message);
                    }
                });
            }

            function copyProtocol(obj) {
                febs.modal.open('协议采购复制', 'por/porHeadMaterialManageCopy', {
                    btn: '提交',
                    area: ['700px', '550px'],
                    data: {
                        source: 'device',
                        ssProfessionUserList: ssProfessionUserList,
                        xsProfessionUserList: xsProfessionUserList
                    },
                    yes: function (e) {
                        $('#copyCarryNo').find('#submit').trigger('click');
                    }
                })
            }

            //导出
            function exportProtocol(obj) {
                if (shipNoSelect.getValue('value').length === 0) {
                    febs.alert.warn('请选择船号后再导出');
                    return false
                }
                let checkStatus = table.checkStatus('protocolList');
                if (checkStatus.data.length > 0) {
                    //勾选导出
                    let phdpIdList = []
                    $.each(checkStatus.data, function (i, v) {
                        phdpIdList.push(v.phdpId)
                    })
                    febs.download(ctx + 'por/porHeadDeviceProtocol/excel',
                        { phdpIds: phdpIdList },
                        'POR设备协议导出' + new Date().getTime() + '.xlsx');
                } else {
                    //当前页导出
                    let param = getHeadQueryParams();
                    param.pageSize = $mainTable.find('.layui-laypage-limits').find("option:selected").val()
                    param.pageNum = $mainTable.find('.layui-laypage-em').next().html()
                    param.shipTypeId = shipTypeId
                    febs.download(ctx + 'por/porHeadDeviceProtocol/excel',
                        param,
                        'POR设备协议导出' + new Date().getTime() + '.xlsx');
                }
            }

            //head批量删除
            function deviceProtocolDelete(obj) {
                let deleteInfo;
                let phdpIdList = [];
                let flg = true;
                let checkStatus = table.checkStatus('protocolList');
                if (checkStatus.data.length == 0) {
                    febs.alert.warn('请至少选择一条数据');
                    return false;
                }
                $.each(checkStatus.data, function (i, v) {
                    if (v.sapStatus != '0') {
                        febs.alert.warn(v.porHeadNo + '中存在SAP已处理的物资，不能删除');
                        flg = false;
                        return false;
                    }
                    let haveEditShiro = false;
                    haveEditShiro = xsorssMap.get(v.proType + "@" + v.proId) != true ? false : true;
                    if (xsorssMap.size == 0) {
                        haveEditShiro = true;
                    }
                    if (!haveEditShiro) {
                        febs.alert.warn(v.porHeadNo + "数据您没有操作权限");
                        flg = false;
                        return false;
                    }
                    phdpIdList.push(v.phdpId)
                })
                let phdpIds = phdpIdList.join(',')
                // if (flg) {
                //     febs.getSyn(ctx + 'por/porLineDeviceProtocol/count', {phdpIds: phdpIds}, function (data) {
                //         deleteInfo = data.data
                //     })
                //     if (deleteInfo.length > 0) {
                //         febs.alert.warn(deleteInfo[0].info + '存在物资编码不能删除');
                //         flg = false;
                //         return false
                //     }
                // }
                if (flg) {
                    febs.modal.confirm('删除POR_HEAD', '是否确定删除选中的head数据？', function () {
                        febs.get(ctx + 'por/porHeadDeviceProtocol/delete/' + phdpIds, null, function () {
                            febs.alert.success('删除成功');
                            tableIns.reload({
                                where: getHeadQueryParams(),
                                url: ctx + 'por/porHeadDeviceProtocol/page',
                                // page: {curr: 1}
                            });
                            createTable2([]);
                        })
                    })

                }
            }

            function deviceProtocolSaveBatch(obj) {
                let infos = [];
                let saveData;
                let msg = '是否确定批量保存已修改的数据';
                let thisCache = table.cache['protocolList'] || {}
                let checkStatus = table.checkStatus('protocolList');
                if (checkStatus.data.length === 0) {
                    saveData = thisCache;
                    msg = '未勾选要保存的数据，将全部保存';
                } else {
                    saveData = checkStatus.data;
                }
                if (saveData === null || saveData.length === 0) {
                    febs.alert.warn('当前页面无数据，无需保存');
                    return false;
                }
                $.each(saveData, function (i, v) {
                    v.produceDate = null; //后台转换报错
                    v.protocolDate = null;//后台转换报错
                    infos.push(v)
                })

                febs.modal.confirm('批量保存', msg, function () {
                    febs.postArray(ctx + 'por/porHeadDeviceProtocol/updateBatch', infos, function () {
                        febs.alert.success('修改成功');
                        // $(obj.tr[0]).removeClass('edited')
                        $headQuery.trigger("click")
                    })
                })
            }

            function porLineRestAmount(obj) {
                let data = obj.data;
                febs.postArray(ctx + 'por/porLineDeviceProtocol/update', data, function () {
                    febs.alert.success('修改成功');
                    // $lineQuery.trigger("click")
                    tableDetailIns.reloadData();
                })
            }

            //head新增
            function deviceProtocolAdd(obj) {
                if (shipNoSelect.getValue('value').length === 0) {
                    febs.alert.warn('请选择船号后再新增');
                    return false
                }
                febs.modal.open('新增', 'por/porDeviceProtocolListManger/addDeviceProtocolHead', {
                    btn: ['确定'],
                    area: ['600px', '530px'],
                    data: {
                        shipId: shipNoSelect.getValue('value')[0],
                        shipTypeId: shipNoSelect.getValue()[0].typeId,
                        xsProfessionUserList: xsProfessionUserList,
                        ssProfessionUserList: ssProfessionUserList
                    },
                    yes: function (e) {
                        $('#febs-grantHeadAdd').find('#grantHeadSubmit').trigger('click');
                    }
                });
            }

            //head导入按钮
            function deviceProtocolImport(obj) {
                if (shipNoSelect.getValue('valueStr') == '') {
                    febs.alert.warn('请选择船号');
                    return false
                }
                febs.modal.open('POR-Head导入', 'por/porDeviceProtocolListManger/importDeviceProtocol', {
                    btn: ['开始上传'],
                    area: ['700px', '480px'],
                    data: { shipId: shipNoSelect.getValue('valueStr'), shipNo: shipNoSelect.getValue('nameStr') },
                    yes: function (e) {
                        $('#deviceProtocolImport').find('#test9').trigger('click');
                    }
                });
            }

            // Line头部工具栏事件
            table.on('toolbar(protocolLineList)', function (obj) {
                if (obj.event == 'importLine') {
                    deviceProtocolLineImport(obj)
                } else if (obj.event == 'addLine') {
                    deviceProtocolLineAdd(obj);
                } else if (obj.event == 'deleteLine') {
                    deviceProtocolLineDelete(obj);
                } else if (obj.event == 'saveBatchLine') {
                    deviceProtocolLineSaveBatch(obj);
                } else if (obj.event == 'saveBatchLineExit') {
                    deviceProtocolLineSaveBatchExit(obj);
                }
            })

            $('#addLine').on('click', function () {
                deviceProtocolLineAdd();
            })
            $('#deleteLine').on('click', function () {
                deviceProtocolLineDelete();
            })
            $('#importLine').on('click', function () {
                deviceProtocolLineImport();
            })
            $('#saveBatchLine').on('click', function () {
                deviceProtocolLineSaveBatch();
            })
            $('#saveBatchLineExit').on('click', function () {
                deviceProtocolLineSaveBatchExit();
            })

            //line 批量删除
            function deviceProtocolLineDelete(obj) {
                // if (headStatus == 1){
                //     febs.alert.warn('已确认数据不能删除');
                //     return
                // }
                if (linePhdpId === undefined || linePhdpId === null) {
                    febs.alert.warn('请先选择por_head');
                    return;
                }
                if (!onRowHaveEditShiro) {
                    febs.alert.warn('当前数据您没有操作权限');
                    return;
                }
                let pldpIdList = [];
                let phdpIdList = [];
                let checkStatus = table.checkStatus('protocolLineList');
                if (checkStatus.data.length == 0) {
                    febs.alert.warn('请至少选择一条数据');
                    return false;
                }
                let errMaterialNos = new Set()
                $.each(checkStatus.data, function (i, v) {
                    errMaterialNos.add(v.basicMaterialNo)
                    pldpIdList.push(v.pldpId)
                    phdpIdList.push(v.phdpId)
                })
                if (headUploadStatus != '0') {
                    let headIds = [linePhdpId]
                    let sapParam = {
                        porMainIdList: headIds,
                        dataType: "8"
                    }
                    febs.post(ctx + 'sap/sapRecordSync', sapParam, function (d) {
                        if (d.code == 200) {
                            if (d.data.resultFlg) {
                                if (d.data.materialNos.length > 0) {
                                    let res = d.data.materialNos.filter(item => errMaterialNos.has(item));
                                    if (res.length > 0) {
                                        let msg = '以下物资编码SAP已处理：' + res.join('、') + ',无法删除！';
                                        layer.open({
                                            title: '删除失败',
                                            type: 1,
                                            area: 'auto',
                                            content: '<div style="padding: 16px;">' + msg + '</div>',
                                            success: function () {
                                            },
                                        });
                                    } else {
                                        delLine(phdpIdList, pldpIdList)
                                    }
                                } else {
                                    delLine(phdpIdList, pldpIdList)
                                }
                            } else {
                                delLine(phdpIdList, pldpIdList)
                            }
                        }
                    });
                } else {
                    delLine(phdpIdList, pldpIdList)
                }

            }

            function delLine(phdpIdList, pldpIdList) {
                let phdpIds = phdpIdList.join(',')
                let pldpIds = pldpIdList.join(',')
                febs.modal.confirm('删除', '是否确定删除选中的Line数据', function () {
                    febs.get(ctx + 'por/porLineDeviceProtocol/delete/' + pldpIds, { phdpIds: phdpIds }, function (data) {
                        if (data.code == 200) {
                            febs.alert.success('删除成功');
                            tableDetailIns.reload({
                                where: getLineQueryParams(),
                                url: ctx + 'por/porLineDeviceProtocol/page',
                                // page: {curr: 1}
                            });
                            tableIns.reloadData();
                        }

                    })
                })
            }

            //line 新增
            function deviceProtocolLineAdd() {
                if (linePhdpId === undefined || linePhdpId === null) {
                    febs.alert.warn('请先选择por_head');
                    return;
                }
                if (!onRowHaveEditShiro) {
                    febs.alert.warn('当前数据您没有操作权限');
                    return;
                }
                if (headUploadStatus != '0') {
                    let headIds = [linePhdpId]
                    let sapParam = {
                        porMainIdList: headIds,
                        dataType: "8"
                    }
                    febs.post(ctx + 'sap/sapRecordSync', sapParam, function (d) {
                    });
                }

                febs.modal.open('POR-Line新增', 'por/porDeviceProtocolListManger/addDeviceProtocolLine', {
                    btn: ['保存'],
                    area: ['500px', '470px'],
                    data: {
                        phdpId: linePhdpId,
                        shipId: shipNoSelect.getValue('value')[0],
                        shipNo: shipNoSelect.getValue('name')[0]
                    },
                    yes: function (e) {
                        $('#febs-grantLindAdd').find('#grantLineSubmit').trigger('click');
                        tableDetailIns.reloadData();
                    }
                });
            }

            //Line导入按钮
            function deviceProtocolLineImport(obj) {
                // if (headStatus == 1){
                //     febs.alert.warn('已确认数据不能导入');
                //     return
                // }
                febs.modal.open('POR-Line导入', 'por/porDeviceProtocolListManger/importDeviceProtocolLine', {
                    btn: ['开始上传'],
                    area: ['700px', '480px'],
                    data: { tableDetailIns: tableDetailIns, tableIns: tableIns },
                    yes: function (e) {
                        $('#deviceProtocolLineImport').find('#test9').trigger('click');
                    }
                });
            }

            function deviceProtocolLineSaveBatch(obj) {
                if (!onRowHaveEditShiro) {
                    febs.alert.warn('当前数据您没有操作权限');
                    return;
                }
                let infosa = [];
                let infosb = [];
                let saveData;
                let msg = '是否确定批量保存已修改的数据';
                let thisCache = table.cache['protocolLineList'] || {}
                let checkStatus = table.checkStatus('protocolLineList');
                if (checkStatus.data.length === 0) {
                    saveData = thisCache;
                    msg = '未勾选要保存的数据，将全部保存';
                } else {
                    saveData = checkStatus.data;
                }
                if (saveData === null || saveData.length === 0) {
                    febs.alert.warn('当前页面无数据，无需保存');
                    return false;
                }
                $.each(saveData, function (i, v) {
                    let vs = {
                        amount: v.amountDb,
                        pldpId: v.pldpId,
                        restAmount: v.restAmount,
                        remark: v.remark,
                        restFlg: v.restFlg
                    }
                    infosa.push(vs)
                    infosb.push(v)
                })

                febs.modal.confirm('批量保存', msg, function () {
                    febs.postArray(ctx + 'por/porLineDeviceProtocol/updateBatch', infosa, function () {
                        febs.postArray(ctx + 'por/porLineDeviceProtocol/updateNoStandardBatch', infosb, function () {
                            // 注销windows监听器 改变值
                            // window.removeEventListener('beforeunload', function (event) {
                            //     event.preventDefault();
                            // })
                            removeWindowLinstener();
                            $view.find('#editFlagWithLine').val("false");


                            febs.alert.success('修改成功');
                            $lineQuery.trigger("click")
                        })
                    })
                })
            }

            function addOrRemoveLins(event) {
                event.preventDefault();
            }

            window.addOrRemoveLins1 = addOrRemoveLins// 无奈 0.0
            function addWindowLinstener() {
                window.addEventListener('beforeunload', addOrRemoveLins);
            }

            function removeWindowLinstener() {
                window.removeEventListener('beforeunload', addOrRemoveLins);
            }

            function deviceProtocolLineSaveBatchExit(obj) {
                if (!onRowHaveEditShiro) {
                    febs.alert.warn('当前数据您没有操作权限');
                    return;
                }
                let infosa = [];
                let infosb = [];
                let saveData;
                let msg = '是否确定批量保存已修改的数据';
                let thisCache = table.cache['protocolLineList'] || {}
                let checkStatus = table.checkStatus('protocolLineList');
                if (checkStatus.data.length === 0) {
                    saveData = thisCache;
                    msg = '未勾选要保存的数据，将全部保存';
                } else {
                    saveData = checkStatus.data;
                }
                if (saveData === null || saveData.length === 0) {
                    febs.alert.warn('当前页面无数据，无需保存');
                    return false;
                }
                $.each(saveData, function (i, v) {
                    let vs = {
                        amount: v.amountDb,
                        pldpId: v.pldpId,
                        restAmount: v.restAmount,
                        remark: v.remark,
                        restFlg: v.restFlg
                    }
                    infosa.push(vs)
                    infosb.push(v)
                })

                // febs.modal.confirm('批量保存', msg, function () {
                febs.postArray(ctx + 'por/porLineDeviceProtocol/updateBatch', infosa, function () {
                    febs.postArray(ctx + 'por/porLineDeviceProtocol/updateNoStandardBatch', infosb, function () {
                        // febs.alert.success('修改成功');
                        // $lineQuery.trigger("click")
                    })
                })
                // })
            }

            //Head 触发上方单元格工具事件
            table.on('tool(porHeadMaterialTable)', function (obj) { // 双击 toolDouble
                let data = obj.data; // 获得当前行数据
                data.produceDate = null; //后台转换报错
                data.protocolDate = null;//后台转换报错
                data.uploadDate = null;//后台转换报错
                if (obj.event === 'save') {
                    febs.modal.confirm('保存', '是否确定保存已修改的POR描述', function () {
                        febs.postArray(ctx + 'por/porHeadDeviceProtocol/update', data, function () {
                            febs.alert.success('修改成功');
                            $(obj.tr[0]).removeClass('edited')
                            $headQuery.trigger("click")
                        })
                    })
                } else if (obj.event === 'view') {
                    $view.find('.viewBgColor').removeClass('viewBgColor')
                    $view.find('.layui-table-click').addClass('viewBgColor')
                    linePhdpId = data.phdpId
                    headStatus = data.sapStatus;
                    let basicMaterialDesc = $view.find('input[name= basicMaterialDesc]').val();
                    let basicMaterialNo = $view.find('input[name= basicMaterialNo]').val();
                    if (basicMaterialDesc != "" || basicMaterialNo != "") {
                        let par = getHeadQueryParams();
                        par.phdpId = linePhdpId
                        tableDetailIns.reload({
                            where: par,
                            url: ctx + 'por/porLineDeviceProtocol/page',
                            // page: {curr: 1}
                        });
                    } else {
                        tableDetailIns.reload({
                            where: { phdpId: linePhdpId },
                            url: ctx + 'por/porLineDeviceProtocol/page',
                            // page: {curr: 1}
                        });
                    }
                } else if (obj.event === 'uploadView') {
                    febs.modal.open('协议单', 'por/porHeadDeviceManageUploadView', {
                        area: ['75%', '100%'],
                        data: { phdpId: obj.data.phdpId, tableIns: tableIns },
                        // yes: function (e) {
                        //     $('#febs-porUpload').find('#submit').trigger('click');
                        // },
                        end: function (e) {
                        },
                        beforeEnd: function (e) {
                        },
                        cancel: function (index, layero, that) {
                            $('#febs-porUpload').find('#cancelSubmit').trigger('click');

                            // layer.confirm('关闭后未保存的内容将会丢失，确认关闭吗？',{
                            //     btn:['确定','取消']
                            // },function() {
                            //     layer.closeAll();
                            // })
                            //阻止默认关闭弹窗行为
                            return false;
                        }
                    })
                } else {
                    febs.modal.open('协议单上传', 'por/porHeadDeviceManageUpload', {
                        area: ['75%', '100%'],
                        data: { phdpId: obj.data.phdpId, tableIns: tableIns },
                        // yes: function (e) {
                        //     $('#febs-porUpload').find('#submit').trigger('click');
                        // },
                        end: function (e) {
                        },
                        beforeEnd: function (e) {
                        },
                        cancel: function (index, layero, that) {
                            $('#febs-porUpload').find('#cancelSubmit').trigger('click');

                            // layer.confirm('关闭后未保存的内容将会丢失，确认关闭吗？',{
                            //     btn:['确定','取消']
                            // },function() {
                            //     layer.closeAll();
                            // })
                            //阻止默认关闭弹窗行为
                            return false;
                        }
                    })
                }
            });
            //Head 单元格编辑事件
            // table.on('edit(porHeadMaterialTable)', function (obj) {
            //     $(obj.tr[0]).addClass('edited')
            //     let index2 = $(this).closest('tr').data('index');
            //     table.setRowChecked('protocolList',{
            //         index: index2,
            //         checked:true
            //     })
            //     var field = obj.field; // 得到字段
            //     var value = obj.value; // 得到修改后的值
            //     var data = obj.data; // 得到所在行所有键值
            //     // 编辑后续操作，如提交更新请求，以完成真实的数据更新
            //     // 其他更新操作
            //     var update = {};
            //     update[field] = value;
            //     obj.update(update);
            // });
            //Line 触发上方单元格工具事件
            table.on('tool(protocolLineList)', function (obj) { // 双击 toolDouble
                let data = obj.data; // 获得当前行数据
                data.amount = data.amountDb
                switch (obj.event) {
                    case 'saveLine':
                        febs.modal.confirm('保存', '是否确定保存已修改数据', function () {
                            febs.postArray(ctx + 'por/porLineDeviceProtocol/update', data, function () {

                            })
                            febs.postArray(ctx + 'por/porLineDeviceProtocol/updateNoStandard', data, function () {
                                febs.alert.success('修改成功');
                                $(obj.tr[0]).removeClass('edited')
                                $lineQuery.trigger("click")
                            })
                        })
                        break;
                    case 'detailTray':
                        openDetailTray(data);
                        break;
                    case 'saveLine':
                        porLineRestAmount(obj);
                        break;
                    case 'showAppendix':
                        showAppendixList(data);
                        break;
                    case 'showAppendixBdNum':
                        showAppendixBdNum(data);
                        break;
                    default:
                        break;
                }

            });

            //打开附件查看明细并更改附件标定数量
            function showAppendixBdNum(data) {
                if (data.restFlg == 0 || headStatus == 0) {
                    return;
                }
                febs.modal.open('修改附件号标定', 'por/porDeviceProtocolListManger/updateAppendix', {
                    btn: ['确定'],
                    area: ['700px', '680px'],
                    data: {
                        shipId: data.shipId,
                        porId: data.phdpId,
                        pldpId: data.pldpId,
                    },
                    yes: function (index, layero) {
                        $('#febs-appendixUpdate').find('#appendixSubmit').trigger('click');
                        layer.close(index)
                        tableDetailIns.reload()
                    }
                });
            }

            function showAppendixList(obj) {
                if (obj.appendixFlg == '0' && obj.sapStatus == '1') {
                    return false
                }
                febs.modal.open('新增附件号', 'por/porDeviceProtocolListManger/addAppendix', {
                    btn: ['确定'],
                    area: ['530px', '680px'],
                    data: {
                        shipId: obj.shipId,
                        porId: obj.phdpId,
                        pldpId: obj.pldpId,
                        headStatus: obj.sapStatus,
                        amount: obj.amountDb
                    },
                    yes: function (index, layero) {
                        $('#febs-appendixAdd').find('#appendixSubmit').trigger('click');
                        layer.close(index)
                        tableDetailIns.reload()
                    }
                });
            }

            //查看物资被哪些托盘使用了
            function openDetailTray(data) {
                febs.modal.open('物资管理详情', 'por/porDeviceManageView/openDetailTray', {
                    area: ['100%', '100%'],
                    data: {
                        row: data
                    },
                });
            }

            //Line 单元格编辑事件
            table.on('edit(protocolLineList)', function (obj) {
                let haveEditShiro = false;
                if (xsorssMap.size == 0) {
                    haveEditShiro = true;
                } else {
                    haveEditShiro = xsorssMap.get(obj.data.proType + "@" + obj.data.proId) != true ? false : true;
                }
                if (!haveEditShiro) {
                    var field = obj.field;
                    var oldValue = obj.oldValue;
                    var update = {};
                    update[field] = oldValue;
                    obj.update(update, true)
                    layer.tips('当前数据您没有操作权限', this, { tips: 1 });
                } else {
                    var field = obj.field; // 得到字段
                    var value = obj.value; // 得到修改后的值
                    var data = obj.data; // 得到所在行所有键值
                    if (field === 'weight' || field === 'amountDb' || field === 'restAmount') {
                        if (!/^(0|[1-9]\d*)$/.test(obj.value)) {
                            layer.tips('输入的数量不正确，请重新编辑', this, { tips: 1 });
                            return obj.reedit(); // 重新编辑 -- v2.8.0 新增
                        }
                    }
                    if (field === 'restAmount') {
                        if (Number(value) > (Number(obj.data.amountDb) - Number(obj.data.useAmount))) {
                            layer.tips("超过剩余可使用物资数量，请重新填写", this, { tips: 1 })
                            return obj.reedit();
                        }
                    }
                    if (data.uploadStatus == '1') {
                        let headIds = [data.phdpId]
                        let sapParam = {
                            porMainIdList: headIds,
                            dataType: "8"
                        }
                        let sapStatus = '0 '
                        let tip = this
                        febs.post(ctx + 'sap/sapRecordSync', sapParam, function (d) {
                            if (d.code == 200) {
                                if (d.data.resultFlg) {
                                    febs.getSyn(ctx + 'por/porLineDeviceProtocol/getSapStatusById', { lineId: data.pldpId }, function (e) {
                                        if (e.code == '200') {
                                            sapStatus = e.data
                                        }
                                    })
                                    if (sapStatus == '1') {
                                        layer.tips("该物资SAP已处理，无法修改", tip, { tips: 1 })
                                        return obj.reedit();
                                    }
                                    saveLineRow(obj.data)
                                } else {
                                    layer.open({
                                        title: '同步采购失败信息',
                                        type: 1,
                                        area: ['20%', '40%'],
                                        content: '<div style="padding: 16px;">' + d.data.msg + '</div>',
                                        success: function () {
                                        },
                                    });
                                    return obj.reedit();
                                }
                            }
                        });
                    } else {
                        saveLineRow(obj.data)
                    }


                }
            });

            function saveLineRow(data) {
                let arr1 = [{
                    amount: data.amountDb,
                    pldpId: data.pldpId,
                    restAmount: data.restAmount,
                    remark: data.remark,
                    restFlg: data.restFlg
                }];
                let arr2 = [data];
                febs.postArray(ctx + 'por/porLineDeviceProtocol/updateBatch', arr1, function () {
                    febs.postArray(ctx + 'por/porLineDeviceProtocol/updateNoStandardBatch', arr2, function () {
                        febs.alert.success('修改成功');
                        tableDetailIns.reloadData();
                    })
                })

            }

            let isDrawflg = false;


            $(document).ready(function () {
                $('#btnRemark').draggable({
                    containment: "#febs-porDeviceProtocolListManger",
                    grid: [1, 1],
                })
                $('#btnRemark').on('drag', function (e, u) {
                    let newPosition = u.helper.position();
                    $(this).data('position', newPosition)
                    if ($('#btnRemarkContent').offset().left < 0) {
                        $('#btnRemarkContent').css('left', $('#btnRemarkContent').offset().left + 259)
                        $('#btnRemarkContent').css('top', newPosition.top + 30)
                    } else {
                        $('#btnRemarkContent').css('top', newPosition.top + 30)
                        $('#btnRemarkContent').css('left', newPosition.left - 227)
                    }

                })

                $('#btnRemark').mousedown(function () {
                    isDrawflg = false;
                }).mousemove(function () {
                    isDrawflg = true;
                })

                //颜色功能友好提示
                $('#btnRemark').on('click', function () {
                    //false 标识不是拖拽操作 而是点击操作
                    if (!isDrawflg) {
                        //

                        let vis = $('#btnRemarkContent').is(":visible");
                        if (vis) {
                            //开了 就关
                            $('#btnRemarkContent').fadeOut();
                        } else {
                            //关了 就开
                            $('#btnRemarkContent').fadeIn();
                            if ($('#btnRemarkContent').offset().left < 0) {
                                $('#btnRemarkContent').css('left', $('#btnRemarkContent').offset().left + 259)
                            }
                        }
                    }
                })
                let pageLeft = Number($('#transferMaterialTips').css('left').split('px')[0]) - 1135;
                // 物资移库窗口
                $('#transferMaterialTips').draggable({
                    containment: "#febs-porDeviceProtocolListManger",
                    grid: [1, 1],
                    start: function (e, u) {
                        let $movePage = $('#transferMaterialTipsContent')
                        if ($movePage.css('left') != 'auto') {
                            pageLeft = parseInt($movePage.css('left').split('px')[0])
                        }
                    },
                    drag: function (e, u) {
                        let $movePage = $('#transferMaterialTipsContent')
                        let newPosition = u.helper.position();
                        let oriLeft = u.originalPosition.left;
                        let left = u.position.left;
                        $(this).data('position', newPosition)
                        $movePage.css('top', newPosition.top + 30)
                        $movePage.css('left', pageLeft - (oriLeft - left))
                    },
                })

                //颜色功能友好提示
                $('#transferMaterialTips').on('click', function () {
                    //false 标识不是拖拽操作 而是点击操作
                    let vis = $('#transferMaterialTipsContent').is(":visible");
                    if (vis) {
                        //开了 就关
                        $('#transferMaterialTipsContent').fadeOut();
                    } else {
                        //关了 就开
                        $('#transferMaterialTipsContent').fadeIn();
                        // if ($('#transferMaterialTipsContent').offset().left < -550) {
                        //     $('#transferMaterialTipsContent').css('left', $('#transferMaterialTipsContent').offset().left + 1100)
                        // }

                        // 打开时检索
                        $('#transferQuery').trigger('click')
                        table.setRowChecked('applyStorgeList', { index: 'all' })// 简单粗暴 重开就全选不记忆
                    }
                })
            })

            $('#transferMaterialForApply').on('click', function () {
                let checkStatus = table.checkStatus('protocolLineList');
                if (checkStatus.data.length <= 0) {
                    febs.alert.warn('请至少选择一条Line数据');
                    return false;
                }

                let params = [];
                let flg = false;
                $.each(checkStatus.data, function (i, v) {
                    let param = v;
                    if (param.sapStatus != 1) {
                        febs.alert.warn('所选数据需要已确认');
                        flg = true;
                        return;
                    } else {
                        params.push(param)
                    }
                })
                if (flg) {
                    return;
                }
                febs.modal.open('物资移库', 'por/porDeviceManageView/transferMaterials', {
                    btn: '提交',
                    area: ['80%', '80%'],
                    data: {
                        source: 'device',
                        phDesc: linePhDesc,
                        porId: porId,
                        porType: porType,
                        lineInfos: params,
                        type: 'tips',
                    },
                    yes: function (e) {
                        $('#transferMaterials').find('#submit').trigger('click');
                        addWindowLinstener();
                    }
                })
            })

            setProTypeTransfer()

            //  物资移库tips
            function setProTypeTransfer() {
                professionsTransfer = xmSelect.render({
                    el: '#professionsTransfer',
                    data: [],
                    radio: true,
                    clickClose: true,
                    filterable: true,
                    height: '150px',
                    tree: {
                        show: true,
                        strict: false,

                    },
                    template({ item }) {
                        return '<div class="selectNameDiv">' + item.name + '</div>'
                            + '<div class="selectShowNameDiv">'
                            + '<span style="padding-right: 5px;" title="' + item.desc + '">' + item.desc + '</span>'
                            + '</div>'
                    },
                    // iconfont : {
                    //     parent : 'hidden' //隐藏父节点图标
                    // },
                })
            }

            // tips查询 查的是已添加的数据
            $('#transferQuery').on('click', function (d) {
                // let shipId = porShipNoSelect.getValue('valueStr');
                let dstShipId = dstShipNoSelect.getValue('valueStr');
                let professionId = professionsTransfer.getValue('value').length === 0 ? '' : professionsTransfer.getValue('value')[0];
                let porType = proTypeListTransfer.getValue('value').length === 0 ? '' : proTypeListTransfer.getValue('value')[0];
                let porNo = $('#porHeadNoTransfer').val();// 以下支持模糊匹配
                let basicMaterialNoOrDesc = $('#basicMaterialNoOrDesc').val();
                let dataArr = JSON.parse(localStorage.getItem('porDeviceInfos'))
                // 根据条件做全匹配或模糊匹配
                // if (shipId != '') {
                //     dataArr = dataArr.filter(item=> shipId == item.porShipId)
                // }
                if (dstShipId != '') {
                    dataArr = dataArr.filter(item => dstShipId == item.dstShipId)
                }
                if (professionId != '') {
                    dataArr = dataArr.filter(item => professionId == item.porId)
                }
                if (porType != '') {
                    dataArr = dataArr.filter(item => porType == item.porType)
                }
                if (porNo != '') {
                    const regex = new RegExp(porNo);
                    dataArr = dataArr.filter(item => regex.test(item.porNo))
                }
                if (basicMaterialNoOrDesc != '') {
                    const regex = new RegExp(basicMaterialNoOrDesc);
                    dataArr = dataArr.filter(item => regex.test(item.basicMaterialNo) || regex.test(item.basicMaterialDesc))
                }

                createTransferTable((dataArr != null ? dataArr : []))
            })

            // 取得存在缓存的数据展示在页面上
            reloadTipsTable()

            function reloadTipsTable() {
                let dataArr = JSON.parse(localStorage.getItem('porDeviceInfos'))

                createTransferTable((dataArr != null ? dataArr : []));
            }

            function createTransferTable(dataArr) {
                transferTableIns = febs.table.init({
                    elem: $view.find('#applyStorgeList'),
                    id: 'applyStorgeList',
                    data: dataArr,
                    autoSort: true,
                    sort: true,
                    // toolbar: '#deviceProtocolListMangerToolbar',.layui-table-cell .layui-form-checkbox[lay-skin=primary] {top: -8px}
                    css: [ // 重设当前表格样式
                        '.layui-table-tool-temp{padding-right: 145px;} .layui-table-cell .layui-form-checkbox[lay-skin=primary] {top: -8px}'
                    ].join(''),
                    defaultToolbar: [],
                    height: '#applyStorge-1',
                    cols: [
                        [
                            { type: 'checkbox' },
                            { field: 'porShipNo', title: '当前船', width: 95, align: 'center' },
                            {
                                field: 'porNo', title: 'porNo', align: 'center', width: 90, templet: function (d) {
                                    return '<div style="text-align: left">' + d.porNo + '</div>'
                                }
                            },
                            {
                                field: 'porDesc', title: 'por描述', width: 150, align: 'center', edit: function (d) {
                                    if (d.sapStatus == 0) {
                                        if (!permiFlag) {
                                            return null;
                                        }
                                        return "text"
                                    }
                                },
                                templet: function (d) {
                                    return '<div style="text-align: left">' + d.porDesc + '</div>'
                                }
                            },

                            { field: 'lineNo', title: 'LineNo', width: 50, align: 'center' },
                            { field: 'basicMaterialNo', title: '物资编码', width: 180, align: 'center' },
                            { field: 'basicMaterialDesc', title: '物资描述', minWidth: 90, align: 'center' },
                            {
                                field: 'amount',
                                title: '<span style="color: #1b83f0;font-weight: bold">移库数量</span>',
                                width: 90,
                                align: 'center',
                                edit: function (d) {
                                    if (d.appendixFlg == '0') {
                                        return 'text'
                                    }
                                },
                                templet: function (d) {
                                    if (d.appendixFlg == '0') {
                                        return d.amount
                                    } else {
                                        return '<span lay-event="updateAppendix" style="text-decoration: underline;">' + (d.amount) + '</span>'
                                    }

                                }
                            },
                            {
                                field: 'appendixFlg',
                                title: '附件号标记',
                                align: 'center',
                                width: 100,
                                minWidth: 70,
                                templet: function (d) {
                                    if (d.appendixFlg == 1) {
                                        return `<span class="layui-badge" style="background-color: #4dbeba" >是</span>`
                                    } else {
                                        return `<span class="layui-badge" style="background-color: #e7d9cc">否</span>`

                                    }
                                }
                            },
                            { field: 'dstShipNo', title: '目标船', width: 90, align: 'center' },
                        ]
                    ],
                    page: false,
                    done: function (res, curr, count, origin) {
                    }
                })
            }

            table.on('tool(applyStorgeList)', function (obj) {
                let data = obj.data;
                switch (obj.event) {
                    case 'updateAppendix':
                        updateAppendixList(data);
                        break;
                }

            })

            function updateAppendixList(obj) {
                let appendixCodes = []
                $.each(obj.appendixTransVoList, function (i, v) {
                    appendixCodes.push(v.appendixCode)
                })
                febs.modal.open('编辑目标船附件号', 'por/porLineMaterialManageAdd/transferMaterialAppendixs', {
                    btn: ['确定'],
                    area: ['550px', '400px'],
                    data: {
                        shipId: obj.shipId,
                        porId: obj.phId,
                        pldpId: obj.lineId,
                        headStatus: obj.sapStatus,
                        amount: obj.amount,
                        type: 'update',
                        source: 'device',
                        dstShipId: obj.dstShipId,
                        appendixCodes: appendixCodes
                    },
                    yes: function (index, layero) {
                        $('#transferMaterialAppendixs').find('#appendixSubmit').trigger('click');
                        layer.close(index)
                        $('#transferQuery').trigger('click');
                    }
                });
            }

            table.on('edit(applyStorgeList)', function (obj) { //注：edit是固定事件名，test是table原始容器的属性 lay-filter="对应的值"
                if (!/^(0|[1-9]\d*)$/.test(obj.value)) {
                    layer.tips('输入的数量不正确，请重新编辑', this, { tips: 1 });
                    return obj.reedit(); // 重新编辑 -- v2.8.0 新增
                }
                if (parseInt(obj.value) == 0 || parseInt(obj.value) > parseInt(obj.data.oldAmount)) {
                    layer.tips('输入的数量不正确，请重新编辑', this, { tips: 1 });
                    return obj.reedit(); // 重新编辑 -- v2.8.0 新增
                }
            });
            $('#transferCancle').on('click', function (d) {
                $('#transferMaterialTipsContent').fadeOut();
            })

            $('#transferSubmit').on('click', function (d) {
                let checkStatus = table.checkStatus('applyStorgeList');
                let dataArr = [];
                if (checkStatus.data.length != 0) {
                    dataArr = checkStatus.data
                } else {
                    febs.alert.warn("请先勾选需要提交的数据!")
                    return false;
                    // dataArr = JSON.parse(localStorage.getItem('porDeviceInfos'))
                }
                if (dataArr == null || dataArr.length == 0) {
                    febs.alert.warn("没有要提交的数据!")
                    return false;
                } else {
                    commonJS.loading_open("正在校验移库物资是否满足SAP物资库存数量...")
                    febs.postArray(ctx + 'por/materialPorApplyInfo/stockValid', dataArr, function (d) {
                        if (d.code == 200) {
                            commonJS.loading_close();
                            febs.modal.open('物资申请单提交', 'por/porDeviceManageView/previousApplySubDetailSubmit', {
                                btn: ['确认', '取消'],
                                area: ['450px', '310px'],
                                data: {
                                    tableIns: POPUP_DATA.tableIns,
                                    materialList: dataArr,
                                    source: 'device'
                                },
                                yes: function (e) {
                                    $('#previousShipPorApplyInfoSubmit').find('#addPreviousShipPorApplySubmit').trigger('click');
                                }
                            });
                        }
                    });
                    //本地测试用
                    // febs.modal.open('物资申请单提交', 'por/porDeviceManageView/previousApplySubDetailSubmit', {
                    //     btn:['确认','取消'],
                    //     area: ['450px','310px'],
                    //     data:{
                    //         tableIns:POPUP_DATA.tableIns,
                    //         materialList: dataArr,
                    //         source: 'device'
                    //     },
                    //     yes: function(e){
                    //         $('#previousShipPorApplyInfoSubmit').find('#addPreviousShipPorApplySubmit').trigger('click');
                    //     }
                    // });
                }
            })

            // 删除 删除缓存
            $('#deleteStorge').on('click', function (d) {
                let checkStatus = table.checkStatus('applyStorgeList');
                if (checkStatus.data.length == 0) {
                    febs.alert.error('请选择一条数据!')
                    return false;
                }
                febs.modal.confirm('确认', '是否删除选中的数据', function () {
                    let storgeDatas = JSON.parse(localStorage.getItem('porDeviceInfos'));
                    let datas = [];
                    $.each(storgeDatas, function (i, v) {
                        let flg = false;
                        $.each(checkStatus.data, function (a, b) {
                            if (v.lineId == b.lineId && v.dstShipId == b.dstShipId) {
                                flg = true;
                            }
                        })
                        if (!flg) {
                            datas.push(v)
                        }
                    })
                    if (datas.length == 0) {
                        localStorage.removeItem('porDeviceInfos')
                    } else {
                        localStorage.setItem('porDeviceInfos', JSON.stringify(datas))
                    }
                    table.reload('applyStorgeList', {
                        data: datas,
                    })
                })
            })
            let applyStorgeHeight = 0;
            let searchHeight = 0;
            let pageHeight = 0;
            let wFlag = false;
            let sFlag = false;
            let top;
            $view.find('#transferMaterialTipsContent').resizable({
                containment: "#febs-porDeviceProtocolListManger",
                handles: "w,s",
                grid: 1,
                minWidth: 600,
                minHeight: 230,
                maxHeight: 700,
                start: function (event, ui) {
                    applyStorgeHeight = $view.find("#applyStorge").height();
                    searchHeight = $view.find("#searchDiv").height();
                    pageHeight = $view.find("#transferMaterialTipsContent").height();
                    top = $view.find("#transferMaterialTipsContent").css('top')
                    wFlag = $(event.originalEvent.target).hasClass('ui-resizable-w');
                    sFlag = $(event.originalEvent.target).hasClass('ui-resizable-s');
                },
                stop: function (event, ui) {

                },
                resize: function (event, ui) {
                    $view.find("#transferMaterialTipsContent").css('top', top)
                    let offsetTop = ui.originalSize.height - ui.size.height;
                    if (offsetTop !== 0 && sFlag) {
                        $view.find("#applyStorge").height(applyStorgeHeight - offsetTop)
                        $view.find("#applyStorge").find('.layui-table-main').height(applyStorgeHeight - offsetTop)
                    }
                    let offsetLeft = ui.originalSize.width - ui.size.width;
                    if (offsetLeft !== 0 && wFlag) {
                        let newSearchHeight = $view.find("#searchDiv").height();
                        $view.find("#transferMaterialTipsContent").height(pageHeight - searchHeight + newSearchHeight);
                    }
                }
            })

            $view.find('#queryOther1').on('click', function () {
                const params = getHeadQueryParams();
                table.reloadData(tableIns.config.id, {
                    where: params,
                    scrollPos: 'fixed'
                })
            })

            $view.find('#queryOther2').on('click', function () {
                var paramTwo = getLineQueryParams()
                table.reloadData(tableDetailIns.config.id, {
                    where: paramTwo,
                    scrollPos: 'fixed'
                })
            })

        });

    </script>
</body>

</html>