<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <th:block th:include="include::header('生产设计计划跟踪')" />
    <link rel="stylesheet" th:href="@{/febs/views/css/commonZs.css}" media="all">
    <style>
        html,
        body {
            height: 100%;
            overflow: hidden;
        }

        #ssPlanTrackView .nodeClass {
            width: 70px;
            height: 38px;
            text-align: center;
            line-height: 38px;
            background: #5eb2fbf2;
            border-radius: 5px;
            color: white;
            margin: 5px;
            position: relative;
        }

        .layui-layer-page .layui-layer-content {
            overflow: initial !important;
        }

        ::-webkit-scrollbar-thumb {
            border-radius: 500px !important;
            background-color: #9ea5a4 !important;
        }

        #ssPlanTrackView .layui-form-item .layui-form-checkbox[lay-skin=primary] {
            margin-top: 0px;
        }

        .layui-table-view .layui-table[lay-size=sm] .layui-table-cell {
            height: 30px;
            line-height: 28px;
            padding-top: 0px;
            padding-left: 11px;
            padding-right: 11px;
        }

        .layui-table-view .layui-table[lay-size=sm] td[data-field=realFinishDate] .layui-table-cell .layui-table-view .layui-table[lay-size=sm] td[data-field=planFinishDate] .layui-table-cell {
            padding-top: 0px;
        }

        #ssPlanTrackView .layui-input-block {
            margin-left: 0px;
            min-height: 36px;
        }

        td .layui-input {
            text-align: center;
            height: 30px;
        }


        #ssPlanTrackView .edited {
            background-color: #74b9ff !important;
        }

        #ssPlanTrackView .layui-form-item .layui-form-label {
            padding: 7px 10px;
            width: 100px;
        }

        table td .layui-input {
            border: none;
            background-color: transparent;
        }

        /* 为隔行变色定义CSS */
        .layui-table tbody tr:nth-child(odd) {
            background-color: #ffffff;
            /* 奇数行背景色 */
        }

        .layui-table tbody tr:nth-child(even) {
            background-color: #f2f2f2;
            /* 偶数行背景色 */
        }

        #ssPlanTrackView .layui-form-item .layui-form-label {
            padding: 5px 10px;
            width: 100px;
        }

        #headerForm .layui-form-label {
            width: 100px !important;
            line-height: 25px;
        }

        #headerForm .layui-btn {
            margin: 0 -5px;
        }

        #headerForm .layui-input-inline {
            width: 200px;
        }

        #headerForm .layui-inline {
            margin: 2px 0px;
        }

        .shipNoSelectText {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 150px;
            position: absolute;
            right: 10px;
        }

        #headerForm .layui-form-label {
            width: 72px !important;
        }

        #headerForm .layui-btn {
            margin: 0 1px;
        }

        #headerForm .layui-input-inline {
            width: 180px;
        }

        /* 高级检索按钮样式 */
        #advancedSearchBtn {
            padding: 0 8px !important;
            font-size: 12px !important;
            margin-left: 5px !important;
        }

        .shipNoText {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 150px;
            position: absolute;
            right: 10px;
        }

        #shipNoSelect xm-select>.xm-body {
            width: 350px;
        }

        .layui-layout-admin .febs-tabs-body {
            padding-bottom: 10px;
        }
    </style>
</head>

<body>
    <div class="layui-fluid layui-anim febs-anim page-body" id="ssPlanTrackView" lay-title="生产设计计划跟踪"
        style="height: calc( 100% - 10px);">
        <form class="layui-form search-form" id="headerForm">
            <div class="jhg-body-search">
                <!-- 第一行：基本检索条件 -->
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm">船号:</label>
                        <div class="layui-input-inline" style="width: 280px;">
                            <div id="shipNoSelect"></div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm">设计阶段:</label>
                        <div class="layui-input-inline">
                            <div id="stageSelect"></div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm">设计专业:</label>
                        <div class="layui-input-inline">
                            <div id="majorSelect"></div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm">图号/描述:</label>
                        <div class="layui-input-inline">
                            <input type="text" id="ddNo" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline" style="margin-left: 15px" shiro:hasPermission="ssPlanTrackView:view">
                        <div id="query" class="layui-btn searchBlue layui-btn-sm">
                            <em class="layui-icon">&#xe615;</em> 检索
                        </div>
                        <div id="advancedSearchBtn" class="layui-btn layui-btn-sm layui-btn-normal">
                            <em class="layui-icon">&#xe671;</em> 高级检索
                        </div>
                        <div id="queryOther" class="layui-hide">检索(假)</div>
                    </div>
                    <div class="layui-inline" shiro:hasPermission="ssPlanTrackView:edit">
                        <div id="import" class="layui-btn layui-btn-sm blueBtm">
                            <i class="layui-icon">&#xe7aa;</i> 导入
                        </div>
                    </div>
                    <div class="layui-inline" shiro:hasPermission="ssPlanTrackView:view">
                        <div id="export" class="layui-btn layui-btn-sm blueBtm">
                            <i class="layui-icon layui-icon-export"></i> 导出
                        </div>
                    </div>
                </div>

                <!-- 第二行：高级检索条件，默认隐藏 -->
                <div class="layui-form-item" id="advancedSearchRow" style="display: none;">
                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm">设计作业:</label>
                        <div class="layui-input-inline">
                            <div id="processSelect"></div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm">船型区域:</label>
                        <div class="layui-input-inline" style="width: 280px;">
                            <div id="regions"></div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm">图纸类型:</label>
                        <div class="layui-input-inline">
                            <div id="typeSelect"></div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm">作业人员:</label>
                        <div class="layui-input-inline">
                            <div id="ssWorkUserSelect"></div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
        <div class="layui-form-item" id="mainTable"
            style="width:100%;height:calc(100% - 55px);background-color: white;overflow: auto;">
            <div class="jhg-body-table">
                <table id="ssPlanTrackTable" lay-filter="ssPlanTrackTable" lay-data="{id: 'ssPlanTrackTable'}"></table>
            </div>
        </div>
    </div>

    <!-- 工具栏模板 -->
    <!--<script type="text/html" id="ssPlanTrackViewToolbar">-->
    <!--    <div class="layui-btn-container">-->
    <!--        <button type="button" class="layui-btn layui-btn-sm layui-bg-green" id="import" lay-event="import" shiro:hasPermission="ssPlanTrackView:edit">-->
    <!--            <i class="layui-icon">&#xe7aa;</i>导入-->
    <!--        </button>-->
    <!--        <button type="button" class="layui-btn layui-btn-sm layui-bg-blue" id="export" lay-event="export" shiro:hasPermission="ssPlanTrackView:view"><i-->
    <!--                class="layui-icon">&#xe601;</i>导出-->
    <!--        </button>-->
    <!--        <button type="button" class="layui-btn layui-btn-sm layui-bg-blue" id="saveBatch" lay-event="saveBatch" shiro:hasPermission="ssPlanTrackView:edit">-->
    <!--            <i class="layui-icon">&#xe7a9;</i>保存-->
    <!--        </button>-->
    <!--    </div>-->
    <!--</script>-->
    <th:block th:include="include::foot" />
    <script type="text/html" id="pageInfo">
    <a href="javascript:void (0)"  lay-event="totalHour" style="text-decoration: underline;">{{ d.totalHour.toFixed(2) }}</a>
</script>
    <script type="text/javascript" charset="utf-8 ">
        layui.config({
            base: ctx + 'febs/'
        }).extend({
            commonJS: 'lay/extends/common',
            febs: 'lay/modules/febs',
            validate: 'lay/modules/validate',
            jqueryui: 'lay/extends/jquery-ui.min',
            croppers: 'lay/cropper/croppers'
        }).use(['layer', 'form', 'laytpl', 'table', 'laydate', 'jquery', 'febs', 'commonJS', 'jqueryui', 'rpcJs'], function () {
            var $ = layui.jquery,
                laypage = layui.laypage,
                febs = layui.febs,
                rpcJs = layui.rpcJs,
                layer = layui.layer,
                laytpl = layui.laytpl,
                form = layui.form,
                table = layui.table,
                commonJS = layui.commonJS,
                laydate = layui.laydate,
                $view = $('#ssPlanTrackView'),
                tableIns,
                pwdMap = new Map(),
                pptdMap = new Map(),
                deptMap = new Map(),
                pdpdMap = new Map(),
                pdtdMap = new Map(),
                strMap = new Map(),
                shipRegionSelect,//船型区域
                ssWorkUserSelect,//作业人员
                majorSelect,
                shipId,
                processSelect,
                typeSelect,
                stageSelect,
                regions,
                regionsArr,
                shipNoSelect,
                shipTypeId,
                shipOwner,
                $searchForm = $view.find('#drawNo');
            var cols = [];
            form.render();
            let editFlg = currentUser.permissionSet.indexOf('ssPlanTrackView:edit') !== -1
            //初始化船号
            initDict();
            setShipNo();
            setSsWorkUser(null);
            setRegions([]);
            initTable();

            // 动态调整表格高度
            function adjustTableHeight() {
                setTimeout(function () {
                    var $headerForm = $('#headerForm');
                    var $mainTable = $('#mainTable');
                    var headerHeight = $headerForm.outerHeight();

                    // 根据搜索表单的实际高度动态调整表格容器高度
                    $mainTable.css('height', 'calc(100% - ' + (headerHeight - 45) + 'px)');

                    // 同时调整表格数据展示部分的高度
                    setTimeout(function () {
                        var mainTableHeight = $mainTable.height();
                        var $tableBody = $mainTable.find(".layui-table-body.layui-table-main");
                        if ($tableBody.length > 0) {
                            // 减去表格头部高度，通常是38px左右
                            var tableHeaderHeight = $mainTable.find(".layui-table-header").outerHeight() || 38;
                            var newTableBodyHeight = mainTableHeight - tableHeaderHeight;
                            $tableBody.css("height", newTableBodyHeight + "px");

                            // 触发layui表格的resize事件，确保表格正确重新渲染
                            if (typeof table !== 'undefined' && table.resize) {
                                table.resize('ssPlanTrackTable');
                            }
                        }
                    }, 50); // 稍微延迟确保容器高度已经更新
                }, 350); // 等待动画完成后调整
            }

            // 初始化时调整一次高度
            adjustTableHeight();

            // 高级检索按钮事件处理
            $('#advancedSearchBtn').on('click', function () {
                var $advancedRow = $('#advancedSearchRow');
                var $btn = $(this);

                if ($advancedRow.is(':visible')) {
                    // 当前显示，点击后隐藏
                    $advancedRow.slideUp(300, function () {
                        // 隐藏后调整表格高度（增加高度）
                        adjustTableHeight();
                    });
                    $btn.html('<em class="layui-icon">&#xe671;</em> 高级检索');
                    $btn.removeClass('layui-btn-danger').addClass('layui-btn-normal');
                } else {
                    // 当前隐藏，点击后显示
                    $advancedRow.slideDown(300, function () {
                        // 显示后调整表格高度（减少高度）
                        adjustTableHeight();
                    });
                    $btn.html('<em class="layui-icon">&#xe672;</em> 隐藏检索');
                    $btn.removeClass('layui-btn-normal').addClass('layui-btn-danger');
                }
            });

            //获取船型区域
            function getShipRegion(shipTypeId) {
                let arr = []
                febs.getSyn(ctx + 'basic/shipTypeArea/all', { shipTypeId: shipTypeId }, function (e) {
                    if (e.code == 200) {
                        strMap.clear()
                        $.each(e.data, function (i, v) {
                            strMap.set(v.strId, v.strCodeName)
                            arr.push({
                                name: v.strCodeName,
                                value: v.strId,
                                showname: v.strCode
                            })
                        })
                    }
                })
                return arr
            }

            //船号选择
            function setShipNo() {
                let arr = [];
                let resp = rpcJs.getShipDataList();
                if (resp.code == 200) {
                    allShips = resp.data;
                    $.each(resp.data, function (i, v) {
                        arr.push({
                            name: v.shipNo,
                            value: v.shipId,
                            showname: v.showName,
                            typeId: v.typeId,
                            shipOwner: v.shipOwner,
                        })
                    })

                }
                shipNoSelect = xmSelect.render({
                    el: '#shipNoSelect',
                    data: arr,
                    filterable: true,
                    template({ item }) {
                        return item.name + '<span class="shipNoSelectText" title="' + item.showname + '">' + item.showname + '</span>'
                    },
                    radio: true,
                    clickClose: true,
                    model: {
                        label: {
                            block: {
                                template(item, sels) {
                                    return item.name + '(' + item.showname + ')'
                                }
                            }
                        }
                    },
                    on: function (data) {
                        if (data.arr[0] != undefined) {
                            shipTypeId = data.arr[0].typeId
                            shipOwner = data.arr[0].shipOwner
                            let arr = getShipRegion(shipTypeId)
                            regionsArr = arr;
                            setRegions(arr);
                        }
                    }
                })
            }

            //设置船型区域
            function setRegions(arr) {
                regions = xmSelect.render({
                    el: '#regions',
                    data: arr,
                    filterable: true,
                    tips: arr.length === 0 ? "请先选择船号" : "请选择",
                    template({ item }) {
                        return item.name + '<span class="shipNoSelectText" title="' + item.showname + '">' + item.showname + '</span>'
                    },
                    radio: true,
                    clickClose: true,
                })
            }

            //设置生设作业人员
            function setSsWorkUser(pptdId) {
                let arr = []
                febs.postArraySync(ctx + 'plan/produceProfessionTypeDict/detailWork', { pptdId: pptdId }, function (data) {
                    if (data.code == 200) {
                        $.each(data.data, function (i, v) {
                            arr.push({
                                name: v.truename,
                                value: v.userId,
                            })
                        })
                    }
                })
                ssWorkUserSelect = xmSelect.render({
                    el: '#ssWorkUserSelect',
                    data: arr,
                    filterable: true,
                    tips: "请选择",
                    radio: true,
                    clickClose: true,
                })
            }

            //查询参数
            function getQueryParams() {
                shipId = shipNoSelect.getValue("value")[0];
                let ddNo = $view.find('#ddNo').val();
                let processSelected = processSelect.getValue('valueStr');
                let stageSelected = stageSelect.getValue('valueStr');
                let majorSelected = majorSelect.getValue('valueStr');
                let typeSelected = typeSelect.getValue('valueStr');
                let strSelected = regions.getValue('valueStr');
                let planInitFlg = '1'//计划已初始化数据
                let proChildren = majorSelect.getValue('children').length == 0 ? [] : majorSelect.getValue('children')[0].children;
                //0 父级别 1 子级别
                let proParentType = '';
                if (proChildren == undefined) {
                    proParentType = '1';
                } else {
                    proParentType = '0';
                }
                let workUserIdList = ssWorkUserSelect.getValue('value');
                return {
                    shipId: shipId,
                    designDrawNo: ddNo,
                    pdwId: processSelected,
                    pptdId: majorSelected,
                    pdpdId: stageSelected,
                    pdtdId: typeSelected,
                    strId: strSelected,
                    proParentType: proParentType,
                    planInitFlg: planInitFlg,
                    workUserIdList: workUserIdList
                }
            }
            var scrollPosition = 0;

            //查询
            $('#query').on('click', function () {
                if (shipNoSelect.getValue('value').length == 0) {
                    febs.alert.warn('请选择船号')
                    return false;
                } else {
                    scrollPosition = 0;
                    let params = getQueryParams();
                    tableIns.reload({
                        where: params,
                        url: ctx + 'plan/ssDrawPlanInfo/getSsPlanTrackDataList',
                        page: { curr: 1 }
                    });
                }
            })

            $view.find('#queryOther').on('click', function () {
                const params = getQueryParams()
                scrollPosition = $view.find('.layui-table-body').scrollTop()
                const p = $(".layui-laypage-skip").find("input").val();
                const s = $('.layui-laypage-limits').find("option:selected").val();
                if (($(".layui-laypage-skip").find("input").val()) === undefined) {
                    tableIns.reload({ where: params, page: { curr: 1, limit: s } });
                } else {
                    tableIns.reloadData({ where: params, page: { curr: p, limit: s } });
                }
            })

            function initTable() {
                tableIns = febs.table.init({
                    elem: '#ssPlanTrackTable',
                    id: 'ssPlanTrackTable',
                    height: '#mainTable-45',
                    // toolbar: '#ssPlanTrackViewToolbar',
                    defaultToolbar: [],
                    data: [],
                    sort: true,
                    autoSort: true,
                    cols: [
                        [
                            { type: 'checkbox' },
                            { field: 'shipNo', title: '船号', align: 'center', minWidth: 70, width: 70 },
                            { field: 'shipType', title: '船型', align: 'center', minWidth: 100 },
                            { field: 'shipOwner', title: '船东', align: 'center', minWidth: 100 },
                            {
                                field: 'strId', title: '区域', align: 'center', minWidth: 100, templet(d) {
                                    return strMap.get(d.strId) == null ? '船型区域已删除' : strMap.get(d.strId)
                                }
                            },
                            {
                                field: 'designDrawNo', title: '生产设计图号', sort: true, align: 'center', width: 100, templet(d) {
                                    return '<div style="text-align: left">' + d.designDrawNo + '</div>'
                                }
                            },
                            {
                                field: 'designDrawDesc', title: '生产设计图纸描述', align: 'center', minWidth: 140, templet(d) {
                                    return '<div style="text-align: left">' + d.designDrawDesc + '</div>'
                                }
                            },
                            { field: 'produceDate', title: '生产需求日', sort: true, align: 'center', minWidth: 100 },
                            {
                                field: 'drawWorkUserInfoList', title: '作业人员', align: 'center', minWidth: 150, templet(d) {
                                    if (d.drawWorkUserInfoList != null) {
                                        /*                                    let html = ''
                                                                            d.drawWorkUserInfoList.forEach(userInfo => {
                                                                                html += '<span class="username layui-badge-rim"> '+userInfo.truename+' </span>'
                                                                            })
                                                                            return html*/
                                        let truenameList = []
                                        d.drawWorkUserInfoList.forEach(userInfo => {
                                            truenameList.push(userInfo.truename);
                                        })
                                        return truenameList.join(',');
                                    } else {
                                        return ''
                                    }
                                }
                            },
                            {
                                field: 'pwdId', title: '生产设计类别', align: 'center', minWidth: 100, templet(d) {
                                    return pwdMap.get(d.pdwId) == null ? '类别已删除' : pwdMap.get(d.pdwId)
                                }
                            },
                            {
                                field: 'pdpdId', title: '生产设计阶段', align: 'center', minWidth: 100, templet(d) {
                                    return pdpdMap.get(d.pdpdId) == null ? '阶段已删除' : pdpdMap.get(d.pdpdId)
                                }
                            },
                            {
                                field: 'pdtdId', title: '生产图纸类型', align: 'center', minWidth: 90, templet(d) {
                                    return pdtdMap.get(d.pdtdId) == null ? '类型已删除' : pdtdMap.get(d.pdtdId)
                                }
                            },
                            {
                                field: 'planFinishDate', title: '计划结束日期', sort: true, align: 'center', minWidth: 115, templet: function (d) {
                                    return '<input id="PLANDATE-' + d.designDrawId + '" disabled class="layui-input laydate-demo"  value="' + (d.planFinishDate == null || d.planFinishDate == '' ? '' : commonJS.formatDate(new Date(d.planFinishDate), 'yyyy-MM-dd')) + '">'
                                }
                            },
                            {
                                field: 'realFinishDate', title: '实际完成日期', align: 'center', minWidth: 115, templet: function (d) {
                                    return '<input id="REALDATE-' + d.designDrawId + '"  class="layui-input laydate-demo"  value="' + (d.realFinishDate == null || d.realFinishDate == '' ? '' : commonJS.formatDate(new Date(d.realFinishDate), 'yyyy-MM-dd')) + '">'
                                }
                            },
                            {
                                field: 'deviation', title: '差值', align: 'center', minWidth: 80, templet: function (d) {
                                    if (d.deviation == null) {
                                        return ''
                                    }
                                    if (d.deviation < 0) {
                                        return '<span style="color: red">' + d.deviation + '</span>'
                                    }
                                    return d.deviation
                                }
                            },
                            {
                                fixed: 'right', field: 'pptdId', title: '生产设计专业', align: 'center', minWidth: 120, templet(d) {
                                    return pptdMap.get(d.pptdId) == null ? '设计专业已删除' : pptdMap.get(d.pptdId)
                                }
                            },
                            { fixed: 'right', field: 'totalHour', title: '作业总工时', align: 'center', minWidth: 120, style: 'color:#1b83f0;cursor: pointer', templet: '#pageInfo' },

                        ]
                    ],
                    page: true,
                    // limits: [10, 20, 30, 40, 50, 100],
                    limit: 1000,
                    done: function (res, curr, count) {
                        res.data.forEach(item => {
                            $view.find('.layui-table-body').scrollTop(scrollPosition);
                            $('#REALDATE-' + item.designDrawId).on('blur', function () {
                                $(this).closest('tr').css("background-color", "#fff")
                            })
                            laydate.render({
                                elem: '#REALDATE-' + item.designDrawId,
                                disabled: !editFlg,
                                max: 0,
                                position: 'absolute',
                                ready: function (date) {
                                    $(this.elem).closest('tr').css("background-color", "#9eff42")
                                    var inputElem = $(this.elem); // 输入框元素
                                    var inputLeft = inputElem.offset().left;
                                    var popupLeft = inputLeft + 104;
                                    $('.layui-laydate').css({
                                        left: popupLeft + 'px'
                                    })
                                },
                                done: function (value, date, endDate) {
                                    if (value === '') {
                                        item.realFinishDate = ''
                                    } else {
                                        item.realFinishDate = value + ' 00:00:00'

                                    }
                                    // $view.find('#REALDATE-' + item.designDrawId).closest('tr').addClass('edited');
                                    // let index2 = $view.find('#REALDATE-' + item.designDrawId).closest('tr').data('index');
                                    // table.setRowChecked('ssPlanTrackTable',{
                                    //   index: index2,
                                    //   checked:true
                                    // })
                                    saveRow(item);
                                    $(this.elem).closest('tr').css("background-color", "#fff")
                                }
                            });
                        })

                    }
                });

            }

            table.on('sort(ssPlanTrackTable)', function (d) {
                let field = d.field;
                let type = d.type;
                let order = d.type;
                if (shipNoSelect.getValue('value').length === 0) {
                    febs.alert.warn('请选择船号')
                    return false;
                } else {
                    let params = getQueryParams();
                    params.order = order;
                    params.field = field;
                    tableIns.reload({
                        where: params,
                        url: ctx + 'plan/ssDrawPlanInfo/getSsPlanTrackDataList',
                        page: { curr: 1 }
                    });
                }
            })


            $view.find('#saveBatch').on('click', function () {
                let flg = true;
                let phdpList = [];
                let saveData;
                let msg = '是否确定批量保存已修改的数据';
                let thisCache = table.cache['ssPlanTrackTable'] || {}
                let checkStatus = table.checkStatus("ssPlanTrackTable");
                if (checkStatus.data.length === 0) {
                    saveData = thisCache;
                    msg = '未勾选要保存的数据，将全部保存';
                } else {
                    saveData = checkStatus.data;
                }
                if (saveData === null || saveData.length === 0) {
                    febs.alert.warn('当前页面无数据，无需保存');
                    return false;
                }
                $.each(saveData, function (i, v) {
                    let data = v; // 获得当前行数据
                    if (!data.produceDate) {
                        febs.alert.warn('生产需求日不能为空');
                        flg = false;
                        return false;
                    }
                })
                let checkedList = $view.find('.layui-table-main').find('.layui-table-checked');
                if (checkStatus.data.length === 0) {
                    checkedList = $view.find('.layui-table-main').find('tr');
                }
                $.each(checkedList, function (i, checked) {
                    let arr = []
                    let $inputList = $(checked).find('input[name="cycle"]')
                    let phType = $(checked).find('td[data-field = phType]').find('.layui-table-cell').text();
                    let protocolDate = $(checked).find('input[name="day-protocol"]').val();
                    let protocolDays = $(checked).find('input[name="cycle-protocol"]').val();
                    if (phType == 0 && protocolDate == null && protocolDays.trim() == "") {
                        febs.alert.warn("协议/POR发放日为空")
                        flg = false;
                        return false;
                    }
                    let phdp = {
                        phId: $(checked).find('td[data-field = phId]').find('.layui-table-cell').text(),
                        shipId: $(checked).find('td[data-field = shipId]').find('.layui-table-cell').text(),
                        phType: phType,
                        produceDate: $(checked).find('td[data-field = produceDate]').find('.layui-table-cell').text(),
                        protocolDate: protocolDate,
                        protocolDays: protocolDays
                    }
                    $.each($inputList, function (i, v) {
                        if ($(v).val() !== '') {
                            let obj = {
                                xdndId: $(v).data('id'),
                                cycleDays: $(v).val()
                            }
                            arr.push(obj)
                        }
                    })
                    arr.sort((a, b) => a.cycleDays - b.cycleDays);
                    $.each(arr, function (i, v) {
                        v.sort = i
                    })
                    phdp.nodeList = arr;
                    phdpList.push(phdp);
                })
                console.log(phdpList);
                if (flg) {
                    febs.modal.confirm('批量保存', '是否确定批量保存已修改的周期', function () {
                        febs.postArray(ctx + 'por/porMaProductionNodePlan/updateBatchPorNodeCycleRule', phdpList, function (res) {
                            febs.alert.success('修改成功');
                            // 刷新最新的页面数据
                            tableIns.reload();
                        })
                    })
                }
            })

            $('#import').on('click', function () {
                importDetailDesignPlan();
            })
            $('#export').on('click', function () {
                exportDetailDesignPlan();
            })
            table.on('toolbar(ssPlanTrackTable)', function (obj) {
                switch (obj.event) {
                    case 'saveBatch':
                        saveBatch()
                        break;
                    case 'import':
                        importDetailDesignPlan()
                        break;
                    case 'export':
                        exportDetailDesignPlan()
                        break;
                }
            })
            function saveRow(item) {
                let params = []
                let param = {
                    designDrawId: item.designDrawId,
                    realFinishDate: item.realFinishDate,
                }
                params.push(param)
                febs.postArray(ctx + 'plan/ssDrawPlanInfo/saveBatchSsPlanTrack', params, function (d) {
                    if (d.code == '200') {
                        febs.alert.success("保存成功")
                        $view.find('#queryOther').trigger('click')
                        // tableIns.reloadData();
                    }
                })
            }
            function saveBatch() {
                let checkStatus = table.checkStatus('ssPlanTrackTable');
                if (checkStatus.data.length === 0) {
                    febs.alert.warn("请勾选需要保存的数据")
                    return false;
                }
                //plan/ssDrawPlanInfo/saveBatchSsPlanTrack
                let params = []
                $.each(checkStatus.data, function (i, v) {
                    let param = {
                        designDrawId: v.designDrawId,
                        realFinishDate: v.realFinishDate,
                    }
                    params.push(param)
                })
                febs.postArray(ctx + 'plan/ssDrawPlanInfo/saveBatchSsPlanTrack', params, function (d) {
                    if (d.code == '200') {
                        febs.alert.success("保存成功")
                        tableIns.reload();
                    }
                })
            }

            function importDetailDesignPlan() {
                let shipId = shipNoSelect.getValue("value")[0]
                if (shipId !== undefined) {
                    febs.modal.open('生设计划跟踪导入', 'plan/ssPlanTrackView/import', {
                        btn: ['开始上传'],
                        area: ['700px', '480px'],
                        data: { shipId: shipNoSelect.getValue('valueStr'), shipNo: shipNoSelect.getValue('nameStr'), shipOwner: shipOwner, shipTypeId: shipTypeId },
                        yes: function (e) {
                            $('#importPlanTrack').find('#test9').trigger('click');
                        }
                    });
                } else {
                    febs.alert.warn('请一个船号');
                    return false;
                }
            }

            function exportDetailDesignPlan() {
                let shipId = shipNoSelect.getValue("value")[0]
                if (shipId !== undefined) {
                    febs.download(ctx + 'plan/ssDrawPlanInfo/exportSsDesignPlanTrackTemplate', { shipId: shipNoSelect.getValue('valueStr'), shipNo: shipNoSelect.getValue('nameStr'), shipOwner: shipOwner, shipTypeId: shipTypeId }, shipNoSelect.getValue('nameStr') + '生设计划跟踪导出' + new Date().getTime() + '.xlsx');// 2024-06-06新导入
                } else {
                    febs.alert.warn('请输入一个船号');
                }
            }

            //触发单元格工具事件
            table.on('tool(ssPlanTrackTable)', function (obj) { // 双击 toolDouble
                userEdit(obj)
            });


            function userEdit(obj) {
                let data = obj.data;
                data.xsDesignDrawId = data.designDrawId;
                febs.modal.open('生产设计专业作业人员信息', 'plan/detailDesignCycleMakeView/userDetail', {
                    area: ['1200px', '700px'],
                    data: {
                        drawInfo: data,
                        professionId: data.pptdId,
                        designType: "1",
                        type: '1'
                    },
                });
            }



            //初始化字典数据
            function initDict() {
                febs.get(ctx + 'plan/produceDesignWorkDict/all', {}, function (e) {
                    if (e.code == 200) {
                        let arr = []
                        $.each(e.data, function (i, v) {
                            pwdMap.set(v.pdwId, v.pdwCodeName)
                            arr.push({
                                name: v.pdwCodeName,
                                value: v.pdwId,
                                code: v.pdwCode
                            })
                        })
                        processSelect = xmSelect.render({
                            el: '#processSelect',
                            data: arr,
                            radio: true,
                            filterable: true,
                            template({ item }) {
                                return item.name + '<span style="position: absolute;right: 10px;color: #90a4ae">' + item.code + '</span>'
                            },
                            clickClose: true,
                        })
                    }
                })

                febs.get(ctx + 'plan/produceProfessionTypeDict/all', {}, function (e) {
                    if (e.code == 200) {
                        $.each(e.data, function (i, v) {
                            pptdMap.set(v.pptdId, v.pptdCodeName)
                            deptMap.set(v.pptdId, v.deptId)
                        })
                    }
                })
                febs.get(ctx + 'plan/produceProfessionTypeDict/tree', {}, function (e) {
                    if (e.code == 200) {
                        majorSelect = xmSelect.render({
                            el: '#majorSelect',
                            data: e.data,
                            radio: true,
                            filterable: true,
                            clickClose: true,
                            tree: {
                                show: true,
                                strict: false,

                            },
                            on: function (data) {
                                if (data.isAdd) {
                                    setSsWorkUser(data.arr[0].value)
                                } else {
                                    setSsWorkUser(null)
                                }
                            }

                        })
                    }
                })
                febs.get(ctx + 'plan/produceDesignProgressDict/all', {}, function (e) {
                    if (e.code == 200) {
                        let arr = []
                        $.each(e.data, function (i, v) {
                            pdpdMap.set(v.pdpdId, v.pdpdCodeName)
                            arr.push({
                                name: v.pdpdCodeName,
                                value: v.pdpdId,
                                code: v.pdpdCode
                            })
                        })
                        stageSelect = xmSelect.render({
                            el: '#stageSelect',
                            data: arr,
                            radio: true,
                            filterable: true,
                            template({ item }) {
                                return item.name + '<span style="position: absolute;right: 10px;color: #90a4ae">' + item.code + '</span>'
                            },
                            clickClose: true,
                        })
                    }
                })
                febs.get(ctx + 'plan/produceDrawTypeDict/getAll', {}, function (e) {
                    if (e.code == 200) {
                        let arr = []
                        $.each(e.data, function (i, v) {
                            pdtdMap.set(v.pdtId, v.pdtCodeName)
                            arr.push({
                                name: v.pdtCodeName,
                                value: v.pdtId,
                                code: v.pdtCode
                            })
                        })
                        typeSelect = xmSelect.render({
                            el: '#typeSelect',
                            data: arr,
                            radio: true,
                            filterable: true,
                            template({ item }) {
                                return item.name + '<span style="position: absolute;right: 10px;color: #90a4ae">' + item.code + '</span>'
                            },
                            clickClose: true,
                        })
                    }
                })
            }

        });

    </script>
</body>

</html>