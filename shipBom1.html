<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns="http://www.w3.org/1999/html" xmlns:shiro="http://www.w3.org/1999/xhtml"
    lang="en">
<style>
    .layui-table-view .layui-table[lay-size=sm] td[data-field=planFinishDate] .layui-table-cell {
        padding-top: 0;
    }
</style>

<head>
    <th:block th:include="include::header('BOM管理')" />
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/ui-lightness/jquery-ui.css">
    <style>
        #febs-shipBomManger .yfs-list {
            background-color: #f7f7f7;
            height: 100%;
        }

        #febs-shipBomManger .ztree li span.button.add {
            margin-left: 2px;
            margin-right: -1px;
            background-position: -144px 0;
            vertical-align: top;
        }

        #febs-shipBomManger .ztree li a {
            height: 25px;
        }

        #febs-shipBomManger .ztree * {
            padding: 0;
            margin: 0;
            font-size: 16px;
            font-family: <PERSON>erd<PERSON>, Arial, Helvetica, AppleGothic, sans-serif;
        }

        #febs-shipBomManger .button .ico_open {
            width: 20px;
            height: 20px;
        }

        #febs-shipBomManger .tree-selected {
            color: #2F9688;
            font-weight: bold;
        }

        .layui-table tbody tr:hover,
        .layui-table-hover {
            background-color: #F8F8F8;
        }

        .layui-table-checked.layui-table-hover {
            background-color: #74b9ff !important;
        }

        #detail .layui-form-item {
            margin-bottom: 0;
            padding-top: 10px;
            padding-bottom: 5px;
        }

        #detail .layui-form-label {
            position: relative;
            float: left;
            display: block;
            padding: 9px 5px;
            width: 98px;
            font-weight: 400;
            line-height: 20px;
            text-align: right;
        }

        #febs-shipBomManger .right-style {
            z-index: 999;
            display: none;
            position: fixed;
            padding-left: 15px;
            padding-right: 15px;
            padding-bottom: 10px;
            background-color: #FBFBFB;
            border: 2px solid #F0F0F0;
            cursor: pointer;
        }

        #febs-shipBomManger .edited {
            background-color: #74b9ff !important;
        }

        .por .layui-icon:after {
            content: '\e697';
            color: #16b777;
        }

        .detailBgColor {
            background-color: #9df50a !important;
        }


        #febs-shipBomManger .two p:hover {
            background-color: mediumslateblue;
        }

        .two {
            display: none;
            position: absolute;
            z-index: 3;
            width: 100px
        }

        #searchForm .layui-form-label {
            width: 80px !important;
            line-height: 25px;
        }

        #searchForm .layui-input-inline {
            width: 200px;
        }

        .layui-form-item .layui-inline {
            margin: 0;
        }

        /* 为隔行变色定义CSS */
        .layui-table tbody tr:nth-child(odd) {
            background-color: #ffffff;
            /* 奇数行背景色 */
        }

        .layui-table tbody tr:nth-child(even) {
            background-color: #f2f2f2;
            /* 偶数行背景色 */
        }

        /*.layui-table-header .layui-table {*/
        /*    margin-top: 5px;*/
        /*}*/
        .layui-table-header {
            height: 32px;
        }

        .shipNoSelectText {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 180px;
            position: absolute;
            right: 10px;
        }

        #shipNoSelect xm-select>.xm-body {
            width: 350px;
        }

        #febs-shipBomManger .layui-input[disabled],
        .layui-textarea[disabled] {
            border: none;
            background: transparent;
        }

        #febs-shipBomManger .layui-table-tool-temp {
            padding-right: 0 !important;
        }

        #febs-shipBomManger .layui-table-tool {
            padding-right: 0 !important;
        }

        #febs-shipBomManger .layui-btn-container .layui-btn {
            margin-right: 5px !important;
        }

        #febs-shipBomManger .jhg-body-search .layui-form-item .layui-form-label {
            padding: 3px 0 !important;
            width: auto;
        }

        .layui-table-header {
            border-width: 1px 0 0 0;
        }

        #febs-shipBomManger .search-form {
            padding-bottom: 0 !important;
        }

        .blueTag {
            float: left;
            width: 3px;
            background-color: #1E9FFF !important;
            display: inline-block;
            height: 20px;
            margin-left: 10px;
            margin-top: 5px;
            margin-right: 2px;
        }

        .title-font-bom {
            float: left;
            color: #1e1e1e;
            font-size: 16px;
            font-weight: 600;
            font-family: auto;
            height: 30px;
        }

        #febs-shipBomManger .jhg-body-search .layui-form-item {
            margin-bottom: 0;
            padding-top: 10px;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            /*justify-content: space-evenly;*/
            height: 40px;
            line-height: 30px;
        }

        .layui-table-tool {
            padding: 10px !important;
        }

        td .layui-input {
            border: none;
        }

        /*侧边栏伸缩*/
        .sidebar {
            position: absolute;
            height: calc(100% - 10px);
            left: 0;
            transition: transform 0.3s ease;
        }

        .sidebar.collapsed {
            transform: translateX(-250px);
            /* 侧边栏收缩时的位置 */
        }

        .main-content {
            margin-left: 250px;
            width: 85%;
            height: 100%;
            transition: margin-left 0.3s ease;
        }

        .sidebar.collapsed+.main-content {
            margin-left: 0;
            /* 侧边栏收缩时，主内容区宽度自适应 */
        }

        .show_slide {
            width: 15px;
            height: 55px;
            background-color: rgb(24, 144, 255);
            border-radius: 8px 0 0 8px;
            line-height: 55px;
            text-align: left;
            position: absolute;
            bottom: 50%;
            cursor: pointer;
            color: #ffff;
            left: 250px;
        }

        /* jQuery UI resizable 样式 */
        .ui-resizable-handle {
            position: absolute;
            font-size: 0.1px;
            display: block;
            -ms-touch-action: none;
            touch-action: none;
        }

        .ui-resizable-s {
            cursor: s-resize;
            height: 7px;
            width: 100%;
            bottom: -5px;
            left: 0;
            background: linear-gradient(to bottom, transparent, #ddd);
            transition: background 0.2s ease;
        }

        .ui-resizable-s:hover {
            background: linear-gradient(to bottom, transparent, #1890ff);
        }

        .ui-resizable-s::after {
            content: '';
            position: absolute;
            bottom: 2px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 2px;
            background: #999;
            border-radius: 1px;
        }

        .ui-resizable-s:hover::after {
            background: #1890ff;
        }

        /* 表格容器样式 - 确保工具栏固定在顶部 */
        .table-container {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .table-container .layui-table-tool {
            flex-shrink: 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            background-color: #fff;
            border-bottom: 1px solid #e6e6e6;
        }

        .table-container .table-scroll-area {
            flex: 1;
            overflow-y: auto;
        }

        /* 隐藏layui表格内部的滚动条，使用外层容器滚动 */
        .table-container .layui-table-body.layui-table-main {
            overflow-y: hidden !important;
        }

        /* 表格容器结构 */
        #detailBomTable,
        #changeTableId,
        #beforeShipTableId {
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        /* 表格内容区域 - 横向滚动条在这一层 */
        #detailBomTable .table-content,
        #changeTableId .table-content,
        #beforeShipTableId .table-content {
            flex: 1;
            overflow-y: auto;
            overflow-x: auto;
            min-height: 0;
            position: relative;
        }

        /* 禁用layui表格内部的所有滚动条 */
        #detailBomTable .layui-table-view,
        #changeTableId .layui-table-view,
        #beforeShipTableId .layui-table-view {
            overflow: visible !important;
        }

        #detailBomTable .layui-table-box,
        #changeTableId .layui-table-box,
        #beforeShipTableId .layui-table-box {
            overflow: visible !important;
        }

        #detailBomTable .layui-table-header,
        #changeTableId .layui-table-header,
        #beforeShipTableId .layui-table-header {
            overflow: visible !important;
        }

        #detailBomTable .layui-table-body,
        #changeTableId .layui-table-body,
        #beforeShipTableId .layui-table-body {
            overflow: visible !important;
        }

        #detailBomTable .layui-table-body.layui-table-main,
        #changeTableId .layui-table-body.layui-table-main,
        #beforeShipTableId .layui-table-body.layui-table-main {
            overflow: visible !important;
        }

        /* 让表格宽度自适应内容，确保横向滚动条出现在table-content层 */
        #detailBomTable .layui-table,
        #changeTableId .layui-table,
        #beforeShipTableId .layui-table {
            min-width: 100%;
            width: max-content;
        }

        #detailBomTable .layui-table-view,
        #changeTableId .layui-table-view,
        #beforeShipTableId .layui-table-view {
            width: max-content;
            min-width: 100%;
        }

        #detailBomTable .layui-table-header,
        #detailBomTable .layui-table-body,
        #changeTableId .layui-table-header,
        #changeTableId .layui-table-body,
        #beforeShipTableId .layui-table-header,
        #beforeShipTableId .layui-table-body {
            width: max-content;
            min-width: 100%;
        }

        /* 美化table-content层的滚动条 */
        #detailBomTable .table-content::-webkit-scrollbar,
        #changeTableId .table-content::-webkit-scrollbar,
        #beforeShipTableId .table-content::-webkit-scrollbar {
            height: 5px;
            width: 5px;
        }

        #detailBomTable .table-content::-webkit-scrollbar-track,
        #changeTableId .table-content::-webkit-scrollbar-track,
        #beforeShipTableId .table-content::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        #detailBomTable .table-content::-webkit-scrollbar-thumb,
        #changeTableId .table-content::-webkit-scrollbar-thumb,
        #beforeShipTableId .table-content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 6px;
        }

        #detailBomTable .table-content::-webkit-scrollbar-thumb:hover,
        #changeTableId .table-content::-webkit-scrollbar-thumb:hover,
        #beforeShipTableId .table-content::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }

        /* 工具栏固定在顶部 */
        #detailBomTable .layui-table-tool,
        #changeTableId .layui-table-tool,
        #beforeShipTableId .layui-table-tool {
            position: sticky;
            top: 0;
            z-index: 1000;
            background-color: #fff;
            border-bottom: 1px solid #e6e6e6;
            flex-shrink: 0;
        }
    </style>
    <title></title>
</head>

<body>
    <div class="layui-fluid layui-anim febs-anim page-body" id="febs-shipBomManger" lay-title="BOM管理"
        style=" height:calc(100% - 10px);">
        <div class="layui-row" style="display: flex;flex-direction: column;height: 100%">
            <div class="layui-form" style="display: flex;height: 100%">
                <div id='mymenu' class="right-style"></div>
                <div style="width:250px;background-color: white;margin: 3px;" class="sidebar">
                    <div style="padding: 5px;">
                        <div class="layui-inline">
                            <div class="layui-input-inline" style="width: 240px;margin: 2px 0">
                                <div id="shipNoSelect"></div>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <div class="layui-input-inline" style="width: 150px;margin: 3px 0;">
                                <label for="keyword"></label><input id="keyword" type="text" name="keyword"
                                    autocomplete="off" class="layui-input" style="height: 30px" placeholder="请输入分段节点">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <div id="queryKeyword" class="layui-btn searchBlue layui-btn-sm">
                                <em class="layui-icon">&#xe615;</em> 检索
                            </div>
                        </div>
                    </div>
                    <div id="treeBom" style="overflow: auto;max-height: 88vh"></div>

                </div>
                <div class="layui-form main-content" style="display: flex;flex-wrap: wrap;">
                    <div id="showSlide" class="show_slide">
                        <i class="layui-icon layui-icon-left" style="font-size: 20px;" id="showIcon"></i>
                    </div>
                    <div id="mainBomTableContainer"
                        style="width: 100%; background-color: white;margin: 3px; height: 30%;">
                        <div id="mainBomTable"
                            style="width: 100%; height: 100%; display: flex; flex-direction: column;">
                            <form class="layui-form search-form" id="searchForm" style="flex-shrink: 0;">
                                <div class="jhg-body-search">
                                    <div class="layui-form-item">
                                        <label class="blueTag"></label>
                                        <label class="title-font-bom">BOM信息</label>

                                        <div class="layui-inline">
                                            <!--                                    <label class="layui-form-label" style="width: 110px !important;">BOM编号/描述：</label>-->
                                            <label class="layui-form-label" style="width: 110px !important;"></label>
                                            <div class="layui-input-inline">
                                                <label for="sbBomDesc"></label><input type="text" name="porHeadNo"
                                                    autocomplete="off" class="layui-input" style="height: 30px"
                                                    id="sbBomDesc" placeholder="BOM编号/描述：">
                                            </div>
                                        </div>
                                        <div class="layui-inline">
                                            <!--                                    <label class="layui-form-label">计划完成日:</label>-->
                                            <div class="layui-inline">
                                                <label for="planFinishDate"></label><input type="text"
                                                    class="layui-input" name="startTime" id="planFinishDate"
                                                    placeholder="计划完成日:" autocomplete="off" style="height: 30px">
                                            </div>
                                        </div>
                                        <div class="layui-inline">
                                            <div id="query" class="layui-btn searchBlue layui-btn-sm">
                                                <em class="layui-icon">&#xe615;</em> 检索
                                            </div>
                                        </div>
                                        <div class="layui-inline" shiro:hasPermission="febs-shipBomManger:edit">
                                            <div id="bomAdd" class="layui-btn layui-btn-sm blueBtm">
                                                <i class="layui-icon layui-icon-add-1"></i>
                                                新增
                                            </div>
                                        </div>
                                        <div class="layui-inline hideBtn" shiro:hasPermission="febs-shipBomManger:edit">
                                            <div class="layui-btn layui-btn-small blueBtm">
                                                <i class="layui-icon">&#xe7aa;</i> 导入
                                            </div>
                                            <div class="two" style="background-color: #1b83f0;">
                                                <p style="color: white;padding: 8px;" id="import2">船体BOM钢板</p>
                                                <p style="color: white;padding: 8px;" id="importBar">船体BOM型材</p>
                                            </div>
                                        </div>

                                        <div class="layui-inline hideBtn">
                                            <div class="layui-btn layui-btn-small blueBtm">
                                                <i class="layui-icon">&#xe7aa;</i> 导出
                                            </div>
                                            <div class="two" style="background-color: #1b83f0;">
                                                <p style="color: white;padding: 8px;" id="exportSteel">船体BOM钢板</p>
                                                <p style="color: white;padding: 8px;" id="exportBar">船体BOM型材</p>
                                            </div>
                                        </div>
                                        <div class="layui-inline" shiro:hasPermission="febs-shipBomManger:edit">
                                            <div id="comfirm" class="layui-btn blueBtm layui-btn-sm">
                                                <i class="layui-icon layui-icon-ok"></i> 托盘确认
                                            </div>
                                        </div>
                                        <div class="layui-inline" shiro:hasPermission="febs-shipBomManger:edit">
                                            <div id="backBom" class="layui-btn layui-bg-red layui-btn-sm">
                                                <i class="layui-icon layui-icon-delete"></i> 撤销确认
                                            </div>
                                        </div>
                                        <div class="layui-inline" shiro:hasPermission="febs-shipBomManger:edit">
                                            <div id="delBom" class="layui-btn layui-bg-red layui-btn-sm">
                                                <i class="layui-icon layui-icon-delete"></i> 删除
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                            <div class="jhg-body-table" style="flex: 1; overflow-y: auto;">
                                <table class="layui-hide" id="bomList">
                                </table>
                            </div>
                        </div>
                    </div>
                    <div id="bottomTablesContainer" style="width: 100%; height: 70%; display: flex;">
                        <div id="detailBomTableContainer"
                            style="width: 53%;background-color: white; margin: 3px; display: flex; flex-direction: column;">
                            <div id="detailBomTable" style="flex: 1;">
                                <div class="table-content">
                                    <table class="layui-hide" id="detailBomList">
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div id="rightTablesContainer"
                            style="width: 46%; margin: 3px;display: flex;flex-direction: column;box-sizing: border-box;">

                            <div id="changeTableContainer"
                                style="height: 40%;background-color: white; display: flex; flex-direction: column;">
                                <div id="changeTableId" style="flex: 1;">
                                    <div class="table-content">
                                        <table class="layui-hide" id="changeTable">
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div id="beforeShipTableContainer"
                                style="margin-bottom: 3px;height: 60%;background-color: white; display: flex; flex-direction: column;">
                                <div id="beforeShipTableId" style="flex: 1;">
                                    <div class="table-content">
                                        <table class="layui-hide" id="beforeShipTable">
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>


            </div>
        </div>
    </div>

    <!-- 工具栏模板 -->
    <script type="text/html" id="paintPeBlockRestToolbar">
    <div class="layui-btn-container" style="display: flex; align-items: center; flex-wrap: nowrap;">
        <label style="width: 3px; background-color: #1E9FFF !important; display: inline-block; height: 20px; margin-top: 5px; margin-right: 2px;"></label>
        <label class="title-font" style="color: #1e1e1e;font-size: 16px;font-weight: 600;font-family: auto; margin-right: 10px;">BOM使用余料</label>
        <!--        <label class="layui-form-label" style="width: 110px;padding: 5px 0;">物资编码/描述：</label>-->
        <label class="layui-form-label" style="width: 110px;padding: 5px 0;"></label>
        <div class="layui-input-inline" style="width: 150px !important; margin-right: 10px;">
            <div id="wuziylNo"><label for="wuziylNoSur"></label><input type="text" name="porHeadNo" autocomplete="off" class="layui-input" style="height: 30px" id="wuziylNoSur" placeholder="物资编码/描述:"></div>
        </div>
        <button class="layui-btn searchBlue layui-btn-sm" style="margin-right: 5px;" lay-event="querySurplus">
            <em class="layui-icon">&#xe615;</em> 检索
        </button>
        <button class="layui-btn layui-btn-sm blueBtm" lay-event="addSurplusBtn" style="margin-right: 5px;">
            <i class="layui-icon layui-icon-add-1"></i>添加
        </button>
        <button type="button" class="layui-btn layui-bg-red layui-btn-sm" lay-event="delSurplus">
            <i class="layui-icon layui-icon-delete"></i>删除
        </button>
    </div>
</script>
    <!-- 工具栏模板 -->
    <script type="text/html" id="paintPeBlockMatchToolbar">
    <div class="layui-btn-container" style="display: flex; align-items: center; flex-wrap: nowrap; padding-right: 0 !important;">
        <label style="width: 3px; background-color: #1E9FFF !important; display: inline-block; height: 20px; margin-top: 5px; margin-right: 2px;"></label>
        <label class="title-font" style="color: #1e1e1e; font-size: 16px; font-weight: 600; font-family: auto; margin-right: 10px;">BOM物资</label>
        <!--            <label class="layui-form-label" style="width: 140px;padding: 5px 0;">物资编码/描述：</label>-->
        <!--            <label class="layui-form-label" style="width: 140px;padding: 5px 0;"></label>-->
        <div class="layui-input-inline" style="width: 150px !important; margin-right: 10px;" >
            <div id="wuziNo">
                <label for="wuziNoIpt"></label><input id="wuziNoIpt" type="text" name="porHeadNo" autocomplete="off" class="layui-input" style="height: 30px" placeholder="物资编码/描述:">
            </div>
        </div>
        <div class="layui-input-inline" style="margin-right: 10px;">
            <div id="tlhNo">
                <label for="tlhNoIpt"></label><input id="tlhNoIpt" type="text" autocomplete="off" class="layui-input" style="height: 30px" placeholder="套料号:">
            </div>
        </div>
        <button class="layui-btn searchBlue layui-btn-sm" style="margin-right: 5px;" lay-event="queryBomMaterial" type="button">
            <em class="layui-icon">&#xe615;</em> 检索
        </button>
        <button class="layui-btn layui-btn-sm blueBtm"  lay-event="addSteelBtn" style="margin-right: 5px;">
            <i class="layui-icon layui-icon-add-1"></i>添加
        </button>
        <button class="layui-btn layui-btn-sm blueBtm"  lay-event="exportRest" style="margin-right: 5px;">
            <i class="layui-icon layui-icon-add-1"></i>生成余料
        </button>
        <button type="button" class="layui-btn layui-bg-red layui-btn-sm" lay-event="delWuZiBtn">
            <i class="layui-icon layui-icon-delete"></i>删除
        </button>
    </div>
</script>
    <script type="text/html" id="beforeToolbar">
    <div class="layui-btn-container" style="display: flex; align-items: center; flex-wrap: nowrap;">
        <label style="width: 3px; background-color: #1E9FFF !important; display: inline-block; height: 20px; margin-top: 5px; margin-right: 2px;"></label>
        <label class="title-font" style="color: #1e1e1e;font-size: 16px;font-weight: 600;font-family: auto; margin-right: 10px;">BOM使用前船物资</label>
        <label class="layui-form-label" style="width: 110px;padding: 5px 0;"></label>
        <div class="layui-input-inline" style="width: 150px !important; margin-right: 10px;">
        </div>
        <button class="layui-btn layui-btn-sm blueBtm" lay-event="addBefore" style="margin-right: 5px;">
            <i class="layui-icon layui-icon-add-1"></i>添加
        </button>
        <button type="button" class="layui-btn layui-bg-red layui-btn-sm" lay-event="delBefore">
            <i class="layui-icon layui-icon-delete"></i>删除
        </button>
    </div>
</script>
    <th:block th:include="include::foot" />
    <script data-th-inline="none" type="text/javascript">
        layui.config({
            base: ctx + 'febs/'
        }).extend({
            febs: 'lay/modules/febs',
            validate: 'lay/modules/validate',
            formSelects: 'lay/extends/formSelects-v4.min',
            jqueryui: 'lay/extends/jquery-ui.min',
            echarts: 'lay/extends/echarts.min',
            commonJS: 'lay/extends/common'
        }).use(['tree', 'jquery', 'validate', 'table', 'laypage', 'form', 'febs', 'commonJS', 'dropdown', 'rpcJs', 'laydate', 'jqueryui'], function () {
            var $ = layui.jquery,
                $view = $('#febs-shipBomManger'),
                febs = layui.febs,
                form = layui.form,
                table = layui.table,
                tree = layui.tree,
                commonJS = layui.commonJS,
                dropdown = layui.dropdown,
                rpcJs = layui.rpcJs,
                laydate = layui.laydate,
                treeData,
                shipNoSelect,
                allShips = [],
                //材料使用类型
                // useType ,
                shipId,
                tableIns,
                block,
                bmNo,
                bmIds = [],
                changeTable,
                tableInsDetail_beforeShip,
                maTypeArr = [],
                tp = null,
                sbiId = null,
                sbbiId = null,
                reloadParamForExcel,
                bomlistRow,
                treeClickNode,
                bomBlockId,
                bomBmId,
                tableInsDetail;
            const $wuziylNoSur = $('#wuziylNoSur');
            var status = '';
            var blockMap = new Map();
            var bmMap = new Map();
            let editFlg = currentUser.permissionSet.indexOf('febs-shipBomManger:edit') !== -1
            const layoutShipId = localStorage.getItem("gd-layout-shipId");
            form.render();
            autoHeight();
            $(window).resize(function () {
                autoHeight();
            })
            setShipNo();

            initTable();
            function autoHeight() {
                //选项卡tab高度
                const $febsTW = $(".febs-tabs-wrap"),
                    //标题头的高度
                    $appH = $("#app-header"),
                    tabHeight = $febsTW.height() == null ? "0" : $febsTW.height(),
                    appHeight = $appH.height() == null ? "0" : $appH.height(),
                    diff = tabHeight === 0 ? 16 : 52;
                $view.find('.yfs-body').height($(window).height() - tabHeight - appHeight - diff);
            }
            $(document).ready(function () {
                const $showIcon = $('#showIcon');
                $('#showSlide').click(function () {
                    var sidebar = $('.sidebar');
                    if (sidebar.hasClass('collapsed')) {
                        sidebar.removeClass('collapsed');
                        $('.main-content').css({
                            width: '85%'
                        })
                        $('.show_slide').css({
                            left: '240px'
                        })
                        $showIcon.addClass('layui-icon-left')
                        $showIcon.removeClass('layui-icon-right')
                    } else {
                        $('.show_slide').css({
                            left: '0'
                        })
                        $('.main-content').css({
                            width: '100%'
                        })
                        $showIcon.removeClass('layui-icon-left')
                        $showIcon.addClass('layui-icon-right')
                        sidebar.addClass('collapsed');
                    }
                    autoHeight()

                });
            });

            // //获取材料使用类型
            febs.get(ctx + 'por/porShipBomMaterialDict/all', {}, function (e) {
                if (e.code === 200 && null != e.data && e.data.length > 0) {
                    e.data.forEach(item => {
                        maTypeArr.push(item.psbmId);
                    })
                }
            })




            $view.find('#queryKeyword').on('click', function () {
                if (treeData === undefined) {
                    febs.alert.warn('请选择船号进行检索');
                    return false;
                }
                let keyword = $view.find('input[name=keyword]').val().trim();
                // console.log(keyword)
                if (keyword === '') {
                    tree.reload('demoId', {
                        data: treeData
                    });
                } else {
                    // 自定义搜索逻辑
                    var newData = searchTree(treeData, keyword);
                    // 更新树形控件的数据
                    tree.reload('demoId', {
                        data: newData
                    });
                }
            })

            //开始时间
            laydate.render({
                elem: '#planFinishDate',
                type: 'date',
                trigger: 'click',
                done: function (value, date, endDate) {
                    if (value === "") {
                        febs.alert.warn("请选择日期")
                        return false
                    }
                }
            });

            /**
             * 自定义搜索函数
             * @param data 树数据
             * @param keyword 搜索关键词
             * @returns {*[]}
             */
            function searchTree(data, keyword) {
                var res = [];
                function search(nodes) {
                    nodes.forEach(function (item) {
                        if (item.name === keyword) {
                            // item.spread  = true;
                            res.push(item); // 符合条件的节点
                        }
                        if (item.children && item.children.length > 0) {
                            search(item.children); // 递归搜索子节点
                        }
                    });
                }
                search(data);
                return res;
            }
            //船号选择
            function setShipNo() {
                let arr = [];
                let resp = rpcJs.getShipDataList();
                if (resp.code === 200) {
                    allShips = resp.data;
                    $.each(resp.data, function (i, v) {
                        arr.push({
                            name: v.shipNo,
                            value: v.shipId,
                            showName: v.showName
                        })
                    })
                }
                shipNoSelect = xmSelect.render({
                    el: '#shipNoSelect',
                    data: arr,
                    filterable: true,
                    tips: '请选择船号',
                    initValue: [!!layoutShipId ? layoutShipId : ''],
                    template({ item }) {
                        return item.name + '<span class="shipNoSelectText" title="' + item.showName + '">' + item.showName + '</span>'
                    },
                    model: {
                        label: {
                            block: {
                                template(item, sels) {
                                    return item.name + '(' + item.showName + ')'
                                }
                            }
                        }
                    },
                    radio: true,
                    clickClose: true,
                    on: function (data) { //根据船号渲染左侧树
                        shipId = data.change[0].value;
                        febs.get(ctx + 'bom/shipBomInfo/getBlockBomTreeList', { shipId: shipId }, function (data) {
                            if (data.code === 200) {
                                treeData = data.data;
                                initTable();
                                createSurplusTable([], 0);
                                treeData.forEach(blockList => {
                                    blockMap.set(blockList.id, blockList.name)
                                    blockList.children.forEach(bmList => {
                                        if (bmList.children != null && bmList.children.length > 0) {
                                            bmList.children.forEach(bm => {
                                                bmMap.set(bm.id, bm.name)
                                            })
                                        }
                                    })
                                })
                                console.log('bmMap===>', bmMap)
                                // 树组件渲染
                                tree.render({
                                    elem: "#treeBom",
                                    id: 'demoId',
                                    data: treeData,
                                    onlyIconControl: true,
                                    click: function (obj) {
                                        treeClickNode = obj
                                        combinationClick(obj)
                                    },
                                });
                                let span = "<span class='layui-tree-iconClick por'><i class='layui-icon'></i></span>"
                                febs.get(ctx + 'por/shipBomPorInfo/byShipId', { shipId: shipId }, function (data) {
                                    if (data.code === 200) {
                                        $.each(data.data, function (i, v) {
                                            $view.find('div[data-id="por_' + v.sbpiId + '"]').find('.layui-tree-txt').after(span)
                                        })
                                    }
                                })

                            }
                        })
                    }
                })
                if (!!layoutShipId) {
                    shipId = layoutShipId;
                    febs.get(ctx + 'bom/shipBomInfo/getBlockBomTreeList', { shipId: shipId }, function (data) {
                        if (data.code === 200) {
                            treeData = data.data;
                            initTable();
                            createSurplusTable([], 0);
                            treeData.forEach(blockList => {
                                blockMap.set(blockList.id, blockList.name)
                                blockList.children.forEach(bmList => {
                                    if (bmList.children != null && bmList.children.length > 0) {
                                        bmList.children.forEach(bm => {
                                            bmMap.set(bm.id, bm.name)
                                        })
                                    }
                                })
                            })
                            console.log('bmMap===>', bmMap)
                            // 树组件渲染
                            tree.render({
                                elem: "#treeBom",
                                id: 'demoId',
                                data: treeData,
                                onlyIconControl: true,
                                click: function (obj) {
                                    treeClickNode = obj
                                    combinationClick(obj)
                                },
                            });
                            let span = "<span class='layui-tree-iconClick por'><i class='layui-icon'></i></span>"
                            febs.get(ctx + 'por/shipBomPorInfo/byShipId', { shipId: shipId }, function (data) {
                                if (data.code === 200) {
                                    $.each(data.data, function (i, v) {
                                        $view.find('div[data-id="por_' + v.sbpiId + '"]').find('.layui-tree-txt').after(span)
                                    })
                                }
                            })

                        }
                    })

                }
            }
            //根据船号渲染左侧树
            $('#query').on('click', function () {
                if (shipNoSelect.getValue('value').length === 0) {
                    febs.alert.warn('请选择船号')
                    return false;
                } else {
                    /*
                    shipId = shipNoSelect.getValue('value')[0];
                    febs.get(ctx + 'bom/shipBomInfo/getBlockBomTreeList', {shipId: shipId}, function (data) {
                        if (data.code == 200) {
                            treeData = data.data;
                            initTable();
                            createSurplusTable([],0);
                            treeData.forEach(blockList => {
                                blockMap.set(blockList.id, blockList.name)
                                blockList.children.forEach(bmList => {
                                    bmMap.set(bmList.id, bmList.name)
                                })
                            })
                            // 树组件渲染
                            tree.render({
                                elem: "#treeBom",
                                id: 'demoId',
                                data: treeData,
                                onlyIconControl: true,
                                click: function (obj) {
                                    combinationClick(obj)
                                },
                            });
                            let span = "<span class='layui-tree-iconClick por'><i class='layui-icon'></i></span>"
                            febs.get(ctx + 'por/shipBomPorInfo/byShipId', {shipId:shipId}, function (data) {
                                if (data.code == 200){
                                    $.each(data.data,function (i, v) {
                                        $view.find('div[data-id="por_'+v.sbpiId+'"]').find('.layui-tree-txt').after(span)
                                    })
                                }
                            })
    
                        }
                    })
                    */
                    let type = '';
                    let id = '';
                    if (treeClickNode) {
                        type = treeClickNode.data.id.split('_')[0]
                        id = treeClickNode.data.id.split('_')[1]
                    }

                    let param = {
                        shipId: shipId,
                        sbBomDesc: $('#sbBomDesc').val(),
                        planFinishDate: $('#planFinishDate').val(),
                    }
                    switch (type) {

                        case 'block':
                            param.blockId = id;
                            // param.shipPorType = '0';
                            break
                        case 'GB':
                            param.blockId = id;
                            // param.shipPorType = '0';
                            break
                        case 'bmXC':
                            param.bmId = id;
                            param.shipPorType = '0';
                            break
                        case 'bm':
                            param.bmId = id;
                            param.shipPorType = '0';
                            break;
                        case 'maGB':
                            param.psbmId = id;
                            param.shipPorType = '1';
                            break;
                        case 'xc':
                            param.blockId = id;
                            param.shipPorType = '1';
                            break;
                        case 'maXC':
                            param.psbmId = id;
                            param.shipPorType = '1';
                            break;
                        case 'ma':
                            param.psbmId = id;
                            param.shipPorType = '1';
                            break
                    }
                    if (type === 'bm' || type === 'maGB') {
                        tableIns.reload(
                            {
                                where: param,
                                url: ctx + 'bom/shipBomInfo/page',
                                // page: {curr: 1}
                            }
                        );
                    } else if (type === 'xc' || type === 'maXC') {
                        tableIns.reload(
                            {
                                where: param,
                                url: ctx + 'bom/shipBomBarInfo/page',
                                // page: {curr: 1}
                            }
                        );
                    } else if (type === 'block' || type === 'ma') {
                        //大组分段查询 查钢板和型材
                        tableIns.reload(
                            {
                                where: param,
                                url: ctx + 'bom/shipBomInfo/all',
                                // page: {curr: 1}
                            }
                        );
                    } else if (shipNoSelect.getValue('value').length === 1) {
                        //查船下所有
                        tableIns.reload(
                            {
                                where: param,
                                url: ctx + 'bom/shipBomInfo/all',
                                // page: {curr: 1}
                            }
                        );
                    }
                    createSurplusTable([], tp);

                }
            });


            //树节点点击事件
            function combinationClick(obj) {
                let shipId = shipNoSelect.getValue('valueStr');
                if (shipId === '') {
                    febs.alert.warn('请选择船号')
                    return false;
                }
                let type = obj.data.id.split('_')[0]
                let id = obj.data.id.split('_')[1]
                let param = {
                    shipId: shipId
                }
                bomlistRow = null;//清除页面缓存数据
                switch (type) {
                    case 'block':
                        param.blockId = id;
                        param.shipPorType = '0';
                        break
                    case 'bm':
                        param.bmId = id;
                        param.shipPorType = '0';
                        break;
                    case 'bmXC':
                        param.bmId = id;
                        param.shipPorType = '0';
                        break;
                    case 'GB':
                        param.blockId = id;
                        // param.shipPorType = '2';
                        break;
                    case 'maGB':
                        param.psbmId = id;
                        param.shipPorType = '1';
                        break;
                    case 'xc':
                        param.blockId = id;
                        // param.shipPorType = '1';
                        break;
                    case 'maXC':
                        param.psbmId = id;
                        param.shipPorType = '1';
                        break;
                    case 'ma':
                        param.psbmId = id;
                        param.shipPorType = '1';
                        break
                }
                reloadParamForExcel = param;
                $view.find('#treeBom').find('.tree-selected').removeClass('tree-selected')
                $(obj.elem).children('.layui-tree-entry').find('.layui-tree-txt').addClass('tree-selected')
                if (type === 'bm' || type === 'maGB' || type === 'GB') {
                    tableIns.reload(
                        {
                            where: param,
                            url: ctx + 'bom/shipBomInfo/page',
                            // page: {curr: 1}
                        }
                    );
                } else if (type === 'bmXC' || type === 'xc' || type === 'maXC') {
                    tableIns.reload(
                        {
                            where: param,
                            url: ctx + 'bom/shipBomBarInfo/page',
                            // page: {curr: 1}
                        }
                    );
                } else if (type === 'block' || type === 'ma') {
                    //大组分段查询 查钢板和型材
                    tableIns.reload(
                        {
                            where: param,
                            url: ctx + 'bom/shipBomInfo/all',
                            // page: {curr: 1}
                        }
                    );
                }
                //动态切换 是钢板table 还是型材table
                if (obj.data.name === '型材') {
                    //型材
                    tp = 1;
                } else {
                    tp = 0;
                }
                createSurplusTable([], tp);
            }
            // else{
            //     $view.find('#treeBom').find('.tree-selected').removeClass('tree-selected')
            //     $(obj.elem).find('.layui-tree-main').find('.layui-tree-txt').addClass('tree-selected')
            // }
            //


            function initTable() {
                let arr = []
                createMainTable(arr)
                createDetailTable(arr)
                createDetailBeforeShipTable(arr)
            }
            //生成右上表格数据
            function createMainTable(data) {
                tableIns = febs.table.init({
                    elem: $view.find('#bomList'),
                    id: 'bomList',
                    data: data,
                    autoSort: true,
                    sort: true,
                    cols: [
                        [
                            { type: 'checkbox' },
                            {
                                field: 'sbBomNo', sort: true, title: 'BOM编号', align: 'left', minWidth: 100, templet: function (d) {
                                    return '<div style="text-align: left">' + d.sbBomNo + '</div>'
                                }
                            },
                            {
                                field: 'sbBomDesc', title: '<span style="color: #1b83f0;font-weight: bold">BOM描述</span>', align: 'left', minWidth: 180, edit: function (d) {
                                    if (d.status !== 1 && editFlg) {
                                        return "text"
                                    }
                                }, templet: function (d) {
                                    return '<div style="text-align: left">' + (null != d.sbBomDesc ? d.sbBomDesc : '') + '</div>'
                                }
                            },
                            {
                                field: 'planFinishDate', title: '<span style="color: #1b83f0;font-weight: bold">计划完成日</span>', align: 'center', minWidth: 180,
                                templet(d) {
                                    if (editFlg) {
                                        if (d.planFinishDate == null) {
                                            return '<input id="DATE-' + d.sbiId + '" class="layui-input laydate-demo"  value="" style="text-align: center" ' + (!editFlg ? 'disabled' : '') + '>'

                                        } else {
                                            return '<input id="DATE-' + d.sbiId + '" class="layui-input laydate-demo"  value="' + (commonJS.formatDate(new Date(d.planFinishDate), 'yyyy-MM-dd')) + '" style="text-align: center">'
                                        }
                                    } else {
                                        if (d.planFinishDate == null) {
                                            return '<input id="DATE-' + d.sbiId + '" class="layui-input laydate-demo"  value="" style="text-align: center" ' + (!editFlg ? 'disabled' : '') + ' disabled>'

                                        } else {
                                            return '<input id="DATE-' + d.sbiId + '" class="layui-input laydate-demo"  value="' + (commonJS.formatDate(new Date(d.planFinishDate), 'yyyy-MM-dd')) + '" style="text-align: center" disabled>'
                                        }
                                    }
                                }
                            },
                            { field: 'verifyTime', sort: true, title: '确认时间', align: 'center', minWidth: 150, },
                            {
                                field: 'status', title: '状态', align: 'center', minWidth: 150, templet(d) {
                                    switch (d.status) {
                                        case '0':
                                            return '创建初始化'
                                        case '1':
                                            return '已确认'
                                        case '2':
                                            return '已领用'
                                        case '3':
                                            return '已发放'
                                        default:
                                            return ''
                                    }
                                }
                            },

                            /* {fixed: 'right', title:'操作', width: 240, minWidth: 125, align:'center',templet: function (d){
                                      if (editFlg) {
                                          let html = '    <div class="layui-clear-space">\n'
                                          switch (d.status) {
                                              case '0' :
                                                  if (d.bomType === '0') {
                                                      html +=
                                                          '        <a class="layui-btn layui-btn-xs" lay-event="comfirm">确认</a>\n' +
                                                          '        <a class="layui-btn layui-btn-xs" lay-event="addSteel">选择物资</a>\n' +
                                                          '        <a class="layui-btn layui-btn-xs" lay-event="addSurplus">选择余料</a>\n'
                                                  } else {
                                                      html +=
                                                          '        <a class="layui-btn layui-btn-xs" lay-event="comfirm">确认</a>\n' +
                                                          '        <a class="layui-btn layui-btn-xs" lay-event="addBar">选择物资</a>\n' +
                                                          '        <a class="layui-btn layui-btn-xs" lay-event="addSurplus">选择余料</a>\n'
                                                  }
                                                  break;
    
                                              case '1' :
                                                  html +=
                                                      // '        <a class="layui-btn layui-btn-xs" lay-event="used">领用</a>\n' +
                                                      '        <a class="layui-btn layui-btn-xs layui-bg-red" lay-event="back" >退回</a>\n'
                                                  break;
                                              // case '2':
                                              //     html +=
                                              //         '        <a class="layui-btn layui-btn-xs" lay-event="release">发放</a>\n' +
                                              //         '        <a class="layui-btn layui-btn-xs layui-bg-red" lay-event="back" >退回</a>\n'
                                              //     break;
                                              // case '3' :
                                              //     html +=
                                              //         '        <a class="layui-btn layui-btn-xs" lay-event="view" style="background-color: #16b777">详情</a>\n'
                                              //     break;
                                              default :
                                                  break;
                                          }
                                          // html +='    <a class="layui-btn layui-btn-xs" lay-event="more">\n' +
                                          //     '      更多 \n' +
                                          //     '      <i class="layui-icon layui-icon-down"></i>\n' +
                                          //     '    </a>'+
                                          //     '    </div>'
                                          return html;
                                      } else {
                                          return ''
                                      }
                                  },
                              },*/
                            // {fixed: 'right', title:'操作2', width: 180, minWidth: 100,templet:function (d) {
                            //     if (editFlg){
                            //         return '    <div class="layui-clear-space">\n' +
                            //             // '        <a class="layui-btn layui-btn-xs "  style="background-color: #16b777" lay-event="detail">详情</a>\n' +
                            //             '        <a class="layui-btn layui-btn-xs blueBtm" lay-event="save">保存</a>\n' +
                            //             '        <a class="layui-btn layui-btn-xs layui-bg-red" lay-event="del">删除</a>\n' +
                            //             '    </div>'
                            //     }else {
                            //         return '    <div class="layui-clear-space">\n' +
                            //             // '        <a class="layui-btn layui-btn-xs "  style="background-color: #16b777" lay-event="detail">详情</a>\n' +
                            //             '    </div>'
                            //     }
                            // }}
                        ],

                    ],
                    page: false,
                    done: function (res) {
                        createDetailTable([]);
                        createSurplusTable([], tp);
                        if (null != res.data && res.data.length > 0) {
                            res.data.forEach(item => {
                                laydate.render({
                                    elem: '#DATE-' + item.sbiId,
                                    done: function (value, date, endDate) {
                                        if (!value) {
                                            item.planFinishDate = '';
                                        } else {
                                            item.planFinishDate = commonJS.formatDate(new Date(value), 'yyyy-MM-dd hh:mm:ss');
                                        }
                                        if (item.bomType === '0') {
                                            febs.postArray(ctx + 'bom/shipBomInfo/update', item, function () {
                                                febs.alert.success('计划完成日修改成功');
                                            });
                                        } else {
                                            febs.postArray(ctx + 'bom/shipBomBarInfo/update', item, function () {
                                                febs.alert.success('计划完成日修改成功');
                                            });
                                        }
                                    }
                                });
                            })
                        }

                    }
                })
            }
            //跳转船体BOM新增页面
            $view.on('click', '#addBom', function () {
                let type = $view.find('#addBom').data('type')
                let bmInfo = {
                    shipId: $view.find('#addBom').data('shipid'),
                    type: type
                }
                switch (type) {
                    case 'bm':
                        bmInfo.bmId = $view.find('#addBom').data('bmid');
                        bmInfo.blockId = $view.find('#addBom').data('blockid');
                        bmInfo.psbmId = 0;
                        bmInfo.shipPorType = '0';
                        break;
                    case 'bmXC':
                        bmInfo.bmId = $view.find('#addBom').data('bmid');
                        bmInfo.blockId = $view.find('#addBom').data('blockid');
                        bmInfo.psbmId = 0;
                        bmInfo.shipPorType = '0';
                        break;
                    case 'GB':
                        bmInfo.bmId = 0;
                        bmInfo.blockId = $view.find('#addBom').data('blockid');
                        bmInfo.psbmId = 0;
                        bmInfo.shipPorType = '2';
                        break;
                    case 'maGB':
                        bmInfo.bmId = 0;
                        bmInfo.blockId = 0;
                        bmInfo.psbmId = $view.find('#addBom').data('blockid');
                        bmInfo.shipPorType = '1';
                        break;
                    case 'xc':
                        bmInfo.bmId = 0;
                        bmInfo.blockId = $view.find('#addBom').data('blockid');
                        bmInfo.psbmId = 0;
                        bmInfo.shipPorType = '2';
                        break;
                    case 'maXC':
                        bmInfo.blockId = 0;
                        bmInfo.psbmId = $view.find('#addBom').data('blockid');
                        bmInfo.shipPorType = '1';
                        break;
                }
                febs.modal.open('BOM新增', 'bom/shipBomManage/edit', {
                    area: ['500px', '320px'],
                    btn: ['确定'],
                    data: { bmInfo: bmInfo },
                    yes: function () {
                        $('#shipBomEdit').find('#submit').trigger('click');
                        // if (type === 'bm' || type === 'maGB'){
                        //     tableIns.reload({
                        //         where: {bmId:bmInfo.bmId},
                        //         url: ctx + 'bom/shipBomInfo/page',
                        //         page: {curr: 1}
                        //     });
                        // }else {
                        //     tableIns.reload({where: bmInfo, url: ctx + 'bom/shipBomBarInfo/page', page: {curr: 1}});
                        // }

                    }
                });
            })
            $('#comfirm').on('click', function () {
                comfirmBatch();
            })
            $('#backBom').on('click', function () {
                backBomBatch();
            })

            //批量撤销确认
            function backBomBatch() {
                let checkStatus = table.checkStatus('bomList');
                if (checkStatus.data.length === 0) {
                    febs.alert.warn("请至少选择一条BOM数据")
                    return false;
                }
                let list = []
                let list2 = []
                let flag = true;
                $.each(checkStatus.data, function (i, v) {
                    v.status = '0';
                    v.verifyFlg = '1';
                    if (v.status == null || v.status == undefined || v.status == '') {
                        flag = false;
                    }
                    list.push(v);

                })
                if (!flag) {
                    febs.alert.error('BOM编号不能为空');
                    return false;
                }
                febs.modal.confirm('撤销', '是否撤销BOM信息', function () {
                    febs.postArray(ctx + 'bom/shipBomInfo/batchUpdate', list, function (e) {
                        if (e.code === 200) {
                            febs.alert.success('撤销成功');
                            table.reload('bomList')
                        }
                    })
                    // if(list2.length>0){
                    //     //list:型材
                    //     febs.postArray(ctx + 'bom/shipBomBarInfo/updateBatch', list2, function (e) {
                    //         if (e.code === 200) {
                    //             // febs.alert.success('撤销成功');
                    //             table.reload('bomList')
                    //         }
                    //     });
                    // }
                    // febs.alert.success('撤销成功');
                    // table.reload('bomList')
                    // if(list[0].bomType === '0'){
                    //     febs.postArray(ctx + 'bom/shipBomInfo/batchUpdate', list, function (e) {
                    //         if (e.code === 200) {
                    //             febs.alert.success('撤销成功');
                    //             table.reload('bomList')
                    //         }
                    //     })
                    // }else {
                    //     febs.postArray(ctx + 'bom/shipBomBarInfo/updateBatch', list, function (e) {
                    //         if (e.code === 200) {
                    //             febs.alert.success('撤销成功');
                    //             table.reload('bomList')
                    //         }
                    //     });
                    // }
                })
            }

            function comfirmBatch() {
                let checkStatus = table.checkStatus('bomList');
                if (checkStatus.data.length == 0) {
                    febs.alert.warn("请至少选择一条BOM数据")
                    return false;
                }
                //钢板list
                let list = []
                //型材list
                let list2 = []
                let flag = true;
                $.each(checkStatus.data, function (i, v) {
                    v.status = '1';
                    v.verifyFlg = '1'
                    if (v.status == null || v.status == undefined || v.status == '') {
                        flag = false;
                    }
                    list.push(v);

                })
                if (!flag) {
                    febs.alert.error('BOM编号不能为空');
                    return false;
                }

                febs.modal.confirm('确认', '是否确认BOM信息', function () {
                    febs.postArraySync(ctx + 'bom/shipBomInfo/batchUpdate', list, function (e) {
                        if (e.code === 200) {
                            febs.alert.success('确认成功');
                            table.reload('bomList')
                        }
                    })
                    // if(list2.length>0){
                    //     //list:型材
                    //     febs.postArraySync(ctx + 'bom/shipBomBarInfo/updateBatch', list2, function (e) {
                    //         if (e.code === 200) {
                    //             // febs.alert.success('确认成功');
                    //             // table.reload('bomList')
                    //         }
                    //     });
                    // }
                    // febs.alert.success('确认成功');
                    // table.reload('bomList')
                    // if(list[0].bomType === '0'){
                    //     febs.postArray(ctx + 'bom/shipBomInfo/batchUpdate', list, function (e) {
                    //         if (e.code === 200) {
                    //             febs.alert.success('确认成功');
                    //             table.reload('bomList')
                    //         }
                    //     })
                    // }else {
                    //     febs.postArray(ctx + 'bom/shipBomBarInfo/updateBatch', list, function (e) {
                    //         if (e.code === 200) {
                    //             febs.alert.success('确认成功');
                    //             table.reload('bomList')
                    //         }
                    //     });
                    // }
                })
            }
            // mainBomTable触发单元格工具事件
            table.on('tool(bomList)', function (obj) { // 双击 toolDouble
                if (obj.event === 'comfirm') { //确认
                    comfirm(obj)
                } else if (obj.event === 'used') { //领用
                    used(obj)
                } else if (obj.event === 'release') { //发放
                    release(obj)
                } else if (obj.event === 'back') { //退回
                    back(obj)
                } else if (obj.event === 'view') {
                    view(obj)
                } else if (obj.event === 'detail') {
                    view(obj)
                } else if (obj.event === 'save') {
                    saveBom(obj)
                } else if (obj.event === 'del') {
                    deleteBom(obj)
                } else if (obj.event === 'addSteel') {
                    febs.modal.open('添加钢板BOM物资', 'bom/shipBomManage/materialSelect', {
                        area: ['100%', '100%'],
                        btn: ['确定'],
                        data: { bomInfo: obj.data, tableInsDetail: tableInsDetail },
                        yes: function () {
                            $('#materialSelect').find('#submitMaterial').trigger('click');
                        }
                    });
                } else if (obj.event === 'addBar') {
                    febs.modal.open('添加型材BOM物资', 'bom/shipBomManage/addDetailBarInfo', {
                        area: ['500px', '450px'],
                        btn: ['确定'],
                        data: { bomInfo: obj.data, tableInsDetail: tableInsDetail },
                        yes: function () {
                            $('#addDetailBarInfo').find('#sectionBarSubmit').trigger('click');
                        }
                    });
                }
                else if (obj.event === 'addSurplus') {
                    if (obj.data.bomType === 1) {
                        //型材
                        addSurplusMater(obj);

                    } else {
                        //钢板
                        addSurplusSteel(obj);
                    }
                }
                // else if (obj.event === 'more'){
                //     // 更多 - 下拉菜单
                //     dropdown.render({
                //         elem: this, // 触发事件的 DOM 对象
                //         show: true, // 外部事件触发即显示
                //         data: [{
                //             title: '详情',
                //             id: 'detail'
                //             },
                //             {
                //                 title: '保存',
                //                 id: 'save'
                //             }, {
                //                 title: '删除',
                //                 id: 'del'
                //             }],
                //         click: function(menudata){
                //             if(menudata.id === 'detail'){
                //                 view(obj)
                //             } else if(menudata.id === 'del'){
                //                 deleteBom(obj)
                //             } else if(menudata.id === 'save'){
                //                 saveBom(obj)
                //             }
                //         },
                //         align: 'right', // 右对齐弹出
                //         style: 'box-shadow: 1px 1px 10px rgb(0 0 0 / 12%);' // 设置额外样式
                //     })
                // }
            });
            function addSurplusSteel(obj) {
                let param = obj.data
                param.bmId = bomBmId
                param.blockId = bomBlockId
                febs.modal.open('钢板余料', 'bom/shipBomManage/addSurplusSteel', {
                    btn: ['确定'],
                    area: ['98%', '98%'],
                    data: {
                        row: param
                    },
                    yes: function (e) {
                        $('#febs-steelAdd').find('#submit').trigger('click');
                    }
                });
            }
            function addSurplusMater(obj) {
                let param = obj.data
                param.bmId = bomBmId
                param.blockId = bomBlockId
                febs.modal.open('型材余料', 'bom/shipBomManage/addSurplusMater', {
                    btn: ['确定'],
                    area: ['98%', '98%'],
                    data: {
                        row: param
                    },
                    yes: function (e) {
                        $('#febs-materAdd').find('#submit').trigger('click');
                    }
                });
            }




            //确认
            function comfirm(obj) {
                obj.data.status = '1';
                febs.modal.confirm('确认', '是否确认BOM信息', function () {
                    if (obj.data.bomType === '0') {
                        febs.postArray(ctx + 'bom/shipBomInfo/update', obj.data, function (e) {
                            if (e.code === 200) {
                                febs.alert.success('确认成功');
                                table.reload('bomList')
                            }
                        })
                    } else {
                        febs.postArray(ctx + 'bom/shipBomBarInfo/update', obj.data, function (e) {
                            if (e.code === 200) {
                                febs.alert.success('确认成功');
                                table.reload('bomList')
                            }
                        });
                    }
                })
            }
            //领用
            function used(obj) {
                obj.data.status = '2';
                febs.modal.confirm('领用', '是否领用BOM信息', function () {
                    febs.postArray(ctx + 'bom/shipBomInfo/update', obj.data, function (e) {
                        if (e.code === 200) {
                            febs.alert.success('领用成功');
                            table.reload('bomList')
                        }
                    })
                })
            }
            //发放
            function release(obj) {
                obj.data.status = '3';
                febs.modal.confirm('发放', '是否发放BOM信息', function () {
                    febs.postArray(ctx + 'bom/shipBomInfo/update', obj.data, function (e) {
                        if (e.code === 200) {
                            febs.alert.success('发放成功');
                            table.reload('bomList')
                        }
                    })
                })
            }
            //退回
            function back(obj) {

                obj.data.status = '0';
                febs.modal.confirm('退回', '是否退回BOM信息', function () {
                    if (obj.data.bomType === '0') {
                        febs.postArray(ctx + 'bom/shipBomInfo/update', obj.data, function (e) {
                            if (e.code === 200) {
                                febs.alert.success('确认成功');
                                table.reload('bomList')
                            }
                        })
                    } else {
                        febs.postArray(ctx + 'bom/shipBomBarInfo/update', obj.data, function (e) {
                            if (e.code === 200) {
                                febs.alert.success('退回成功');
                                table.reload('bomList')
                            }
                        });
                    }
                })
            }


            // 监听排序
            table.on('sort(bomList)', function (d) {
                let param = d.config.where;
                let serverUrl = d.config.url;
                param.order = d.type;
                let shipId = shipNoSelect.getValue('valueStr');
                if (shipId === '') {
                    febs.alert.warn('请选择船号')
                    return false;
                } else {
                    let shipId = shipNoSelect.getValue('value')[0];
                    tableIns.reload(
                        {
                            url: serverUrl,
                            where: param,
                            // page: {curr: 1}
                        }
                    );

                }
            })

            table.on('row(bomList)', function (obj) {
                let tr = obj.tr;
                $view.find('.detailBgColor').removeClass('detailBgColor')
                tr.removeClass('detailBgColor')
                tr.addClass('detailBgColor')
                // 标注当前点击行的选中状态
                // obj.setRowChecked({
                //     type: 'checkbox', // radio 单选模式；checkbox 复选模式
                //     checked:false
                // });
                //行内点击的时候 查询关联钢板数据
                view(obj);

            })
            function view(obj) {
                bomlistRow = obj.data;
                // $view.find('.detailBgColor').removeClass('detailBgColor')
                // $view.find('.layui-table-click').addClass('detailBgColor')
                status = obj.data.status;
                if (obj.data.shipPorType === '0') {
                    bomBmId = obj.data.bmId
                    bomBlockId = 0
                } else if (obj.data.shipPorType === '2') {
                    bomBlockId = obj.data.blockId
                    bomBmId = 0
                }
                if (obj.data.bomType == 1) {
                    //型材
                    tp = 1;
                    createSurplusTable([], tp);
                    tableInsDetail.reload({
                        where: { sbiId: obj.data.sbiId },
                        url: ctx + 'bom/shipBomDetailInfo/getBarInfoById',
                        // page: {curr: 1}
                    });
                    tableInsDetail_beforeShip.reload({
                        where: { sbiId: obj.data.sbiId },
                        url: ctx + 'bom/shipBomDetailInfo/getBeforeShipBar',
                        // page: {curr: 1}
                    });
                    changeTable.reload({
                        where: { sbbiId: obj.data.sbiId },
                        url: ctx + 'bom/surplusMaterialBarInfo/listPage',
                        // page: {curr: 1}
                    })
                    sbiId = null;
                    sbbiId = obj.data.sbiId
                } else {
                    //钢板
                    tp = 0;
                    createSurplusTable([], tp);
                    tableInsDetail.reload({
                        where: { sbiId: obj.data.sbiId },
                        url: ctx + 'bom/shipBomDetailInfo/getSteelInfoById',
                        // page: {curr: 1}
                    });
                    tableInsDetail_beforeShip.reload({
                        where: { sbiId: obj.data.sbiId },
                        url: ctx + 'bom/shipBomDetailInfo/getBeforeShipSteel',
                        // page: {curr: 1}
                    });
                    changeTable.reload({
                        where: { sbiId: obj.data.sbiId },
                        url: ctx + 'bom/surplusMaterialSteelInfo/listPage',
                        // page: {curr: 1}
                    })
                    sbiId = obj.data.sbiId
                    sbbiId = null;
                }

            }
            //删除船体BOM
            function deleteBom(obj) {
                if (obj.data.status === 1) {
                    febs.alert.warn('已确认的船体BOM不能删除');
                    return false;
                }
                febs.modal.confirm('删除', '是否确定删除选中的BOM信息', function () {
                    if (obj.data.bomType === '0') {
                        febs.get(ctx + 'bom/shipBomInfo/delete', { sbiId: obj.data.sbiId }, function (e) {
                            if (e.code === 200) {
                                febs.alert.success('删除成功');
                                table.reload('bomList')
                            }
                        })
                    } else {
                        febs.get(ctx + 'bom/shipBomBarInfo/delete', { sbbiId: obj.data.sbiId }, function (e) {
                            if (e.code === 200) {
                                febs.alert.success('删除成功');
                                table.reload('bomList')
                            }
                        })
                    }
                })
            }
            //批量删除船体BOM
            $('#delBom').on('click', function () {
                let flg = true;
                let sbiIds = []
                let list = []
                let checkStatus = table.checkStatus('bomList');
                if (checkStatus.data.length === 0) {
                    febs.alert.warn("请至少选择一条BOM数据")
                    return false;
                }
                let bomType = checkStatus.data[0].bomType;
                $.each(checkStatus.data, function (i, v) {
                    if (v.status === 1) {
                        febs.alert.warn('已确认的船体BOM不能删除');
                        flg = false;
                        return false;
                    }
                    // sbiIds.push(v.sbiId);
                    list.push(v);
                })
                if (flg) {
                    febs.modal.confirm('批量删除', '是否确定删除选中的BOM', function () {
                        // if (bomType === '0'){
                        febs.postArray(ctx + 'bom/shipBomInfo/deleteBatchNew', list, function (e) {
                            if (e.code === 200) {
                                febs.alert.success('删除成功');
                                tableIns.reloadData();
                            }
                        })
                        // }

                        // else {
                        //     febs.get(ctx + 'bom/shipBomBarInfo/deleteBatch', {sbbiIds : sbiIds.join(',')}, function (e) {
                        //         if (e.code === 200) {
                        //             febs.alert.success('删除成功');
                        //             tableIns.reloadData();
                        //         }
                        //     })
                        // }
                    })
                }
            })

            function saveBom(obj) {
                if (obj.data.bomType === '0') {
                    febs.postArray(ctx + 'bom/shipBomInfo/update', obj.data, function () {
                        febs.alert.success('修改成功');
                        $(obj.tr[0]).removeClass('edited')
                    });
                } else {
                    febs.postArray(ctx + 'bom/shipBomBarInfo/update', obj.data, function () {
                        febs.alert.success('修改成功');
                        $(obj.tr[0]).removeClass('edited')
                    });
                }
            }


            table.on('toolbar(changeTable)', function (obj) {
                switch (obj.event) {
                    case 'delSurplus': {
                        delSurplus();
                    }
                        break;
                    case 'querySurplus': {
                        let param = {}
                        if (tp === 1) {
                            param.sbbiId = sbbiId
                            param.basicMaterialNo = $wuziylNoSur.val();
                            //型材余料查询
                            changeTable.reload({
                                where: param,
                                url: ctx + 'bom/surplusMaterialBarInfo/listPage',
                                // page: {curr: 1}
                            })
                        } else {
                            param.sbiId = sbiId
                            param.basicMaterialNo = $wuziylNoSur.val();
                            //钢板余料查询
                            changeTable.reload({
                                where: param,
                                url: ctx + 'bom/surplusMaterialSteelInfo/listPage',
                                // page: {curr: 1}
                            })

                        }
                        $wuziylNoSur.val(param.basicMaterialNo);
                    }
                        break;
                }
            })

            function delSurplus() {
                let checkStatus = table.checkStatus('changeTable');
                if (checkStatus.data.length === 0) {
                    febs.alert.warn("请至少选择一条余料数据")
                    return false;
                }
                let list = [];
                let bomId = '';
                if (tp === 1) {
                    bomId = checkStatus.data[0].sbbiId;
                    $.each(checkStatus.data, function (i, v) {
                        list.push(v.smbiId);
                    })
                    febs.modal.confirm('删除', '是否确定删除选中的余料', function () {
                        febs.postArray(ctx + 'bom/surplusMaterialBarInfo/deleteBarInfo/' + bomId, list, function (e) {
                            if (e.code === 200) {
                                febs.alert.success('删除成功');
                                table.reload('changeTable')
                            }
                        })
                    })


                } else {
                    bomId = checkStatus.data[0].sbiId;
                    $.each(checkStatus.data, function (i, v) {
                        list.push(v.smsiId);
                    })
                    febs.modal.confirm('删除', '是否确定删除选中的余料', function () {
                        febs.postArray(ctx + 'bom/surplusMaterialSteelInfo/deleteSteelInfo/' + bomId, list, function (e) {
                            if (e.code === 200) {
                                febs.alert.success('删除成功');
                                table.reload('changeTable')
                            }
                        })
                    })
                }
            }

            // table.on('toolbar(detailBomList)',function (obj){
            //     console.log(obj)
            //     switch (obj.event) {
            //         case 'queryBomMaterial':{
            //             let param = {}
            //             if(tp==1){
            //                 param.sbiId = sbbiId
            //                 param.basicMaterialNo = $('#wuziNoIpt').val();
            //                 //型材查询
            //                 tableInsDetail.reload({
            //                     where: param,
            //                     url: ctx + 'bom/shipBomDetailInfo/getBarInfoById',
            //                     // page: {curr: 1}
            //                 });
            //             }else {
            //                 param.sbiId = sbiId
            //                 param.basicMaterialNo = $('#wuziNoIpt').val();
            //                 //钢板查询
            //                 tableInsDetail.reload({
            //                     where: param,
            //                     url: ctx + 'bom/shipBomDetailInfo/getSteelInfoById',
            //                     // page: {curr: 1}
            //                 });
            //             }
            //             $('#wuziNoIpt').val(param.basicMaterialNo);
            //         }
            //         break;
            //     }
            // })

            // detailBomTable触发单元格工具事件
            table.on('tool(detailBomList)', function (obj) { // 双击 toolDouble
                let data = obj.data; // 获得当前行数据
                if (obj.event === 'del') {
                    febs.modal.confirm('删除', '是否确定删除选中的钢板', function () {
                        febs.get(ctx + 'bom/shipBomDetailInfo/removeSteelInfo', { sbdiIds: obj.data.sbdiId }, function (e) {
                            if (e.code === 200) {
                                febs.alert.success('删除成功');
                                table.reload('detailBomList')
                            }
                        })
                    })
                }
                else if (obj.event === 'createSurplus') {
                    //创建生成钢板余料
                    createSurplus(obj);
                } else if (obj.event === 'createSurplusMater') {
                    //型材
                    createSurplusMater(obj);
                }
            });

            function createSurplus(obj) {
                let title = '生成钢板余料-' + obj.data.shipSteelCode
                febs.modal.open(title, 'bom/shipBomManage/createSurplusView', {
                    area: ['100%', '100%'],
                    data: { row: obj.data },
                });


                // febs.modal.open('生成钢板余料', 'bom/shipBomManage/createSurplus', {
                //     area: ['700px', '580px'],
                //     btn: ['确定'],
                //     data : {row : obj.data},
                //     yes: function () {
                //         $('#shipBomAddSurplus').find('#submit').trigger('click');
                //     }
                // });
            }
            flushDataSteel = function (sbiId) {
                changeTable.reload({
                    where: { sbiId },
                    url: ctx + 'bom/surplusMaterialSteelInfo/listPage',
                    // page: {curr: 1}
                })
            }
            function createSurplusMater(obj) {

                let title = '生成型材余料-' + obj.data.sectionBarNo
                febs.modal.open(title, 'bom/shipBomManage/createSurplusBarView', {
                    area: ['100%', '100%'],
                    data: { row: obj.data },
                });

                // febs.modal.open('生成型材余料', 'bom/shipBomManage/createSurplusMater', {
                //     area: ['700px', '480px'],
                //     btn: ['确定'],
                //     data : {row : obj.data},
                //     yes: function () {
                //         $('#shipBomAddSurplusMater').find('#submit').trigger('click');
                //     }
                // });
            }

            flushDataBar = function (sbbiId) {
                changeTable.reload({
                    where: { sbbiId },
                    url: ctx + 'bom/surplusMaterialBarInfo/listPage',
                    // page: {curr: 1}
                })
            }

            //生成右下表格数据
            function createDetailTable(data) {
                tableInsDetail = febs.table.init({
                    elem: $view.find('#detailBomList'),
                    id: 'detailBomList',
                    data: data,
                    defaultToolbar: [],
                    toolbar: '#paintPeBlockMatchToolbar',
                    page: false,
                    sort: true,
                    autoSort: true,
                    cols: [
                        [
                            { type: 'checkbox' },
                            { field: 'lineNo', title: '行项号', align: 'center', width: 60, sort: true },
                            { field: 'shipNO', title: '船号', align: 'center', width: 60 },
                            {
                                field: 'blockId', title: '大组分段', align: 'center', width: 80, templet: function (d) {
                                    if (d.blockId && !d.useType) {
                                        return blockMap.get("block_" + d.blockId);
                                    } else if (d.blockId && d.useType) {
                                        return blockMap.get("ma_" + d.blockId);
                                    } else {
                                        return "";
                                    }
                                }
                            },
                            {
                                field: 'bmId', title: '加工分段', align: 'center', width: 80, templet: function (d) {
                                    if (d.bmId && !d.useType) {
                                        let val = "";
                                        val = bmMap.get("bmXC_" + d.bmId) || bmMap.get("bm_" + d.bmId) || bmMap.get("xc_" + d.bmId);
                                        return val;
                                    } else if (d.bmId && d.useType) {
                                        let val = "";
                                        val = bmMap.get("maGB_" + d.bmId) || bmMap.get("maXC_" + d.bmId);
                                        return val;
                                    } else {
                                        return "";
                                    }

                                }
                            },
                            {
                                field: 'basicMaterialDesc', title: '物资描述', align: 'center', minWidth: 160, sort: true, templet: function (d) {
                                    return '<div style="text-align: left">' + d.basicMaterialDesc + '</div>'
                                }
                            },
                            { field: 'nestingNo', title: '套料号', align: 'center', minWidth: 160, sort: true },
                            {
                                field: 'shipSteelCode', title: '钢板/型材编码', align: 'center', width: 140, sort: true, templet: function (d) {
                                    if (d.bomType == 1) {
                                        return '<div style="text-align: left">' + d.sectionBarNo + '</div>';
                                    } else {
                                        return '<div style="text-align: left">' + d.shipSteelCode + '</div>';
                                    }
                                }
                            },
                            {
                                field: 'shipSteelDesc', title: '备注', align: 'center', minWidth: 130, templet: function (d) {
                                    if (d.bomType == 1) {
                                        return '<div style="text-align: left">' + (d.sectionBarDesc === null ? '' : d.sectionBarDesc) + '</div>';
                                    } else {
                                        return '<div style="text-align: left">' + (d.shipSteelDesc === null ? '' : d.shipSteelDesc) + '</div>';
                                    }
                                }
                            },
                            {
                                field: 'basicMaterialNo', title: '物资编码', align: 'center', width: 100, templet: function (d) {
                                    return '<div style="text-align: left">' + d.basicMaterialNo + '</div>'
                                }
                            },
                            {
                                field: 'sbPorNo', title: 'POR编号', align: 'center', width: 100, templet: function (d) {
                                    return '<div style="text-align: left">' + d.sbPorNo + '</div>'
                                }
                            },
                            {
                                field: 'sbPorDesc', title: 'POR描述', align: 'center', width: 100, templet: function (d) {
                                    return '<div style="text-align: left">' + d.sbPorDesc + '</div>'
                                }
                            },

                        ]
                    ],
                    done: function (res, curr, count) {
                        $('#addSteelBtn').mouseenter(function () {
                            // console.log('鼠标进入export_btn')
                            $('#addSteelBtn').empty()
                            let h = `
                        <i class="layui-icon layui-icon-add-1"></i>添加
                            <p class="add_item" style="color: white;text-align: center;width:100px;z-index:99999999;position:relative;background-color: #1b83f0;top: -1px;right: 10px;padding: 10px"  onclick="shipBomMaterialAddCurr_function()">本船BOM物资</p>
                            <p class="add_item" style="color: white;text-align: center;width:100px;z-index:99999999;position:relative;background-color: #1b83f0;top: -1px;right: 10px;padding: 10px"  onclick="shipBomMaterialAddBefore_function()">前船BOM物资</p>
                        `
                            $('#addSteelBtn').append(h)
                        })
                        $('#addSteelBtn').mouseleave(function () {
                            let btn_name = 'aaa'
                            // console.log('鼠标移除export_btn')
                            $('#addSteelBtn').empty()
                            let h = `
                        <i class="layui-icon layui-icon-add-1"></i>添加
                        <div style="background-color: #0164ff;">
                            <p style="color: white;text-align: center;width:100px;z-index:99999999;position:relative;background-color: #1b83f0;top: -1px;right: 10px;display: none;padding: 10px"  onclick="shipBomMaterialAddCurr_function()">本船BOM物资</p>
                            <p style="color: white;text-align: center;width:100px;z-index:99999999;position:relative;background-color: #1b83f0;top: -1px;right: 10px;display: none;padding: 10px"  onclick="shipBomMaterialAddBefore_function()">前船BOM物资</p>
                             </div>
                        `
                            $('#addSteelBtn').append(h)
                        })
                    }
                })

            }
            table.on('toolbar(beforeShipTable)', function (obj) {
                switch (obj.event) {
                    case 'queryBefore': {
                        if (tp == 0) {
                            //钢板
                            tableInsDetail_beforeShip.reload({
                                where: { sbiId: bomlistRow.sbiId },
                                url: ctx + 'bom/shipBomDetailInfo/getBeforeShipSteel',
                            })
                        } else {
                            tableInsDetail_beforeShip.reload({
                                where: { sbiId: bomlistRow.sbiId },
                                url: ctx + 'bom/shipBomDetailInfo/getBeforeShipBar',
                            })
                        }


                    }
                        break
                    case 'delBefore': {
                        let checkStatus = table.checkStatus('beforeShipTable');
                        if (checkStatus.data.length == 0) {
                            febs.alert.warn("请选择至少一条数据");
                            return false;
                        }

                        let ids = []
                        $.each(checkStatus.data, function (i, v) {
                            if (tp == 0) {
                                ids.push(v.spprId);
                            } else {
                                ids.push(v.bpprId);
                            }

                        })
                        let url = ''
                        if (tp == 0) {
                            url = 'bom/shipBomDetailInfo/delBeforeShipBomDetailInfo'
                        } else {
                            url = 'bom/shipBomDetailInfo/delBeforeShipBomDetailBarInfo'
                        }
                        febs.modal.confirm('删除', '是否确定删除选中的数据', function () {
                            febs.postArray(ctx + url,
                                ids
                                , function (e) {
                                    if (e.code === 200) {
                                        febs.alert.success('删除成功');
                                        tableInsDetail_beforeShip.reloadData();
                                    }
                                })
                        })




                    }
                        break
                    case 'addBefore': {
                        addBeforeShip()
                    }
                        break
                }
            })


            function createDetailBeforeShipTable(data) {
                tableInsDetail_beforeShip = febs.table.init({
                    elem: $view.find('#beforeShipTable'),
                    id: 'beforeShipTable',
                    data: data,
                    defaultToolbar: [],
                    toolbar: '#beforeToolbar',
                    page: false,
                    sort: true,
                    autoSort: true,
                    cols: [
                        [
                            { type: 'checkbox' },
                            { field: 'lineNo', title: '行项号', align: 'center', width: 60, sort: true },
                            { field: 'applyNo', title: '移库单号', align: 'center', width: 130 },
                            {
                                field: 'basicMaterialDesc', title: '物资描述', align: 'center', minWidth: 160, sort: true, templet: function (d) {
                                    return '<div style="text-align: left">' + d.basicMaterialDesc + '</div>'
                                }
                            },
                            // {field: 'nestingNo', title: '套料号', align: 'center', minWidth: 160,sort:true},
                            {
                                field: 'steelNo', title: '钢板/型材编码', align: 'center', width: 140, sort: true, templet: function (d) {
                                    if (d.steelNo) {
                                        return '<div style="text-align: left">' + d.steelNo + '</div>';
                                    }
                                    return '';
                                }
                            },
                            // {field: 'amount', title: '使用数量', align: 'center', width: 130},
                            { field: 'steelDesc', title: '备注', align: 'center', minWidth: 130 },
                            {
                                field: 'basicMaterialNo', title: '物资编码', align: 'center', width: 100, templet: function (d) {
                                    return '<div style="text-align: left">' + d.basicMaterialNo + '</div>'
                                }
                            },
                            {
                                field: 'porNo', title: 'POR编号', align: 'center', width: 100, templet: function (d) {
                                    return '<div style="text-align: left">' + d.porNo + '</div>'
                                }
                            },
                            {
                                field: 'porDesc', title: 'POR描述', align: 'center', width: 100, templet: function (d) {
                                    return '<div style="text-align: left">' + d.porDesc + '</div>'
                                }
                            },
                            { field: 'porShipNo', title: '船号', align: 'center', width: 60 },
                            { field: 'porBlockNo', title: '分段', align: 'center', width: 80 },
                            { field: 'firstThickness', title: '厚度1', align: 'center', width: 80 },
                            { field: 'secondThickness', title: '厚度2', align: 'center', width: 80 },
                            { field: 'firstWidth', title: '宽度1', align: 'center', width: 100 },
                            { field: 'secondWidth', title: '宽度2', align: 'center', width: 100 },
                            { field: 'porBomLength', title: '长度', align: 'center', width: 100 },
                            { field: 'classFirst', title: '船级社1', align: 'center', width: 120 },
                            { field: 'classSecond', title: '船级社2', align: 'center', width: 120 },
                            { field: 'maType', title: '材质', align: 'center', width: 100 },
                        ]
                    ],
                    done: function (res, curr, count) {

                    }
                })

            }
            // 头部工具栏事件
            table.on('toolbar(detailBomList)', function (obj) {
                let options = obj.config; // 获取当前表格属性配置项
                let checkStatus = table.checkStatus(options.id); // 获取选中行相关数据
                // 根据不同的事件名进行相应的操作
                switch (obj.event) { // 对应模板元素中的 lay-event 属性值
                    case 'exportRest':
                        exportRest();
                        break;
                    case 'addSteelBtn':
                        addSteelBtn();
                        break;
                    case 'addBtn_ship': {
                        let event = $('#addBtn_ship').attr('data-btn-event')
                        if (event == 'curr') {
                            //添加本船bom物资
                            addSteelBtn()
                        } else if (event == 'before') {
                            //添加前船bom物资
                            addBeforeShip()

                        }
                    }
                        break;
                    case 'delWuZiBtn':
                        delWuZiBtn();
                        break;
                    case 'searchSteel':
                        searchSteel();
                        break;
                    case 'queryBomMaterial': {
                        let param = {}
                        const $wuziNoIpt = $('#wuziNoIpt'),
                            $tlhNoIpt = $('#tlhNoIpt');
                        if (tp === 1) {
                            param.sbiId = sbbiId
                            param.basicMaterialNo = $wuziNoIpt.val();
                            param.nestingNo = $tlhNoIpt.val();
                            //型材查询
                            tableInsDetail.reload({
                                where: param,
                                url: ctx + 'bom/shipBomDetailInfo/getBarInfoById',
                                // page: {curr: 1}
                            });
                        } else {
                            param.sbiId = sbiId
                            param.basicMaterialNo = $wuziNoIpt.val();
                            param.nestingNo = $tlhNoIpt.val();
                            //钢板查询
                            tableInsDetail.reload({
                                where: param,
                                url: ctx + 'bom/shipBomDetailInfo/getSteelInfoById',
                                // page: {curr: 1}
                            });
                        }
                        $wuziNoIpt.val(param.basicMaterialNo);
                        $tlhNoIpt.val(param.nestingNo);
                    }
                        break;
                }
                return false;
            });

            function searchSteel() {

                if (tp == 1) {
                    //型材
                    tableInsDetail.reload({
                        where: { sbiId: bomlistRow.data.sbiId, nums: $('#wuziNo').val() },
                        url: ctx + 'bom/shipBomDetailInfo/getBarInfoById',
                    });

                    sbiId = null;
                    sbbiId = bomlistRow.data.sbiId
                } else {
                    //钢板
                    tableInsDetail.reload({
                        where: { sbiId: bomlistRow.data.sbiId, nums: $('#wuziNo').val() },
                        url: ctx + 'bom/shipBomDetailInfo/getSteelInfoById',
                    });

                    sbiId = bomlistRow.data.sbiId
                    sbbiId = null;
                }



            }
            function addSteelBtn() {
                let checkStatus = table.checkStatus('bomList');
                let param = {}
                if (checkStatus.data.length == 0 && bomlistRow != null) {
                    param = bomlistRow;
                } else if (checkStatus.data.length == 1) {
                    param = checkStatus.data[0];
                } else {
                    febs.alert.warn("请选择一条BOM数据")
                    return false;
                }
                // console.log(param)
                if (tp == 0) {
                    febs.modal.open('添加钢板BOM物资', 'bom/shipBomManage/materialSelect', {
                        area: ['98%', '95%'],
                        btn: ['确定'],
                        data: { bomInfo: param, tableInsDetail: tableInsDetail },
                        yes: function () {
                            $('#materialSelect').find('#submitMaterial').trigger('click');
                        }
                    })
                } else {
                    febs.modal.open('新增型材BOM物资', 'bom/shipBomManage/materialBarSelect', {
                        area: ['98%', '95%'],
                        btn: ['确定'],
                        data: { bomInfo: param, tableInsDetail: tableInsDetail },
                        yes: function () {
                            $('#materialBarSelect').find('#submitMaterialBar').trigger('click');
                        }
                    });
                    // febs.modal.open('添加型材BOM物资', 'bom/shipBomManage/addDetailBarInfo', {
                    //     area: ['500px', '450px'],
                    //     btn: ['确定'],
                    //     data : {bomInfo : param,tableInsDetail:tableInsDetail},
                    //     yes: function () {
                    //         $('#addDetailBarInfo').find('#sectionBarSubmit').trigger('click');
                    //     }
                    // });
                }
            }
            function addBeforeShip() {
                let checkStatus = table.checkStatus('bomList');
                let param = {}
                if (checkStatus.data.length == 0 && bomlistRow != null) {
                    param = bomlistRow;
                } else if (checkStatus.data.length == 1) {
                    param = checkStatus.data[0];
                } else {
                    febs.alert.warn("请选择一条BOM数据")
                    return false;
                }
                let title = ''
                if (tp == 0) {
                    title = '钢板'
                } else {
                    title = '型材'
                }
                febs.modal.open(`添加${title}BOM物资`, 'bom/shipBomManage/materialBeforeShipBom', {
                    area: ['98%', '95%'],
                    btn: ['确定'],
                    data: { bomInfo: param, tp: tp, tableInsDetail: tableInsDetail_beforeShip },
                    yes: function () {
                        $('#febs-materialBeforeShipBomPage').find('#addBeforeMaterial').trigger('click');
                    }
                })
            }
            function exportRest() {
                let checkStatus = table.checkStatus('detailBomList');
                if (checkStatus.data.length == 0 || checkStatus.data.length > 1) {
                    febs.alert.warn("请选择一条物资数据");
                    return false;
                }
                let param = checkStatus.data[0]
                param.bmId = bomBmId
                param.blockId = bomBlockId
                // console.log(checkStatus.data)
                if (tp == 0) {
                    let title = '生成钢板余料-' + checkStatus.data[0].shipSteelCode
                    febs.modal.open(title, 'bom/shipBomManage/createSurplusView', {
                        area: ['98%', '98%'],
                        data: { row: param },
                    })
                } else {
                    let title = '生成型材余料-' + checkStatus.data[0].sectionBarNo
                    febs.modal.open(title, 'bom/shipBomManage/createSurplusBarView', {
                        area: ['98%', '98%'],
                        data: { row: param },
                    })
                }

            }
            function delWuZiBtn() {
                debugger
                let checkStatus = table.checkStatus('detailBomList');
                if (checkStatus.data.length == 0) {
                    febs.alert.warn("请至少选择一条数据");
                    return false;
                }
                let bomType = checkStatus.data[0].bomType;

                if (tp == 0) {
                    let sbdiIds = []
                    let flg = true
                    $.each(checkStatus.data, function (i, v) {
                        if (v.applyStatus == '1') {
                            febs.alert.warn(v.shipSteelCode + "钢板已做物资申请，无法删除");
                            flg = false;
                            return false;
                        }
                        sbdiIds.push(v.sbdiId);
                    })
                    if (flg) {
                        febs.modal.confirm('删除', '是否确定删除选中的钢板', function () {
                            febs.get(ctx + 'bom/shipBomDetailInfo/removeSteelInfo', {
                                sbdiIds: sbdiIds.join(','),
                                bomId: checkStatus.data[0].sbiId
                            }, function (e) {
                                if (e.code === 200) {
                                    febs.alert.success('删除成功');
                                    table.reload('detailBomList');
                                }
                            })
                        })
                    }
                } else {
                    if (bomType != 1) {
                        febs.alert.warn("请至少选择一条型材数据");
                        return false;
                    }
                    let sbdiIds = []
                    let flg = true
                    $.each(checkStatus.data, function (i, v) {
                        if (v.applyStatus == '1') {
                            febs.alert.warn(v.sectionBarNo + "型材已做物资申请，无法删除");
                            flg = false;
                            return false;
                        }
                        sbdiIds.push(v.sbdbiId);
                    })
                    if (flg) {
                        febs.modal.confirm('删除', '是否确定删除选中的型材', function () {
                            febs.get(ctx + 'bom/shipBomDetailInfo/removeBarInfo', {
                                sbdiIds: sbdiIds.join(','),
                                bomId: checkStatus.data[0].sbbiId
                            }, function (e) {
                                if (e.code === 200) {
                                    febs.alert.success('删除成功');
                                    table.reload('detailBomList')
                                }
                            })
                        })
                    }

                }

            }
            //触发排序事件
            table.on('sort(detailBomList)', function (obj) {
                if (sbbiId != null) {
                    let param = { sbiId: sbbiId }
                    param.order = obj.type;//desc;asc;null
                    tableInsDetail.reload({
                        initSort: obj,
                        where: param,
                        url: ctx + 'bom/shipBomDetailInfo/getBarInfoById',
                        // page: {curr: 1}
                    });
                }
                if (sbiId != null) {
                    let param = { sbiId: sbiId }
                    param.order = obj.type;//desc;asc;null
                    tableInsDetail.reload({
                        initSort: obj,
                        where: param,
                        url: ctx + 'bom/shipBomDetailInfo/getSteelInfoById',
                        // page: {curr: 1}
                    });
                }
            })
            //创建余料table 可以是型材 可以是钢板
            function createSurplusTable(data, type) {
                let colArr = [];
                if (0 === type) {
                    //钢板
                    colArr = [
                        [
                            { type: 'checkbox' },
                            { field: 'shipNo', title: '船号', align: 'center', width: 80 },
                            { field: 'surplusSteelNo', title: '余料钢板编码', align: 'center', width: 130 },
                            { field: 'surplusSteelDesc', title: '余料钢板描述', align: 'center', width: 130 },
                            {
                                field: 'bmId', title: '加工分段', align: 'center', width: 80, templet: function (d) {
                                    if (d.bmId && !d.useType) {
                                        let val = "";
                                        val = bmMap.get("bm_" + d.bmId) || bmMap.get("xc_" + d.bmId);
                                        return val;
                                    } else if (d.bmId && d.useType) {
                                        let val = "";
                                        val = bmMap.get("maGB_" + d.bmId) || bmMap.get("maXC_" + d.bmId);
                                        return val;
                                    } else {
                                        return "";
                                    }
                                }
                            },
                            {
                                field: 'bomNo', title: 'BOM编号', align: 'center', width: 130, templet: function (d) {
                                    return '<div style="text-align: left">' + d.bomNo + '</div>'
                                }
                            },
                            {
                                field: 'bomDesc', title: 'BOM描述', align: 'center', width: 130, templet: function (d) {
                                    return '<div style="text-align: left">' + (null != d.bomDesc ? d.bomDesc : '') + '</div>'
                                }
                            },

                            {
                                field: 'steelNo', title: '母钢板编码', align: 'center', width: 130, templet: function (d) {
                                    return '<div style="text-align: left">' + d.steelNo + '</div>'
                                }
                            },
                            { field: 'nestingNo', title: '套料号', align: 'center', width: 100 },

                            {
                                field: 'basicMaterialNo', title: '物资编码', align: 'center', minWidth: 130, templet: function (d) {
                                    return '<div style="text-align: left">' + d.basicMaterialNo + '</div>'
                                }
                            },
                            {
                                field: 'materialDesc', title: '物资描述', align: 'center', minWidth: 130, templet: function (d) {
                                    return '<div style="text-align: left">' + d.materialDesc + '</div>'
                                }
                            },
                            { field: 'length', title: '余料长度', align: 'center', minWidth: 130 },
                            { field: 'width', title: '余料宽度', align: 'center', minWidth: 130 },
                            { field: 'thickness', title: '余料厚度', align: 'center', minWidth: 130 },
                            { field: 'shape', title: '余料形状', align: 'center', minWidth: 130 },
                            {
                                field: 'useStatus', title: '是否使用', align: 'center', minWidth: 130, templet: function (d) {
                                    if (d.useStatus == '1') {
                                        return '是'
                                    } else {
                                        return '否'
                                    }
                                }
                            },
                            { field: 'remark', title: '备注', align: 'center', minWidth: 130 },
                        ]
                    ];
                } else {
                    //型材
                    colArr = [
                        [
                            { type: 'checkbox' },
                            { field: 'shipNo', title: '船号', align: 'center', width: 80 },
                            { field: 'surplusBarNo', title: '余料型材编码', align: 'center', width: 130 },
                            { field: 'surplusBarDesc', title: '余料型材描述', align: 'center', width: 130 },
                            {
                                field: 'blockId', title: '大组分段', align: 'center', width: 80, templet: function (d) {
                                    if (d.blockId && !d.useType) {
                                        return blockMap.get("block_" + d.blockId);
                                    } else if (d.blockId && d.useType) {
                                        return blockMap.get("ma_" + d.blockId);
                                    } else {
                                        return "";
                                    }
                                }
                            },
                            { field: 'useType', title: '材料使用类型', align: 'center', width: 100 },
                            { field: 'bomNo', title: 'BOM编号', align: 'center', width: 130 },
                            { field: 'bomDesc', title: 'BOM描述', align: 'center', width: 130 },
                            { field: 'sectionBarNo', title: '母型材编码', align: 'center', width: 130 },
                            { field: 'sectionBarDesc', title: '母型材描述', align: 'center', width: 130 },
                            { field: 'basicMaterialNo', title: '物资编码', align: 'center', minWidth: 130 },
                            { field: 'materialDesc', title: '物资描述', align: 'center', minWidth: 130 },
                            { field: 'nestingNo', title: '套料号', align: 'center', width: 100 },
                            { field: 'length', title: '余料型材长度', align: 'center', minWidth: 130 },
                            { field: 'shape', title: '余料型材形状', align: 'center', minWidth: 130 },
                            {
                                field: 'useStatus', title: '是否使用', align: 'center', minWidth: 130, templet: function (d) {
                                    if (d.useStatus == '1') {
                                        return '是'
                                    } else {
                                        return '否'
                                    }
                                }
                            },
                            { field: 'remark', title: '备注', align: 'center', minWidth: 130 },
                        ]
                    ];
                }
                changeTable = febs.table.init({
                    elem: $view.find('#changeTable'),
                    id: 'changeTable',
                    defaultToolbar: [],
                    toolbar: '#paintPeBlockRestToolbar',
                    data: data,
                    cols: colArr,
                    autoSort: true,
                    sort: true,
                    page: false
                })
            }

            // 头部工具栏事件
            table.on('toolbar(changeTable)', function (obj) {
                let options = obj.config; // 获取当前表格属性配置项
                let checkStatus = table.checkStatus(options.id); // 获取选中行相关数据
                // 根据不同的事件名进行相应的操作
                switch (obj.event) { // 对应模板元素中的 lay-event 属性值
                    case 'addSurplusBtn':
                        addSurplusBtn();
                        break;
                    case 'searchSurplus':
                        searchSurplus();
                        break;
                    case 'delYuliao':
                        delYuliao();
                        break;
                    case 'delSurplus': {
                        delSurplus();
                    }
                        break;
                    case 'querySurplus': {
                        let param = {}
                        if (tp == 1) {
                            param.sbbiId = sbbiId
                            param.basicMaterialNo = $wuziylNoSur.val();
                            //型材余料查询
                            changeTable.reload({
                                where: param,
                                url: ctx + 'bom/surplusMaterialBarInfo/listPage',
                                // page: {curr: 1}
                            })
                        } else {
                            param.sbiId = sbiId
                            param.basicMaterialNo = $wuziylNoSur.val();
                            //钢板余料查询
                            changeTable.reload({
                                where: param,
                                url: ctx + 'bom/surplusMaterialSteelInfo/listPage',
                                // page: {curr: 1}
                            })

                        }
                        $wuziylNoSur.val(param.basicMaterialNo);
                    }
                        break;
                }
                return false;
            });
            function searchSurplus() {
                if (tp == 1) {

                    changeTable.reload({
                        where: { sbbiId: obj.data.sbiId, nums: $('#wuziylNo').val() },
                        url: ctx + 'bom/surplusMaterialBarInfo/listPage',
                        // page: {curr: 1}
                    })
                    sbiId = null;
                    sbbiId = obj.data.sbiId
                } else {
                    changeTable.reload({
                        where: { sbiId: obj.data.sbiId, nums: $('#wuziylNo').val() },
                        url: ctx + 'bom/surplusMaterialSteelInfo/listPage',
                        // page: {curr: 1}
                    })
                    sbiId = obj.data.sbiId
                    sbbiId = null;
                }
            }
            function addSurplusBtn() {
                let checkStatus = table.checkStatus('bomList');
                let param = { data: {} };
                if (checkStatus.data.length == 0 && bomlistRow != null) {
                    param.data = bomlistRow;
                } else if (checkStatus.data.length == 1) {
                    param.data = checkStatus.data[0];
                } else {
                    febs.alert.warn("请选择一条BOM数据")
                    return false;
                }
                if (tp == 0) {
                    //钢板
                    addSurplusSteel(param);
                } else {
                    //型材
                    addSurplusMater(param);
                }
            }
            function delYuliao() {
                let checkStatus = table.checkStatus('changeTable');
                if (checkStatus.data.length == 0) {
                    febs.alert.warn("请选择一条余料数据");
                    return false;
                }

                let sbdiIds = []
                $.each(checkStatus.data, function (i, v) {
                    sbdiIds.push(v.sbdiId);
                })
                febs.modal.confirm('删除', '是否确定删除选中的余料', function () {
                    febs.get(ctx + 'bom/shipBomDetailInfo/removeSteelInfo', {
                        sbdiIds: sbdiIds.join(','),
                        bomId: checkStatus.data[0].sbiId
                    }, function (e) {
                        if (e.code === 200) {
                            febs.alert.success('删除成功');
                            table.reload('changeTable');
                        }
                    })
                })
            }
            //设置右击菜单
            $view.on('contextmenu', '.layui-tree-set', function (e) {
                if (editFlg) {
                    $this = $(this)
                    if ($this.parent().parent().data('id') === undefined) {
                        return false;
                    }
                    let parendId = $this.parent().parent().data('id').split('_')[1]
                    let dataType = $this.data('id').split('_')[0]
                    let dataId = $this.data('id').split('_')[1]
                    if (dataType === 'GB' || dataType === 'bmXC' || dataType === 'bm' || 'xc' == dataType || dataType.indexOf("ma") > -1) {
                        //右击加工分段生成右击菜单
                        $view.find("#mymenu").empty()
                        let html = ''
                        html += '<div id="addBom" data-type="' + dataType + '" data-bmid="' + dataId + '" data-shipid="' + shipId + '" data-blockid="' + parendId + '" style="margin-top: 5px">新增</div>'
                        $view.find("#mymenu").append(html)
                        if ($view.height() - e.clientY >= 38) {
                            $view.find('#mymenu').css({
                                display: 'block',
                                left: e.clientX,
                                top: e.clientY
                            })
                        } else {
                            $view.find('#mymenu').css({
                                display: 'block',
                                left: e.clientX,
                                top: e.clientY - 38
                            })
                        }
                    }
                }
                return false
            })
            //鼠标离开隐藏右击菜单
            $view.on("mouseleave", "#mymenu", function () {
                $view.find('#mymenu').css('display', 'none')
            })

            //跳转BOM物资生成页面
            $view.on('click', '#addGoods', function () {
                let checkStatus = table.checkStatus('bomList');
                if (checkStatus.data.length !== 1) {
                    febs.alert.warn('请选择一条船体BOM数据');
                    return false;
                }
                if (checkStatus.data[0].status == '3') {
                    febs.alert.warn('已发放船体BOM不能再新增物资');
                    return false;
                }
                febs.modal.open('新增BOM物资', 'bom/shipBomManage/materialSelect', {
                    area: ['80%', '700px'],
                    btn: ['确定'],
                    data: { bomInfo: checkStatus.data[0] },
                    yes: function () {
                        $('#materialSelect').find('#submitMaterial').trigger('click');
                    }
                });
            })
            // 单元格编辑事件
            table.on('edit(bomList)', function (obj) {
                // $(obj.tr[0]).addClass('edited')
                saveBom(obj);
            });




            //导入船体BOM 隐藏
            $('#import').on('click', function () {
                let checkStatus = table.checkStatus('bomList');
                if (checkStatus.data[0] == undefined) {
                    febs.alert.warn("请先勾选需要导入的船体BOM")
                    return false
                }
                if (checkStatus.data[0].status == "3") {
                    febs.alert.warn("该船体BOM已发放")
                    return false
                }
                if (block != undefined) {
                    block.children.forEach(item => {
                        if (item.id.split("_")[1] == checkStatus.data[0].bmId) {
                            bmNo = item.name
                        }
                    })
                }
                febs.modal.open('导入', 'bom/shipBomManage/importShipBomDetail', {
                    btn: ['开始上传'],
                    area: ['700px', '480px'],
                    data: {
                        sbBomNo: checkStatus.data[0].sbBomNo,
                        bmNo: bmNo,
                        shipNo: shipNoSelect.getValue("name")[0]
                    },
                    yes: function (e) {
                        $('#shipMaterialImport').find('#test9').trigger('click');
                    }
                });
            });
            $('#import2').on('click', function () {
                febs.modal.open('导入', 'bom/shipBomManage/importShipBomDetails', {
                    btn: ['开始上传'],
                    area: ['700px', '480px'],
                    data: {
                        reloadParamForExcel: reloadParamForExcel
                    },
                    yes: function (e) {
                        $('#shipMaterialsImport').find('#test9').trigger('click');
                    }
                });
            });
            $('#importBar').on('click', function () {
                febs.modal.open('导入', 'bom/shipBomManage/importShipBomDetailBar', {
                    btn: ['开始上传'],
                    area: ['700px', '480px'],
                    data: {
                        reloadParamForExcel: reloadParamForExcel
                    },
                    yes: function (e) {
                        $('#shipMaterialsImport').find('#test9').trigger('click');
                    }
                });
            })

            //导出钢板
            $('#exportSteel').on('click', function () {
                if (shipNoSelect.getValue('value').length === 0) {
                    febs.alert.warn('请选择船号')
                    return false;
                }
                let checkStatus = table.checkStatus('bomList');
                let data = {};
                data.shipId = shipNoSelect.getValue('value')[0];
                data.blockId = block != undefined ? block.id.split("_")[1] : null;
                data.bmIds = bmIds;
                if (checkStatus.data.length >= 0) {
                    //勾选导出
                    let sbiIds = []
                    $.each(checkStatus.data, function (i, v) {
                        sbiIds.push(v.sbiId)
                    })
                    // data.sbiIds = sbiIds
                    data.sbiIdStr = sbiIds.join(',')
                }
                febs.download(ctx + 'bom/shipBomInfo/excel',
                    data,
                    '船体BOM钢板物资导出' + new Date().getTime() + '.xlsx');
            })

            //导出型材
            $('#exportBar').on('click', function () {
                if (shipNoSelect.getValue('value').length === 0) {
                    febs.alert.warn('请选择船号')
                    return false;
                }
                let checkStatus = table.checkStatus('bomList');
                let data = {};
                data.shipId = shipNoSelect.getValue('value')[0];
                data.blockId = block != undefined ? block.id.split("_")[1] : null;
                data.bmIds = bmIds;
                if (checkStatus.data.length >= 0) {
                    //勾选导出
                    let sbiIds = []
                    $.each(checkStatus.data, function (i, v) {
                        sbiIds.push(v.sbiId)
                    })
                    // data.sbiIds = sbiIds
                    data.sbiIdStr = sbiIds.join(',')
                }
                febs.download(ctx + 'bom/shipBomBarInfo/excel',
                    data,
                    '船体BOM型材物资导出' + new Date().getTime() + '.xlsx');
            })

            $('#delSteel').on('click', function () {
                let checkStatus = table.checkStatus('detailBomList');
                if (checkStatus.data.length == 0) {
                    febs.alert.warn("请至少选择一条钢板数据");
                    return false;
                }
                let bomType = checkStatus.data[0].bomType;
                if (bomType != 0) {
                    febs.alert.warn("请至少选择一条钢板数据");
                    return false;
                }
                let sbdiIds = []
                $.each(checkStatus.data, function (i, v) {
                    sbdiIds.push(v.sbdiId);
                })
                febs.modal.confirm('删除', '是否确定删除选中的钢板', function () {
                    febs.get(ctx + 'bom/shipBomDetailInfo/removeSteelInfo', {
                        sbdiIds: sbdiIds.join(','),
                        bomId: checkStatus.data[0].sbiId
                    }, function (e) {
                        if (e.code === 200) {
                            febs.alert.success('删除成功');
                            table.reload('detailBomList');
                        }
                    })
                })
            })

            $('#delBar').on('click', function () {
                let checkStatus = table.checkStatus('detailBomList');
                // console.log(checkStatus.data)
                if (checkStatus.data.length == 0) {
                    febs.alert.warn("请至少选择一条型材数据");
                    return false;
                }
                let bomType = checkStatus.data[0].bomType;
                if (bomType != 1) {
                    febs.alert.warn("请至少选择一条型材数据");
                    return false;
                }
                let sbdiIds = []
                $.each(checkStatus.data, function (i, v) {
                    sbdiIds.push(v.sbdbiId);
                })
                febs.modal.confirm('删除', '是否确定删除选中的型材', function () {
                    febs.get(ctx + 'bom/shipBomDetailInfo/removeBarInfo', {
                        sbdiIds: sbdiIds.join(','),
                        bomId: checkStatus.data[0].sbbiId
                    }, function (e) {
                        if (e.code === 200) {
                            febs.alert.success('删除成功');
                            table.reload('detailBomList')
                        }
                    })
                })
            })

            //新增bom
            $('#bomAdd').on('click', function () {
                if (shipNoSelect.getValue('value').length === 0) {
                    febs.alert.warn('请选择船号')
                    return false;
                }
                febs.modal.open('BOM新增', 'bom/shipBomManage/addBom', {
                    area: ['500px', '400px'],
                    btn: ['确定'],
                    data: {
                        shipId: shipNoSelect.getValue('value')[0]
                    },
                    yes: function () {
                        $('#shipBomAdd').find('#submitAddBom').trigger('click');
                    }
                });
            })


            reloadTableData = function (type, param) {
                if ('GB' == type) {
                    tableIns.reload({
                        where: param,
                        url: ctx + 'bom/shipBomInfo/page',
                        // page: {curr:1}
                    })
                } else if ('XC' == type) {
                    tableIns.reload(
                        {
                            where: param,
                            url: ctx + 'bom/shipBomBarInfo/page',
                            // page: {curr: 1}
                        }
                    );
                }
            }
            //弹出下拉框
            $view.find('.hideBtn').on('mouseover', function (e) {
                $(this).find("div").show()
            })
            $view.find('.hideBtn').on('mouseout', function (e) {
                $(this).find("div:gt(0)").hide()
            })

            // 初始化表格拖动调整高度功能 - 使用jQuery UI resizable
            initTableResize();

            function initTableResize() {
                var $mainBomTableContainer = $('#mainBomTableContainer');
                var $bottomTablesContainer = $('#bottomTablesContainer');
                var $detailBomTableContainer = $('#detailBomTableContainer');
                var $rightTablesContainer = $('#rightTablesContainer');
                var $changeTableContainer = $('#changeTableContainer');
                var $beforeShipTableContainer = $('#beforeShipTableContainer');

                // 第一个表格容器的拖拽调整 - 影响上方容器和下方整体容器
                $mainBomTableContainer.resizable({
                    handles: "s",
                    grid: 1,
                    minHeight: 150,
                    delay: 0,
                    distance: 1,
                    start: function () {
                        // 记录初始高度
                        this.startMainHeight = $mainBomTableContainer.height();
                        this.startBottomHeight = $bottomTablesContainer.height();
                    },
                    resize: function (event, ui) {
                        var heightChange = ui.size.height - this.startMainHeight;
                        var newBottomHeight = this.startBottomHeight - heightChange;

                        // 确保最小高度
                        if (newBottomHeight < 200) {
                            newBottomHeight = 200;
                            ui.size.height = this.startMainHeight + this.startBottomHeight - 200;
                        }

                        // 调整下方容器高度
                        $bottomTablesContainer.css('height', newBottomHeight + 'px');
                    }
                });

                // 左侧详情表格容器的拖拽调整 - 影响左右两个容器
                $detailBomTableContainer.resizable({
                    handles: "e",
                    grid: 1,
                    minWidth: 200,
                    delay: 0,
                    distance: 1,
                    start: function () {
                        // 记录初始宽度
                        this.startDetailWidth = $detailBomTableContainer.width();
                        this.startRightWidth = $rightTablesContainer.width();
                    },
                    resize: function (event, ui) {
                        var widthChange = ui.size.width - this.startDetailWidth;
                        var newRightWidth = this.startRightWidth - widthChange;

                        // 确保最小宽度
                        if (newRightWidth < 200) {
                            newRightWidth = 200;
                            ui.size.width = this.startDetailWidth + this.startRightWidth - 200;
                        }

                        // 调整右侧容器宽度
                        $rightTablesContainer.css('width', newRightWidth + 'px');
                    }
                });

                // 右侧上方表格容器的拖拽调整 - 影响右侧上下两个容器
                $changeTableContainer.resizable({
                    handles: "s",
                    grid: 1,
                    minHeight: 100,
                    delay: 0,
                    distance: 1,
                    start: function () {
                        // 记录初始高度
                        this.startChangeHeight = $changeTableContainer.height();
                        this.startBeforeShipHeight = $beforeShipTableContainer.height();
                    },
                    resize: function (event, ui) {
                        var heightChange = ui.size.height - this.startChangeHeight;
                        var newBeforeShipHeight = this.startBeforeShipHeight - heightChange;

                        // 确保最小高度
                        if (newBeforeShipHeight < 100) {
                            newBeforeShipHeight = 100;
                            ui.size.height = this.startChangeHeight + this.startBeforeShipHeight - 100;
                        }

                        // 调整下方容器高度
                        $beforeShipTableContainer.css('height', newBeforeShipHeight + 'px');
                    }
                });
            }

            let shipBomMaterialAddCurr_function = (event) => {
                document.getElementById('addBtn_ship').setAttribute("data-btn-event", 'curr')
                document.getElementById('addBtn_ship').click()
            }
            let shipBomMaterialAddBefore_function = (event) => {
                document.getElementById('addBtn_ship').setAttribute("data-btn-event", 'before')
                document.getElementById('addBtn_ship').click()
            }

        });
    </script>
</body>

</html>