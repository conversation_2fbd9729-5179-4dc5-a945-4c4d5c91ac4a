<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三级树结构拖拽管理</title>
    <!-- Layui CSS -->
    <link rel="stylesheet" href="https://unpkg.com/layui@2.8.18/dist/css/layui.css">
    <!-- jQuery UI CSS for drag and drop -->
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/ui-lightness/jquery-ui.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }

        .container {
            display: flex;
            height: 100vh;
            gap: 10px;
            padding: 10px;
            box-sizing: border-box;
        }

        .left-panel {
            flex: 1;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
            overflow: auto;
        }

        .right-panel {
            flex: 1;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
            overflow: auto;
        }

        .panel-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #1890ff;
        }

        /* 树结构样式 */
        .tree-container {
            max-height: calc(100vh - 150px);
            overflow-y: auto;
        }

        .tree-node {
            margin: 5px 0;
            user-select: none;
        }

        .tree-node-level1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 15px;
            border-radius: 6px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tree-node-level1:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .tree-node-level2 {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px 15px;
            margin: 8px 0 8px 20px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .tree-node-level2:hover {
            background: #e3f2fd;
            border-color: #1890ff;
        }

        .tree-node-level2.drop-target {
            background: #e8f5e8;
            border-color: #52c41a;
            border-width: 2px;
            border-style: dashed;
        }

        .tree-node-level2::before {
            content: '';
            position: absolute;
            left: -15px;
            top: 50%;
            width: 10px;
            height: 1px;
            background: #ccc;
        }

        .tree-node-level3 {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 8px 12px;
            margin: 5px 0 5px 40px;
            border-radius: 4px;
            font-size: 14px;
            position: relative;
        }

        .tree-node-level3::before {
            content: '';
            position: absolute;
            left: -25px;
            top: 50%;
            width: 20px;
            height: 1px;
            background: #ccc;
        }

        /* 右侧列表样式 */
        .items-list {
            max-height: calc(100vh - 150px);
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 10px;
        }

        .draggable-item {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border: 1px solid #ffb74d;
            padding: 12px 15px;
            margin: 8px 0;
            border-radius: 6px;
            cursor: move;
            transition: all 0.3s ease;
            position: relative;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .draggable-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 183, 77, 0.3);
        }

        .draggable-item.ui-draggable-dragging {
            transform: rotate(5deg);
            z-index: 1000;
            opacity: 0.8;
        }

        .draggable-item.selected {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            border-color: #0984e3;
        }

        .draggable-item.selected .item-code {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .draggable-item.hidden {
            display: none;
        }

        .checkbox-container {
            position: absolute;
            top: 8px;
            right: 8px;
        }

        .checkbox-container input[type="checkbox"] {
            width: 16px;
            height: 16px;
            cursor: pointer;
        }

        .item-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .item-name {
            font-weight: bold;
            color: #333;
        }

        .item-code {
            font-size: 12px;
            color: #666;
            background: rgba(255, 255, 255, 0.7);
            padding: 2px 6px;
            border-radius: 3px;
        }

        /* 操作按钮样式 */
        .action-buttons {
            margin-top: 20px;
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .layui-btn {
            margin: 0 5px;
        }

        /* 滚动条样式 */
        .tree-container::-webkit-scrollbar,
        .items-list::-webkit-scrollbar {
            width: 6px;
        }

        .tree-container::-webkit-scrollbar-track,
        .items-list::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .tree-container::-webkit-scrollbar-thumb,
        .items-list::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .tree-container::-webkit-scrollbar-thumb:hover,
        .items-list::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }

        /* 展开/收起图标 */
        .expand-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-right: 8px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .expand-icon.expanded {
            transform: rotate(90deg);
        }

        /* 统计信息 */
        .stats-info {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-size: 14px;
            color: #0369a1;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- 左侧树结构面板 -->
        <div class="left-panel">
            <div class="panel-title">
                <i class="layui-icon layui-icon-tree"></i>
                树结构管理
            </div>
            <div class="stats-info">
                <div>总计: <span id="total-count">0</span> 个三级节点</div>
                <div>已分配: <span id="assigned-count">0</span> 个 | 未分配: <span id="unassigned-count">0</span> 个</div>
            </div>
            <div class="tree-container" id="tree-container">
                <!-- 树结构将在这里动态生成 -->
            </div>
        </div>

        <!-- 右侧待分配列表面板 -->
        <div class="right-panel">
            <div class="panel-title">
                <i class="layui-icon layui-icon-list"></i>
                待分配的三级数据
            </div>
            <div class="stats-info">
                <div>待分配数量: <span id="pending-count">0</span> 个</div>
                <div>拖拽到左侧对应的二级节点进行分配</div>
            </div>

            <!-- 搜索和批量操作 -->
            <div style="margin-bottom: 15px;">
                <div class="layui-input-group" style="margin-bottom: 10px;">
                    <input type="text" id="search-input" placeholder="搜索项目名称或编码..." class="layui-input"
                        style="border-radius: 4px;">
                    <div class="layui-input-split layui-input-suffix" style="border: none;">
                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="clear-search">
                            <i class="layui-icon layui-icon-close"></i>
                        </button>
                    </div>
                </div>
                <div style="display: flex; gap: 5px; flex-wrap: wrap;">
                    <button class="layui-btn layui-btn-xs layui-btn-primary" id="select-all">全选</button>
                    <button class="layui-btn layui-btn-xs layui-btn-primary" id="select-none">取消全选</button>
                    <button class="layui-btn layui-btn-xs layui-btn-normal" id="batch-assign" disabled>批量分配</button>
                </div>
            </div>

            <div class="items-list" id="items-list">
                <!-- 待分配的三级数据将在这里显示 -->
            </div>
        </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
        <button class="layui-btn layui-btn-primary" id="reset-btn">
            <i class="layui-icon layui-icon-refresh"></i>
            重置
        </button>
        <button class="layui-btn layui-btn-normal" id="preview-btn">
            <i class="layui-icon layui-icon-preview"></i>
            预览结构
        </button>
        <button class="layui-btn" id="save-btn">
            <i class="layui-icon layui-icon-ok"></i>
            保存配置
        </button>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- jQuery UI -->
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
    <!-- Layui JS -->
    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>

    <script>
        // 模拟数据
        const mockData = {
            // 一级节点（只有一个）
            level1: {
                id: 'root',
                name: '船舶设计管理系统',
                children: []
            },
            // 二级节点（60-100个）
            level2: [],
            // 三级节点（400个，初始时未分配）
            level3: []
        };

        // 生成模拟的二级数据
        for (let i = 1; i <= 80; i++) {
            mockData.level2.push({
                id: `level2_${i}`,
                name: `设计专业${i}`,
                code: `SP${String(i).padStart(3, '0')}`,
                parentId: 'root',
                children: []
            });
        }

        // 生成模拟的三级数据
        for (let i = 1; i <= 400; i++) {
            mockData.level3.push({
                id: `level3_${i}`,
                name: `设计项目${i}`,
                code: `PRJ${String(i).padStart(4, '0')}`,
                description: `这是设计项目${i}的详细描述`,
                parentId: null // 初始时未分配
            });
        }

        // 当前的树结构数据
        let currentTreeData = JSON.parse(JSON.stringify(mockData));

        layui.use(['layer'], function () {
            const layer = layui.layer;

            // 初始化页面
            initPage();

            function initPage() {
                renderTree();
                renderPendingItems();
                updateStats();
                initDragAndDrop();
                bindEvents();
            }

            // 渲染树结构
            function renderTree() {
                const container = $('#tree-container');
                container.empty();

                // 渲染一级节点
                const level1Html = `
                    <div class="tree-node tree-node-level1" data-id="${currentTreeData.level1.id}">
                        <span class="expand-icon expanded">▶</span>
                        ${currentTreeData.level1.name}
                    </div>
                `;
                container.append(level1Html);

                // 渲染二级节点
                currentTreeData.level2.forEach(item => {
                    const level2Html = `
                        <div class="tree-node tree-node-level2" data-id="${item.id}" data-level="2">
                            <div class="item-info">
                                <span class="item-name">${item.name}</span>
                                <span class="item-code">${item.code}</span>
                            </div>
                            <div class="level3-container" data-parent="${item.id}"></div>
                        </div>
                    `;
                    container.append(level2Html);

                    // 渲染该二级节点下的三级节点
                    const level3Items = currentTreeData.level3.filter(l3 => l3.parentId === item.id);
                    const level3Container = container.find(`[data-parent="${item.id}"]`);

                    level3Items.forEach(l3Item => {
                        const level3Html = `
                            <div class="tree-node tree-node-level3" data-id="${l3Item.id}">
                                <div class="item-info">
                                    <span class="item-name">${l3Item.name}</span>
                                    <span class="item-code">${l3Item.code}</span>
                                </div>
                            </div>
                        `;
                        level3Container.append(level3Html);
                    });
                });
            }

            // 渲染待分配项目
            function renderPendingItems(searchTerm = '') {
                const container = $('#items-list');
                container.empty();

                const pendingItems = currentTreeData.level3.filter(item => !item.parentId);

                pendingItems.forEach(item => {
                    // 搜索过滤
                    const matchesSearch = !searchTerm ||
                        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        item.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        item.description.toLowerCase().includes(searchTerm.toLowerCase());

                    const itemHtml = `
                        <div class="draggable-item ${matchesSearch ? '' : 'hidden'}" data-id="${item.id}">
                            <div class="checkbox-container">
                                <input type="checkbox" class="item-checkbox" data-id="${item.id}">
                            </div>
                            <div class="item-info">
                                <span class="item-name">${item.name}</span>
                                <span class="item-code">${item.code}</span>
                            </div>
                            <div style="font-size: 12px; color: #666; margin-top: 5px; padding-right: 30px;">
                                ${item.description}
                            </div>
                        </div>
                    `;
                    container.append(itemHtml);
                });

                updateBatchAssignButton();
            }

            // 更新统计信息
            function updateStats() {
                const totalCount = currentTreeData.level3.length;
                const assignedCount = currentTreeData.level3.filter(item => item.parentId).length;
                const unassignedCount = totalCount - assignedCount;

                $('#total-count').text(totalCount);
                $('#assigned-count').text(assignedCount);
                $('#unassigned-count').text(unassignedCount);
                $('#pending-count').text(unassignedCount);
            }

            // 初始化拖拽功能
            function initDragAndDrop() {
                // 使待分配项目可拖拽
                $('#items-list .draggable-item').draggable({
                    helper: 'clone',
                    cursor: 'move',
                    opacity: 0.8,
                    revert: 'invalid',
                    start: function (event, ui) {
                        $(this).addClass('ui-draggable-dragging');
                        ui.helper.css({
                            'z-index': 1000,
                            'transform': 'rotate(5deg)',
                            'box-shadow': '0 8px 25px rgba(0,0,0,0.3)'
                        });
                    },
                    stop: function (event, ui) {
                        $(this).removeClass('ui-draggable-dragging');
                    }
                });

                // 使二级节点可接收拖拽
                $('.tree-node-level2').droppable({
                    accept: '.draggable-item',
                    hoverClass: 'drop-target',
                    tolerance: 'pointer',
                    drop: function (event, ui) {
                        const droppedItemId = ui.draggable.data('id');
                        const targetLevel2Id = $(this).data('id');

                        // 更新数据
                        const item = currentTreeData.level3.find(item => item.id === droppedItemId);
                        if (item) {
                            item.parentId = targetLevel2Id;

                            // 重新渲染
                            renderTree();
                            renderPendingItems();
                            updateStats();
                            initDragAndDrop(); // 重新初始化拖拽

                            // 显示成功消息
                            layer.msg(`已将 "${item.name}" 分配到 "${$(this).find('.item-name').text()}"`, {
                                icon: 1,
                                time: 2000
                            });
                        }
                    }
                });
            }

            // 更新批量分配按钮状态
            function updateBatchAssignButton() {
                const checkedCount = $('.item-checkbox:checked').length;
                const $batchBtn = $('#batch-assign');

                if (checkedCount > 0) {
                    $batchBtn.prop('disabled', false).text(`批量分配 (${checkedCount})`);
                } else {
                    $batchBtn.prop('disabled', true).text('批量分配');
                }
            }

            // 显示批量分配对话框
            function showBatchAssignDialog() {
                const checkedItems = $('.item-checkbox:checked');
                if (checkedItems.length === 0) {
                    layer.msg('请先选择要分配的项目！', { icon: 2 });
                    return;
                }

                // 生成二级节点选择列表
                let optionsHtml = '<option value="">请选择目标专业</option>';
                currentTreeData.level2.forEach(item => {
                    optionsHtml += `<option value="${item.id}">${item.name} (${item.code})</option>`;
                });

                const dialogHtml = `
                    <div style="padding: 20px;">
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">选择目标专业：</label>
                            <select id="target-level2" class="layui-input" style="width: 100%;">
                                ${optionsHtml}
                            </select>
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">将要分配的项目 (${checkedItems.length} 个)：</label>
                            <div style="max-height: 200px; overflow-y: auto; border: 1px solid #e6e6e6; padding: 10px; border-radius: 4px; background: #f9f9f9;">
                                ${Array.from(checkedItems).map(cb => {
                    const itemId = $(cb).data('id');
                    const item = currentTreeData.level3.find(i => i.id === itemId);
                    return `<div style="margin: 5px 0; padding: 5px; background: white; border-radius: 3px;">• ${item.name} (${item.code})</div>`;
                }).join('')}
                            </div>
                        </div>
                    </div>
                `;

                layer.open({
                    type: 1,
                    title: '批量分配项目',
                    area: ['500px', '400px'],
                    content: dialogHtml,
                    btn: ['确认分配', '取消'],
                    yes: function (index) {
                        const targetId = $('#target-level2').val();
                        if (!targetId) {
                            layer.msg('请选择目标专业！', { icon: 2 });
                            return;
                        }

                        // 执行批量分配
                        checkedItems.each(function () {
                            const itemId = $(this).data('id');
                            const item = currentTreeData.level3.find(i => i.id === itemId);
                            if (item) {
                                item.parentId = targetId;
                            }
                        });

                        // 重新渲染
                        renderTree();
                        renderPendingItems();
                        updateStats();
                        initDragAndDrop();

                        layer.close(index);
                        layer.msg(`成功分配 ${checkedItems.length} 个项目！`, { icon: 1 });
                    }
                });
            }

            // 绑定事件
            function bindEvents() {
                // 重置按钮
                $('#reset-btn').click(function () {
                    layer.confirm('确定要重置所有分配吗？', {
                        icon: 3,
                        title: '确认重置'
                    }, function (index) {
                        // 重置所有三级节点的parentId
                        currentTreeData.level3.forEach(item => {
                            item.parentId = null;
                        });

                        renderTree();
                        renderPendingItems();
                        updateStats();
                        initDragAndDrop();

                        layer.close(index);
                        layer.msg('重置成功！', { icon: 1 });
                    });
                });

                // 预览结构按钮
                $('#preview-btn').click(function () {
                    showPreview();
                });

                // 保存配置按钮
                $('#save-btn').click(function () {
                    saveConfiguration();
                });

                // 树节点展开/收起
                $(document).on('click', '.expand-icon', function (e) {
                    e.stopPropagation();
                    const $icon = $(this);
                    const $node = $icon.closest('.tree-node-level1');
                    const $level2Nodes = $node.nextAll('.tree-node-level2');

                    if ($icon.hasClass('expanded')) {
                        $icon.removeClass('expanded');
                        $level2Nodes.slideUp(300);
                    } else {
                        $icon.addClass('expanded');
                        $level2Nodes.slideDown(300);
                    }
                });

                // 搜索功能
                $('#search-input').on('input', function () {
                    const searchTerm = $(this).val().trim();
                    renderPendingItems(searchTerm);
                    initDragAndDrop();
                });

                // 清除搜索
                $('#clear-search').click(function () {
                    $('#search-input').val('');
                    renderPendingItems();
                    initDragAndDrop();
                });

                // 全选
                $('#select-all').click(function () {
                    $('.item-checkbox:visible').prop('checked', true);
                    $('.draggable-item:visible').addClass('selected');
                    updateBatchAssignButton();
                });

                // 取消全选
                $('#select-none').click(function () {
                    $('.item-checkbox').prop('checked', false);
                    $('.draggable-item').removeClass('selected');
                    updateBatchAssignButton();
                });

                // 批量分配
                $('#batch-assign').click(function () {
                    showBatchAssignDialog();
                });

                // 复选框变化事件
                $(document).on('change', '.item-checkbox', function () {
                    const $item = $(this).closest('.draggable-item');
                    if ($(this).is(':checked')) {
                        $item.addClass('selected');
                    } else {
                        $item.removeClass('selected');
                    }
                    updateBatchAssignButton();
                });

                // 点击项目选中/取消选中
                $(document).on('click', '.draggable-item', function (e) {
                    if ($(e.target).is('input[type="checkbox"]')) {
                        return; // 如果点击的是复选框，不处理
                    }

                    const $checkbox = $(this).find('.item-checkbox');
                    $checkbox.prop('checked', !$checkbox.is(':checked')).trigger('change');
                });
            }

            // 显示预览
            function showPreview() {
                let previewHtml = '<div style="max-height: 400px; overflow-y: auto;">';
                previewHtml += `<h3>${currentTreeData.level1.name}</h3>`;

                currentTreeData.level2.forEach(level2 => {
                    const level3Items = currentTreeData.level3.filter(item => item.parentId === level2.id);
                    previewHtml += `
                        <div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                            <strong>${level2.name} (${level2.code})</strong>
                            <span style="color: #666; margin-left: 10px;">[${level3Items.length} 个项目]</span>
                    `;

                    if (level3Items.length > 0) {
                        previewHtml += '<ul style="margin: 5px 0 0 20px; padding: 0;">';
                        level3Items.forEach(item => {
                            previewHtml += `<li style="list-style: none; margin: 3px 0; color: #333;">• ${item.name} (${item.code})</li>`;
                        });
                        previewHtml += '</ul>';
                    }
                    previewHtml += '</div>';
                });

                previewHtml += '</div>';

                layer.open({
                    type: 1,
                    title: '树结构预览',
                    area: ['600px', '500px'],
                    content: previewHtml
                });
            }

            // 保存配置
            function saveConfiguration() {
                const assignedCount = currentTreeData.level3.filter(item => item.parentId).length;
                const totalCount = currentTreeData.level3.length;

                if (assignedCount === 0) {
                    layer.msg('请至少分配一个三级项目！', { icon: 2 });
                    return;
                }

                layer.confirm(`确定保存当前配置吗？<br>已分配: ${assignedCount}/${totalCount} 个项目`, {
                    icon: 3,
                    title: '确认保存'
                }, function (index) {
                    // 这里可以发送数据到后端
                    const saveData = {
                        level1: currentTreeData.level1,
                        level2: currentTreeData.level2,
                        level3: currentTreeData.level3,
                        timestamp: new Date().toISOString()
                    };

                    // 模拟保存过程
                    layer.load(1);
                    setTimeout(() => {
                        layer.closeAll('loading');
                        layer.close(index);
                        layer.msg('保存成功！', { icon: 1 });
                        console.log('保存的数据:', saveData);
                    }, 1500);
                });
            }
        });
    </script>
</body>

</html>