<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns="http://www.w3.org/1999/html">

<head>
    <th:block th:include="include::header('详设计划完成率报表')" />
    <style>
        #shipId xm-select>.xm-body {
            width: 350px;
        }

        #designDrawFinishReport .shipNoText {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 180px;
            position: absolute;
            right: 10px;
        }

        #designDrawFinishReport #searchForm .layui-form-label {
            padding: 5px 5px;
            width: 73px;
        }

        #designDrawFinishReport .layui-tab-title li {
            line-height: 30px;
            border-right: 1px solid #f3f3f3;
        }

        #designDrawFinishReport .layui-tab-title .layui-this {
            background: #c9d6eb;
        }

        #designDrawFinishReport .layui-tab-title .layui-this:after {
            height: 100%;
        }

        #designDrawFinishReport .layui-tab {
            height: 90%;
            overflow: auto;
            margin: 0;
        }

        #designDrawFinishReport .layui-tab-content {
            padding: 1px 0 15px 0;
        }

        #designDrawFinishReport #shipMaterialTotalTable th,
        #designDrawFinishReport #shipMaterialTotalTable td {
            text-align: center;
            font-size: 14px;
        }

        #designDrawFinishReport .topOption {
            width: 48.1%;
            height: 100%;
            font-size: 18px;
            font-weight: bold;
            background: #f0f2f5;
            color: #000000a6;
        }

        .innerSelect {
            top: -10px;
            width: 188px;
            height: 30px;
            display: flex;
            align-items: center;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin-left: 17px;
            color: #5ca4f1;
        }

        .innerUnselect {
            top: -10px;
            width: 188px;
            height: 30px;
            display: flex;
            align-items: center;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin-left: 17px;
        }

        .shipNoSelectText {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 150px;
            position: absolute;
            right: 10px;
        }

        #searchForm .layui-form-item .layui-input-inline {
            margin-right: 5px !important;
            width: 170px !important;
        }

        .section-header {
            display: flex;
            align-items: center;
            white-space: nowrap;
        }

        .section-header span {
            white-space: nowrap;
        }

        .layui-tab-title {
            background-color: unset;
        }
    </style>
    <link rel="stylesheet" th:href="@{/febs/views/css/commonZs.css}" media="all">
</head>

<body>
    <div class="layui-fluid layui-anim febs-anim page-body" id="designDrawFinishReport" lay-title="详设计划完成率报表"
        style="height: calc( 100% - 15px);">
        <form class="layui-form search-form" id="searchForm">
            <div class="jhg-body-search">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm" for="startDate">计划开始:</label>
                        <div class="layui-input-inline" style="width:140px;">
                            <input type="text" name="startDate" id="startDate" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm" for="endDate">计划结束:</label>
                        <div class="layui-input-inline" style="width:140px;">
                            <input type="text" name="endDate" id="endDate" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm" style="width: 72px !important;">船号:</label>
                        <div class="layui-input-inline" style="width: 240px;">
                            <div id="shipNoSelect"></div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm">船型区域:</label>
                        <div class="layui-input-inline">
                            <div id="shipRegionSelect"></div>
                        </div>
                    </div>
                    <div class="layui-inline" id="detailMajor">
                        <label class="layui-form-label layui-form-label-sm">设计专业:</label>
                        <div class="layui-input-inline">
                            <div id="detailMajorSelect"></div>
                        </div>
                    </div>
                    <div class="layui-inline" id="xsWorkUser">
                        <label class="layui-form-label layui-form-label-sm">作业人员:</label>
                        <div class="layui-input-inline">
                            <div id="workUserSelect"></div>
                        </div>
                    </div>
                    <div class="layui-inline" id="nodeIdDiv">
                        <label class="layui-form-label layui-form-label-sm">节点信息 :</label>
                        <div class="layui-input-inline">
                            <div id="nodeId"></div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div id="query" class="layui-btn searchBlue layui-btn-sm" style="margin-left: 5px">
                            <em class="layui-icon">&#xe615;</em> 检索
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div id="export" class="layui-btn layui-btn-sm blueBtm">
                            <i class="layui-icon layui-icon-export"></i> 导出
                        </div>
                    </div>
                </div>
            </div>
        </form>
        <div id="detailDesignDiv" style="background-color: white;display: flex;height: 90%;overflow: auto">
            <div class="layui-col-md6" style="height: 100%;">
                <div class="jhg-body-table layui-card" id="detailCard1" style="height:calc(100% - 20px);">
                    <div class="layui-card-header popTitle" id="detailPopTitleDiv1">
                        <label></label>
                        <span>科室图纸完成情况</span>
                    </div>
                    <table id="detailDesignTable" lay-filter="detailDesignTable" lay-data="{id: 'detailDesignTable'}">
                    </table>
                    <div id="detailEchartsTab" style="width: 100%;height: 350px;top:0"></div>
                </div>
            </div>
            <div class="layui-col-md6" style="height: 100%;">

                <div class="jhg-body-table layui-card" id="detailCard3" style="height:calc(100% - 20px);">
                    <div class="layui-card-header popTitle" id="detailPopTitleDiv3">
                        <label></label>
                        <div class="section-header">
                            <span>图纸清单信息</span>
                        </div>
                        <div style="margin-left:0;margin-top: 2px;width: 92%;display: contents;">
                            <div class="innerSelect" id="detailFinishedNum" onclick="delayCompleteShow(this)"></div>
                            <div class="innerUnselect" id="detailUnFinishedNum" onclick="delayInCompleteShow(this)">
                            </div>
                        </div>
                    </div>
                    <table id="detailProDevicePlanTable" lay-filter="detailProDevicePlanTable"
                        lay-data="{id: 'detailProDevicePlanTable'}">
                    </table>
                </div>
            </div>
        </div>
    </div>
    <th:block th:include="include::foot" />
    <script data-th-inline="none" type="text/javascript">
        layui.config({
            base: ctx + 'febs/'
        }).extend({
            febs: 'lay/modules/febs',
            validate: 'lay/modules/validate',
            formSelects: 'lay/extends/formSelects-v4.min',
            jqueryui: 'lay/extends/jquery-ui.min',
            echarts: 'lay/extends/echarts.min',
            commonJS: 'lay/extends/common'
        }).use(['tree', 'jquery', 'validate', 'table', 'laydate', 'form', 'febs', 'commonJS', 'element', 'rpcJs'], function () {
            var $ = layui.jquery,
                $view = $('#designDrawFinishReport'),
                febs = layui.febs,
                form = layui.form,
                laydate = layui.laydate,
                commonJS = layui.commonJS,
                rpcJs = layui.rpcJs,
                shipNoSelect,
                allShips = [],
                shipRegionsArr,
                ddptMap = new Map(), //详设专业
                detailMajorSelect,//详设专业
                shipRegionSelect,//船型区域
                detailDesigns,
                workUserSelect,
                nodeSelect,
                checkArr = [],
                verifyArr = [],
                pwdMap = new Map(),
                pdpdMap = new Map(),
                pptdMap = new Map(),
                regionsMap = new Map(),
                detailFinishedList,
                detailUnFinishedList,
                cardDiv = 'detail';

            // 日期组件
            laydate.render({
                elem: '#startDate',
            });

            laydate.render({
                elem: ' #endDate',
            });

            form.render();

            //查询
            $('#query').on('click', function () {
                if (!$view.find("#startDate").val()) {
                    febs.alert.warn('请选择计划开始日期')
                    return false;
                }
                if (!$view.find("#endDate").val()) {
                    febs.alert.warn('请选择计划结束日期')
                    return false;
                }

                let url = ctx + 'plan/xsDesignDrawInfo/getXsPlanFinishReport'

                febs.getSyn(url, getQueryParams(), function (res) {
                    let data = res.data
                    detailFinishedList = data.overTimeFinishedList || []
                    detailUnFinishedList = data.overTimeUnFinishedList || []
                    // 初始化计划完成率表格
                    const tableData = {
                        planTypeList: data.designPlanTypeDtoList || []
                    }
                    initDetailDesignTable(tableData);
                    // 初始化山积图
                    const echarsData = {
                        weekShowList: data.weekShowList || [],
                        weekPlanFinishRateList: data.weekPlanFinishRateList || [],
                        weekActualFinishRateList: data.weekActualFinishRateList || []
                    }
                    initEcharts(echarsData);
                    // 初始化拖期完成份数和拖期未完成份数
                    const delayData = {
                        overTimeFinishedNum: data.overTimeFinishedNum || 0,
                        overTimeUnFinishedNum: data.overTimeUnFinishedNum || 0
                    }
                    initDelayDiv(delayData);
                    initProDevicePlanTable(detailFinishedList);
                })
            });

            //导出
            $('#export').on('click', function () {
                if (!$view.find("#startDate").val()) {
                    febs.alert.warn('请选择计划开始日期')
                    return false;
                }
                if (!$view.find("#endDate").val()) {
                    febs.alert.warn('请选择计划结束日期')
                    return false;
                }
                let url = ctx + 'plan/xsDesignDrawInfo/export'
                let excelName = '详设计划完成率报表'
                let param = getQueryParams()
                febs.download(url, param, excelName + new Date().getTime() + '.xlsx');
                console.log(param)
            })

            init();

            function init() {
                getCheckUserList();
                getVerifyUserList()
                initDict();
                getShipRegion(null)
                setXsWorkUser(null)
                setShipNo();
                setShipRegions([]);
            };

            function getCheckUserList() {
                let param = {}
                febs.postArraySync(ctx + 'plan/detailDesignProfessionTypeDict/detailCheck', param, function (e) {
                    if (e.code == 200) {
                        $.each(e.data, function (i, v) {
                            checkArr.push({
                                name: v.truename,
                                value: v.checkUserId,
                                ddptId: v.professionId
                            })
                        })
                    }
                })
            }

            function getVerifyUserList() {
                let param = {}
                febs.postArraySync(ctx + 'plan/detailDesignProfessionTypeDict/detailVerify', param, function (e) {
                    if (e.code == 200) {

                        $.each(e.data, function (i, v) {
                            verifyArr.push({
                                name: v.truename,
                                value: v.verifyUserId,
                                ddptId: v.professionId
                            })
                        })
                    }
                })
            }

            //设置详设作业人员
            function setXsWorkUser(ddptId) {
                let arr = []
                febs.postArraySync(ctx + 'plan/detailDesignProfessionTypeDict/detailWork', { ddptIds: ddptId }, function (data) {
                    if (data.code == 200) {
                        $.each(data.data, function (i, v) {
                            arr.push({
                                name: v.truename,
                                value: v.userId,
                            })
                        })
                    }
                })
                workUserSelect = xmSelect.render({
                    el: '#workUserSelect',
                    data: arr,
                    filterable: true,
                    toolbar: {
                        show: true
                    },
                    tips: "请选择",
                    radio: false,
                    clickClose: false
                })
            }

            //船号选择
            function setShipNo() {
                let arr = [];
                let resp = rpcJs.getShipDataList();
                if (resp.code == 200) {
                    allShips = resp.data;
                    $.each(resp.data, function (i, v) {
                        arr.push({
                            name: v.shipNo,
                            value: v.shipId,
                            showname: v.showName,
                            typeId: v.typeId
                        })
                    })
                }
                shipNoSelect = xmSelect.render({
                    el: '#shipNoSelect',
                    data: arr,
                    filterable: true,
                    toolbar: {
                        show: true
                    },
                    template({ item }) {
                        // return item.name + '<span style="position: absolute;right: 10px;color: #90a4ae">' + item.showname + '</span>'
                        return item.name + '<span class="shipNoSelectText" title="' + item.showname + '">' + item.showname + '</span>'
                    },
                    model: {
                        label: {
                            block: {
                                template(item, sels) {
                                    return item.name + '(' + item.showname + ')'
                                }
                            }
                        }
                    },
                    radio: false,
                    clickClose: false,
                    on: function (data) {
                        if (data.isAdd) {
                            let shipTypeIds = data.arr.map(v => v.typeId).join()
                            let arr = getShipRegion(shipTypeIds)
                            shipRegionsArr = arr;
                            setShipRegions(arr);
                            //initTime(data.arr.map(v => v.value).join())
                        } else {
                            setShipRegions([]);
                        }
                    }
                })
            }

            function setShipRegions(arr) {
                shipRegionSelect = xmSelect.render({
                    el: '#shipRegionSelect',
                    data: arr,
                    filterable: true,
                    toolbar: {
                        show: true
                    },
                    tips: "请选择",
                    template({ item }) {
                        // return item.name + '<span style="position: absolute;right: 10px;color: #90a4ae">' + item.showname + '</span>'
                        return item.name + '<span class="shipNoSelectText" title="' + item.showname + '">' + item.showname + '</span>'
                    },
                    radio: false,
                    clickClose: false
                })
            }

            //设置船型区域
            function getShipRegion(shipTypeIds) {
                let arr = []
                febs.getSyn(ctx + 'basic/shipTypeArea/shipTypeRegionDictByTypeIds', { typeIds: shipTypeIds }, function (e) {
                    if (e.code == 200) {
                        $.each(e.data, function (i, v) {
                            regionsMap.set(v.strId, v.strCodeName);
                            arr.push({
                                name: v.strCodeName,
                                value: v.strId,
                                showname: v.strCode
                            })
                        })
                    }
                })
                return arr
            }

            //初始化字典数据
            function initDict() {
                febs.get(ctx + 'plan/detailDesignProfessionTypeDict', {}, function (e) {
                    if (e.code == 200) {
                        detailDesigns = [];
                        $.each(e.data, function (i, v) {
                            ddptMap.set(v.ddptId, v.ddptCodeName)
                            detailDesigns.push({
                                name: v.ddptCodeName,
                                value: v.ddptId,
                                showname: v.ddptCode
                            })
                        })
                        detailMajorSelect = xmSelect.render({
                            el: '#detailMajorSelect',
                            data: detailDesigns,
                            radio: false,
                            filterable: true,
                            toolbar: {
                                show: true
                            },
                            clickClose: false,
                            template({ item }) {
                                return item.name + '<span class="shipNoSelectText" title="' + item.showname + '">' + item.showname + '</span>'
                            },
                            on: function (data) {
                                if (data.isAdd) {
                                    setXsWorkUser(data.arr.map(v => v.value).join())
                                } else {
                                    setXsWorkUser(null)
                                }
                            }
                        })
                    }
                })

                febs.getSyn(ctx + 'plan/xsDrawNodeDict/all', null, function (e) {
                    if (String(e.code) === '200') {
                        const arr = [];
                        $.each(e.data, function (i, v) {
                            arr.push({
                                name: v.xdndName,
                                value: v.xdndId
                            })
                        })
                        nodeSelect = xmSelect.render({
                            el: '#nodeId',
                            data: arr,
                            toolbar: {
                                show: true
                            },
                            filterable: false
                        })
                    }
                })

                febs.get(ctx + 'plan/produceDesignWorkDict/all', {}, function (e) {
                    if (e.code == 200) {
                        $.each(e.data, function (i, v) {
                            pwdMap.set(v.pdwId, v.pdwCodeName)
                        })
                    }
                })

                febs.get(ctx + 'plan/produceDesignProgressDict/all', {}, function (e) {
                    if (e.code == 200) {
                        $.each(e.data, function (i, v) {
                            pdpdMap.set(v.pdpdId, v.pdpdCodeName)
                        })
                    }
                })

                febs.get(ctx + 'plan/produceProfessionTypeDict/all', {}, function (e) {
                    if (e.code == 200) {
                        $.each(e.data, function (i, v) {
                            pptdMap.set(v.pptdId, v.pptdCodeName)
                        })
                    }
                })
            }

            function initTime(shipIdList) {
                febs.getSyn(ctx + 'plan/designMainPlanInfo/getStartAndEndDate', { shipId: shipIdList.join(',') }, function (data) {
                    if (data.code == 200) {
                        laydate.render({
                            elem: '#startDate',
                            value: commonJS.formatDate(new Date(data.data.minDate), 'yyyy-MM-dd')
                        });
                        laydate.render({
                            elem: ' #endDate',
                            value: commonJS.formatDate(new Date(data.data.maxDate), 'yyyy-MM-dd')
                        });
                    }
                })
            }

            // 初始化计划完成率表格
            function initDetailDesignTable(data) {
                let tableId = cardDiv + 'DesignTable'
                febs.table.init({
                    elem: $view.find('#' + tableId),
                    height: data && data.planTypeList ? (data.planTypeList.length + 2) * 35 : 0,
                    defaultToolbar: [],
                    id: tableId,
                    css: [ // 重设当前表格样式
                        '.layui-table-tool-temp{padding-right: 145px;}'
                    ].join(''),
                    cellMinWidth: 80,
                    page: false,
                    data: data && data.planTypeList ? data.planTypeList : [],
                    cols: [
                        [
                            { field: 'typeName', title: '科室名称', minWidth: 100, align: 'center' },
                            { field: 'drawTotalNum', title: '图纸总数', minWidth: 100, align: 'center' },
                            { field: 'planFinishNum', title: '计划供图数', minWidth: 100, align: 'center' },
                            {
                                field: 'actualFinishNum',
                                title: '已供图数',
                                minWidth: 100,
                                align: 'center',
                                templet: function (d) {
                                    if (d.progressDesc === 'ANOMALY') {
                                        return `<span style="color: red">${d.actualFinishNum}</span>`
                                    } else {
                                        return `<span>${d.actualFinishNum}</span>`
                                    }
                                }
                            },
                            {
                                field: 'finishRate',
                                title: '供图比例',
                                minWidth: 100,
                                align: 'center',
                                templet: function (d) {
                                    return d.finishRate + '%'
                                }
                            },
                            { field: 'progressDesc', title: '供图进度描述', minWidth: 100, align: 'center' }
                        ]
                    ]
                })
            }

            // 初始化山积图
            function initEcharts(data) {
                let echartId = cardDiv + 'EchartsTab'
                let echartsImg = echarts.init(document.getElementById(echartId));
                echartsImg.clear();
                const calendarList = data.weekShowList
                const seriesData = [
                    {
                        name: "出图实绩",
                        type: "line",
                        data: data.weekActualFinishRateList,
                        itemStyle: { color: 'red' },
                        lineStyle: { type: 'solid' }
                    },
                    {
                        name: "出图计划",
                        type: "line",
                        data: data.weekPlanFinishRateList,
                        itemStyle: { color: 'green' },
                        lineStyle: { type: 'dashed' }
                    }
                ];
                let option = {
                    // 右上角工具比如缩放 切换折现图
                    toolbox: {
                        feature: {
                            // 数据缩放视图
                            dataZoom: {
                                show: true,
                                yAxisIndex: 'none',
                                dataBackground: {
                                    areaStyle: {
                                        color: "#ff0000"
                                    }
                                }
                            },
                            magicType: {
                                show: true,
                                type: ['bar', 'line']
                            },
                            saveAsImage: {
                                show: true
                            }
                        }
                    },
                    grid: {
                        borderWidth: '1',
                        borderColor: '#ccc',
                        width: '85%',
                        'y': 35,
                        'y2': 5,
                        left: '60px',
                        bottom: "60px",
                    },
                    tooltip: {
                        trigger: 'axis',
                    },
                    legend: {
                        data: ["出图实绩", "出图计划"],
                        x: 'center',
                        y: 'top'
                    },
                    xAxis: {
                        data: calendarList,
                        name: '日期'
                    },
                    yAxis: {
                        min: 0, // 设置y轴的最小值
                        max: 100, // 设置y轴的最大值
                        interval: 10, // 设置y轴的间隔
                        name: '图纸出图比例',
                        axisLabel: {
                            formatter: '{value}%'  // 在每个数值后面加一个百分号
                        }
                    },
                    series: seriesData,
                    // 下方拖动截取日期
                    dataZoom: [{
                        show: true,
                        start: 0,
                        end: 100
                    }]
                }
                /* 清楚之前生成的数据 by gaolin start*/
                echartsImg.setOption(option, true)
                /* 清楚之前生成的数据 by gaolin end*/
            }

            // 初始化拖期完成份数和拖期未完成份数
            function initDelayDiv(data) {
                const overTimeFinishedNum = data && data.overTimeFinishedNum ? data.overTimeFinishedNum : 0;
                const overTimeUnFinishedNum = data && data.overTimeUnFinishedNum ? data.overTimeUnFinishedNum : 0;
                $view.find('#' + cardDiv + 'FinishedNum').html(`拖期完成份数(${overTimeFinishedNum})`);
                $view.find('#' + cardDiv + 'UnFinishedNum').html(`拖期未完成份数(${overTimeUnFinishedNum})`);
                // setCardDivHeight();
            }

            // 初始化拖期pro信息表格
            function initProDevicePlanTable(data) {
                // 动态计算表格高度，适应容器高度
                let containerHeight = $view.find('#detailCard3').height();
                let headerHeight = $view.find('#detailPopTitleDiv3').outerHeight() || 60;
                let height = containerHeight - headerHeight - 10; // 减去头部高度和一些边距

                // 设置最小高度，确保表格可用
                if (height < 300) {
                    height = 300;
                }
                let tableId = cardDiv + 'ProDevicePlanTable'
                let cols = [
                    {
                        field: 'xsDesignDrawNo',
                        title: '设计图纸编号',
                        align: 'center',
                        minWidth: 170,
                        templet: function (d) {
                            return '<div style="text-align: left">' + d.xsDesignDrawNo + '</div>'
                        }
                    },
                    {
                        field: 'xsDesignDrawDesc',
                        title: '设计图纸描述',
                        align: 'center',
                        width: 250,
                        templet: function (d) {
                            return '<div style="text-align: left" title="' + d.xsDesignDrawDesc + '">' + d.xsDesignDrawDesc + '</div>'
                        }
                    },
                    {
                        field: 'strId', title: '船型区域', align: 'center', minWidth: 200, templet: function (d) {
                            return regionsMap.get(d.strId)
                        }
                    },
                    {
                        field: 'drawWorkUserInfoList',
                        title: '作业人员',
                        align: 'center',
                        minWidth: 130,
                        templet: function (d) {
                            if (d.xsDrawWorkUserInfoVoList != null) {
                                let truenameList = []
                                d.xsDrawWorkUserInfoVoList.forEach(userInfo => {
                                    truenameList.push(userInfo.truename);
                                })
                                return truenameList.join(',');
                            } else {
                                return ''
                            }
                        }
                    },
                    {
                        field: 'checkUsername',
                        title: '校验人员',
                        align: 'center',
                        minWidth: 80,
                        templet: function (d) {
                            if (d.checkUsername != null && d.checkUsername != undefined) {
                                return d.checkUsername
                            }
                            if (d.checkUserId != null && (d.checkUsername == null || d.checkUsername == undefined)) {
                                return '人员已删除'
                            }
                            if (d.checkUserId == null) {
                                return '';
                            }
                        }
                    },
                    {
                        field: 'verifyUsername',
                        title: '审核人员',
                        align: 'center',
                        minWidth: 80,
                        templet: function (d) {
                            if (d.verifyUsername != null && d.verifyUsername != undefined) {
                                return d.verifyUsername
                            }
                            if (d.verifyUserId != null && (d.verifyUsername == null || d.verifyUsername == undefined)) {
                                return '人员已删除'
                            }
                            if (d.verifyUserId == null) {
                                return '';
                            }
                        }
                    },
                    { field: 'totalHour', title: '总工时', align: 'center', width: 60 },
                    {
                        field: 'sscdFlg', title: '送审船东', align: 'center', minWidth: 80, templet: function (d) {
                            if (d.sscdFlg == 0) {
                                return '是'
                            } else {
                                return '否'
                            }
                        }
                    },
                    {
                        field: 'sscjFlg', title: '送审船检', align: 'center', minWidth: 80, templet: function (d) {
                            if (d.sscjFlg == 0) {
                                return '是'
                            } else {
                                return '否'
                            }
                        }
                    },
                    {
                        field: 'remark', title: '备注', align: 'center', minWidth: 120, templet: function (d) {
                            return '<div style="text-align: left">' + (d.remark == null ? '' : d.remark) + '</div>'
                        }
                    },
                    {
                        field: 'ddptId', title: '详设专业', align: 'center', minWidth: 100, templet: function (d) {
                            return ddptMap.get(d.ddptId) == null ? '详设专业已删除' : ddptMap.get(d.ddptId)
                        }
                    },
                    {
                        field: 'drawNeedFlg',
                        title: '有效状态',
                        align: 'center',
                        minWidth: 70,
                        templet: function (d) {
                            if (d.drawNeedFlg == 0) {
                                return '有效'
                            } else {
                                return '无效'
                            }
                        }
                    },
                ]
                febs.table.init({
                    elem: $view.find('#' + tableId),
                    height: height,
                    defaultToolbar: [],
                    id: tableId,
                    css: [ // 重设当前表格样式
                        '.layui-table-tool-temp{padding-right: 145px;}'
                    ].join(''),
                    cellMinWidth: 80,
                    data: data || [],
                    cols: [cols],
                    page: true,
                    limit: 20
                });
            }

            // 获取查询参数
            function getQueryParams() {
                let workUserIds = ''
                workUserIds = workUserSelect.getValue("valueStr")

                return {
                    shipIds: shipNoSelect.getValue("valueStr"),
                    shipRegionIds: shipRegionSelect.getValue("valueStr"),
                    ddptIds: detailMajorSelect.getValue("valueStr"),
                    workUserIds: workUserIds,
                    xdndIds: nodeSelect.getValue("valueStr"),
                    dateFrom: $view.find("#startDate").val(),
                    dateTo: $view.find("#endDate").val()
                }
            }

            // 拖期未完成点击事件
            window.delayCompleteShow = function (e) {
                let cardId = '#' + cardDiv + 'DesignDiv'
                let thisCard = $view.find(cardId)
                thisCard.find('.innerSelect').removeClass('innerSelect').addClass('innerUnselect'); // 先重置所有
                $(e).removeClass('innerUnselect').addClass('innerSelect'); // 再设置当前点击的
                // 初始化拖期pro信息表格
                initProDevicePlanTable(detailFinishedList);
            }

            // 拖期完成点击事件
            window.delayInCompleteShow = function (e) {
                let cardId = '#' + cardDiv + 'DesignDiv'
                let thisCard = $view.find(cardId)
                thisCard.find('.innerSelect').removeClass('innerSelect').addClass('innerUnselect'); // 先重置所有
                $(e).removeClass('innerUnselect').addClass('innerSelect'); // 再设置当前点击的
                // 初始化拖期pro信息表格
                initProDevicePlanTable(detailUnFinishedList);
            }
        });
    </script>
</body>

</html>