<style>
    #febs-index .welcome-info-wrapper {
        padding: .2rem;
        display: inline-block
    }

    #febs-index .welcome-info-wrapper .user-header {
        display: inline-block;
        vertical-align: middle
    }

    #febs-index .welcome-info-wrapper .user-header img {
        width: 5rem;
        margin: .5rem 1rem;
        border-radius: 50%
    }

    #febs-index .welcome-info-wrapper .user-info {
        display: inline-block;
        vertical-align: middle
    }


    #febs-index .welcome-info-wrapper .user-info .user-dept,
    #febs-index .welcome-info-wrapper .user-info .user-login-info {
        color: rgba(0, 0, 0, 0.45);
    }


    #febs-index .project {
        width: 180px;
        display: inline-block;
        text-align: right;
        font-size: 20px;
    }

    .layui-inline {
        position: relative;
        vertical-align: middle;
    }

    .topInfoBox {
        display: flex;
        flex-wrap: nowrap;
        height: 36px;
        align-items: center;
        background-color: #deefff;
    }

    .random-message {
        padding: 0 10px;
        background-color: #cee4fc;
        border-radius: 22px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
    }

    .loginInfo {
        width: 520px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: nowrap;

    }

    .loginDetail {
        width: 33%;
        height: 100%;
        display: flex;
        /*justify-content: center;*/
        position: relative;
        flex-wrap: nowrap;
    }

    .loginName {
        /*margin-top: 4px;*/
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .loginCount {
        width: 100%;
        display: flex;
        /*justify-content: center;*/
        align-items: center;
        color: #3d9eff;
        font-weight: bold;
    }

    .layui-table-view .layui-table td,
    .layui-table-view .layui-table th {
        padding: 0px 0px;
    }

    .layui-table-tool {
        margin-top: 10px;
    }

    .ssDelayTable .layui-table-tool {
        background: linear-gradient(135deg, #ff6b6b, #ff8e8e) !important;
        min-height: 30px;
        padding: 0px;
        border-radius: 8px 8px 0 0;
        box-shadow: 0 2px 4px rgba(255, 107, 107, 0.2);
    }

    .xsDelayTable .layui-table-tool {
        background: linear-gradient(135deg, #ff9f43, #ffb74d) !important;
        min-height: 30px;
        padding: 0px;
        border-radius: 8px 8px 0 0;
        box-shadow: 0 2px 4px rgba(255, 159, 67, 0.2);
    }

    .ssWeekTable .layui-table-tool {
        background: linear-gradient(135deg, #5dade2, #85c1e9) !important;
        min-height: 30px;
        padding: 0px;
        border-radius: 8px 8px 0 0;
        box-shadow: 0 2px 4px rgba(93, 173, 226, 0.2);
    }

    .xsWeekTable .layui-table-tool {
        background: linear-gradient(135deg, #48c9b0, #76d7c4) !important;
        min-height: 30px;
        padding: 0px;
        border-radius: 8px 8px 0 0;
        box-shadow: 0 2px 4px rgba(72, 201, 176, 0.2);
    }

    .ssMonthTable .layui-table-tool {
        background: linear-gradient(135deg, #f7dc6f, #f8e71c) !important;
        min-height: 30px;
        padding: 0px;
        border-radius: 8px 8px 0 0;
        box-shadow: 0 2px 4px rgba(247, 220, 111, 0.2);
    }

    .xsMonthTable .layui-table-tool {
        background: linear-gradient(135deg, #bb8fce, #d2b4de) !important;
        min-height: 30px;
        padding: 0px;
        border-radius: 8px 8px 0 0;
        box-shadow: 0 2px 4px rgba(187, 143, 206, 0.2);
    }

    /* 表格容器悬停效果 */
    .jhg-body-table {
        transition: all 0.3s ease;
    }

    .jhg-body-table:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
    }

    /* 表格内容区域样式 */
    .layui-table-view {
        border-radius: 8px;
        overflow: hidden;
    }

    /* 表格行悬停效果 */
    .layui-table tbody tr:hover {
        background-color: rgba(0, 0, 0, 0.02) !important;
    }

    /* 整体容器背景优化 */
    .layui-fluid {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }
</style>

<div class="layui-fluid layui-anim febs-anim-up" id="febs-index" lay-title="系统首页">
    <div class="layui-row layui-col-space8 febs-container" style="height: 100%;overflow: auto">
        <div class="layui-col-md12 layui-col-sm12 layui-col-xs12">
            <div class="layui-card" style="padding: 0px">
                <div class="layui-card-body layui-anim layui-anim-fadein" style="padding:0px">


                    <div class="layui-row welcome-info">
                        <div class="topInfoBox">


                            <div class="user-dept" style="margin-left: 20px;margin-right: 20px">
                                <span id="user-dept" style="font-size: 16px;font-weight: bold;"></span> | <span
                                    id="user-role" style="font-size: 16px;font-weight: bold;"></span>
                            </div>
                            <div class="random-message">
                                <span id="welcome-message"></span>
                            </div>

                            <div class="loginInfo">
                                <div class="loginDetail loginIP">
                                    <div class="loginName">今日IP：</div>
                                    <div class="loginCount" id="today-ip">100</div>
                                </div>
                                <div class="loginDetail loginToday">
                                    <div class="loginName">今日访问：</div>
                                    <div class="loginCount" id="today-visit-count">100</div>
                                </div>
                                <div class="loginDetail loginTotal">
                                    <div class="loginName">总访问量：</div>
                                    <div class="loginCount" id="total-visit-count">100</div>
                                </div>
                            </div>


                            <div class="user-login-info">
                                <i class="layui-icon layui-icon-time"></i>
                                上次登录时间：<span id="last-login-time">2019-05-23 18:45:12</span>
                            </div>

                        </div>


                    </div>
                </div>
            </div>
        </div>
        <!--        <div class="layui-col-md12 layui-col-sm12 layui-col-xs12">-->
        <!--            <div class="layui-card">-->
        <!--                <div class="layui-card-body">-->
        <!--                    <div id="chart" style="height: 350px"></div>-->
        <!--                </div>-->
        <!--            </div>-->
        <!--        </div>-->
        <div class="layui-inline" style="height: 100%;margin-top: 5px;width: 99.5%">
            <div class="layui-col-md4" style="height: 98%;padding: 0 8px;">
                <div class="layui-card"
                    style="height: 98%;box-shadow: 0 4px 12px rgba(0,0,0,0.1);border-radius: 12px;overflow: hidden;">
                    <div class="jhg-body-table ssDelayTable"
                        style="height: 50%;background-color: white;margin-bottom: 8px;border-radius: 8px;box-shadow: 0 2px 8px rgba(255, 107, 107, 0.15);"
                        id="ssDelayTableId">
                        <table class="layui-hide" id="ssDelayTable" lay-filter="ssDelayTable">
                        </table>
                    </div>
                    <div class="jhg-body-table xsDelayTable"
                        style="height: calc(50% - 8px);background-color: white;border-radius: 8px;box-shadow: 0 2px 8px rgba(255, 159, 67, 0.15);"
                        id="xsDelayTableId">
                        <table class="layui-hide" id="xsDelayTable" lay-filter="xsDelayTable">
                        </table>
                    </div>
                </div>
            </div>
            <div class="layui-col-md4" style="height: 98%;padding: 0 8px;">
                <div class="layui-card"
                    style="height: 98%;box-shadow: 0 4px 12px rgba(0,0,0,0.1);border-radius: 12px;overflow: hidden;">
                    <div class="jhg-body-table ssWeekTable"
                        style="height: 50%;background-color: white;margin-bottom: 8px;border-radius: 8px;box-shadow: 0 2px 8px rgba(93, 173, 226, 0.15);"
                        id="ssWeekTableId">
                        <table class="layui-hide" id="ssWeekTable" lay-filter="ssWeekTable">
                        </table>
                    </div>
                    <div class="jhg-body-table xsWeekTable"
                        style="height: calc(50% - 8px);background-color: white;border-radius: 8px;box-shadow: 0 2px 8px rgba(72, 201, 176, 0.15);"
                        id="xsWeekTableId">
                        <table class="layui-hide" id="xsWeekTable" lay-filter="xsWeekTable">
                        </table>
                    </div>
                </div>
            </div>

            <div class="layui-col-md4" style="height: 98%;padding: 0 8px;">
                <div class="layui-card"
                    style="height: 98%;box-shadow: 0 4px 12px rgba(0,0,0,0.1);border-radius: 12px;overflow: hidden;">
                    <div class="jhg-body-table ssMonthTable"
                        style="height: 50%;background-color: white;margin-bottom: 8px;border-radius: 8px;box-shadow: 0 2px 8px rgba(247, 220, 111, 0.15);"
                        id="ssMonthTableId">
                        <table class="layui-hide" id="ssMonthTable" lay-filter="ssMonthTable">
                        </table>
                    </div>
                    <div class="jhg-body-table xsMonthTable"
                        style="height: calc(50% - 8px);background-color: white;border-radius: 8px;box-shadow: 0 2px 8px rgba(187, 143, 206, 0.15);"
                        id="xsMonthTableId">
                        <table class="layui-hide" id="xsMonthTable" lay-filter="xsMonthTable">
                        </table>
                    </div>
                </div>
            </div>

        </div>
        <div class="layui-card" style="height: 45%">
            <div class="layui-card-body" style="height: 58%;background-color: white;">
                <div id="chart" style="height: 58%"></div>
            </div>
        </div>
        <div class="layui-card" style="height: 45%">
            <div class="layui-card-body" style="height: 58%;background-color: white;">
                <div id="chart1" style="height: 58%"></div>
            </div>
        </div>
        <div class="layui-card" style="height: 45%">
            <div class="layui-card-body" style="height: 58%;background-color: white;">
                <div id="chart2" style="height: 58%"></div>
            </div>
        </div>
    </div>
</div>
<script type="text/html" id="ssDelayToolbar">
    <div class="layui-btn-container">
        <label style="float: left;
    width: 4px;
    background-color: #ff4757 !important;
    display: block;
    height: 20px;margin-top: 5px;
    margin-right: 8px;border-radius: 2px;"></label>
        <label class="title-font"
               style="float: left;color: white;font-size: 16px;font-weight: 600;font-family: auto;text-shadow: 0 1px 2px rgba(0,0,0,0.3);">生设计划拖期清单</label>
    </div>
</script>
<script type="text/html" id="xsDelayToolbar">
    <div class="layui-btn-container">
        <label style="float: left;
    width: 4px;
    background-color: #ff6348 !important;
    display: block;
    height: 20px;margin-top: 5px;
    margin-right: 8px;border-radius: 2px;"></label>
        <label class="title-font"
               style="float: left;color: white;font-size: 16px;font-weight: 600;font-family: auto;text-shadow: 0 1px 2px rgba(0,0,0,0.3);">详设计划拖期清单</label>
    </div>
</script>
<script type="text/html" id="ssWeekToolbar">
    <div class="layui-btn-container">
        <label style="float: left;
    width: 4px;
    background-color: #3742fa !important;
    display: block;
    height: 20px;margin-top: 5px;
    margin-right: 8px;border-radius: 2px;"></label>
        <label class="title-font"
               style="float: left;color: white;font-size: 16px;font-weight: 600;font-family: auto;text-shadow: 0 1px 2px rgba(0,0,0,0.3);">生设计划七天内到期清单</label>
    </div>
</script>
<script type="text/html" id="xsWeekToolbar">
    <div class="layui-btn-container">
        <label style="float: left;
    width: 4px;
    background-color: #2ed573 !important;
    display: block;
    height: 20px;margin-top: 5px;
    margin-right: 8px;border-radius: 2px;"></label>
        <label class="title-font"
               style="float: left;color: white;font-size: 16px;font-weight: 600;font-family: auto;text-shadow: 0 1px 2px rgba(0,0,0,0.3);">详设计划七天内到期清单</label>
    </div>
</script>
<script type="text/html" id="ssMonthToolbar">
    <div class="layui-btn-container">
        <label style="float: left;
    width: 4px;
    background-color: #ffa502 !important;
    display: block;
    height: 20px;margin-top: 5px;
    margin-right: 8px;border-radius: 2px;"></label>
        <label class="title-font"
               style="float: left;color: white;font-size: 16px;font-weight: 600;font-family: auto;text-shadow: 0 1px 2px rgba(0,0,0,0.3);">生设计划一月内到期清单</label>
    </div>
</script>
<script type="text/html" id="xsMonthToolbar">
    <div class="layui-btn-container">
        <label style="float: left;
    width: 4px;
    background-color: #a55eea !important;
    display: block;
    height: 20px;
    margin-top: 5px;
    margin-right: 8px;border-radius: 2px;"></label>
        <label class="title-font"
               style="float: left;color: white;font-size: 16px;font-weight: 600;font-family: auto;text-shadow: 0 1px 2px rgba(0,0,0,0.3);">详设计划一月内到期清单</label>
    </div>
</script>
<script data-th-inline="javascript" type="text/javascript">
    layui.use(['apexcharts', 'febs', 'jquery', 'util', 'layer'], function () {
        var $ = layui.jquery,
            util = layui.util,
            $view = $('#febs-index'),
            febs = layui.febs,
            ssDelayTable,
            xsDelayTable,
            ssWeekTable,
            xsWeekTable,
            ssMonthTable,
            xsMonthTable,
            ssDelayData = [],
            xsDelayData = [],
            ssWeekData = [],
            xsWeekData = [],
            ssMonthData = [],
            xsMonthData = [],
            layer = layui.layer;
        console.log(currentUser);
        createTable();
        febs.get(ctx + 'index/' + currentUser.username, null, function (r) {
            handleSuccess(r.data);
        });

        febs.get(ctx + 'plan/ssDrawPlanInfo/getShipPlans', null, function (r) {
            handlePlanData(r.data)
        });

        function createTable() {
            febs.getSyn(ctx + "plan/ssDrawPlanInfo/getSsPlanTrackDelayList", null, function (d) {
                if (d.code == '200') {
                    ssDelayData = d.data
                }
            })

            febs.getSyn(ctx + "plan/xsDesignDrawInfo/getXsPlanDelayList", null, function (d) {
                if (d.code == '200') {
                    xsDelayData = d.data
                }
            })

            febs.getSyn(ctx + "plan/xsDesignDrawInfo/getXsPlanWeekOrMonthList", { weekOrMonth: 'week' }, function (d) {
                if (d.code == '200') {
                    xsWeekData = d.data
                }
            })
            febs.getSyn(ctx + "plan/xsDesignDrawInfo/getXsPlanWeekOrMonthList", { weekOrMonth: 'month' }, function (d) {
                if (d.code == '200') {
                    xsMonthData = d.data
                }
            })


            febs.getSyn(ctx + "plan/ssDrawPlanInfo/getSsPlanTrackWeekOrMonthList", { weekOrMonth: 'week' }, function (d) {
                if (d.code == '200') {
                    ssWeekData = d.data
                }
            })
            febs.getSyn(ctx + "plan/ssDrawPlanInfo/getSsPlanTrackWeekOrMonthList", { weekOrMonth: 'month' }, function (d) {
                if (d.code == '200') {
                    ssMonthData = d.data
                }
            })
            ssDelayTable = febs.table.init({
                elem: $view.find('#ssDelayTable'),
                id: 'ssDelayTable',
                defaultToolbar: [],
                height: $('#ssDelayTableId').height(),
                toolbar: '#ssDelayToolbar',
                data: ssDelayData,
                even: true,
                cols: [
                    [
                        { field: 'shipNo', title: '船号', align: 'center', width: 70 },
                        { field: 'designDrawNo', title: '图号', align: 'center', width: 180 },
                        { field: 'planFinishDate', title: '计划完成时间', align: 'center', width: 120 },
                        { field: 'realFinishDate', title: '实际完成时间', align: 'center', width: 120 },
                    ]
                ],
                autoSort: true,
                sort: true,
                page: false,
                done: function (res) {
                    $(this.elem).closest('.layui-table-view').find('.layui-table-tool').css('background-color: #deffff !important;')
                }
            })
            xsDelayTable = febs.table.init({
                elem: $view.find('#xsDelayTable'),
                id: 'xsDelayTable',
                defaultToolbar: [],
                toolbar: '#xsDelayToolbar',
                height: $('#xsDelayTableId').height(),
                data: xsDelayData,
                cols: [
                    [
                        { field: 'shipNo', title: '船号', align: 'center', width: 70 },
                        { field: 'xsDesignDrawNo', title: '图号', align: 'center', width: 180 },
                        { field: 'planEndDate', title: '计划完成时间', align: 'center', width: 120 },
                        { field: 'realEndDate', title: '实际完成时间', align: 'center', width: 120 },
                    ]
                ],
                even: true,
                autoSort: true,
                sort: true,
                page: false
            })
            ssWeekTable = febs.table.init({
                elem: $view.find('#ssWeekTable'),
                id: 'ssWeekTable',
                defaultToolbar: [],
                toolbar: '#ssWeekToolbar',
                height: $('#ssWeekTableId').height(),
                data: ssWeekData,
                cols: [
                    [
                        { field: 'shipNo', title: '船号', align: 'center', width: 80 },
                        { field: 'designDrawNo', title: '图号', align: 'center', width: 180 },
                        { field: 'planStartDate', title: '计划开始时间', align: 'center', width: 120 },
                        { field: 'planFinishDate', title: '计划完成时间', align: 'center', width: 120 },
                    ]
                ],
                even: true,
                autoSort: true,
                sort: true,
                page: false
            })
            xsWeekTable = febs.table.init({
                elem: $view.find('#xsWeekTable'),
                id: 'xsWeekTable',
                defaultToolbar: [],
                toolbar: '#xsWeekToolbar',
                height: $('#xsWeekTableId').height(),
                data: xsWeekData,
                cols: [
                    [
                        { field: 'shipNo', title: '船号', align: 'center', width: 80 },
                        { field: 'designDrawNo', title: '图号', align: 'center', width: 180 },
                        { field: 'planStartDate', title: '计划开始时间', align: 'center', width: 120 },
                        { field: 'planEndDate', title: '计划完成时间', align: 'center', width: 120 },
                    ]
                ],
                even: true,
                autoSort: true,
                sort: true,
                page: false
            })
            ssMonthTable = febs.table.init({
                elem: $view.find('#ssMonthTable'),
                id: 'ssMonthTable',
                defaultToolbar: [],
                toolbar: '#ssMonthToolbar',
                height: $('#ssMonthTableId').height(),
                data: ssMonthData,
                cols: [
                    [
                        { field: 'shipNo', title: '船号', align: 'center', width: 80 },
                        { field: 'designDrawNo', title: '图号', align: 'center', width: 180 },
                        { field: 'planStartDate', title: '计划开始时间', align: 'center', width: 120 },
                        { field: 'planFinishDate', title: '计划完成时间', align: 'center', width: 120 },
                    ]
                ],
                even: true,
                autoSort: true,
                sort: true,
                page: false
            })
            xsMonthTable = febs.table.init({
                elem: $view.find('#xsMonthTable'),
                id: 'xsMonthTable',
                defaultToolbar: [],
                toolbar: '#xsMonthToolbar',
                height: $('#xsMonthTableId').height(),
                data: xsMonthData,
                cols: [
                    [
                        { field: 'shipNo', title: '船号', align: 'center', width: 80 },
                        { field: 'designDrawNo', title: '图号', align: 'center', width: 180 },
                        { field: 'planStartDate', title: '计划开始时间', align: 'center', width: 120 },
                        { field: 'planEndDate', title: '计划完成时间', align: 'center', width: 120 },
                    ]
                ],
                even: true,
                autoSort: true,
                sort: true,
                page: false
            })
        }

        if (currentUser.flg == 1) {

            layer.open({
                title: '请修改密码',
                btn: ['确定'],
                area: ['30%', '30%'],
                content: '<div><p style="font-size: 20px;font-weight: bold">密码不符合要求，请先修改</p>' +
                    '<br>' +
                    '<p>集团最新密码要求：密码<span style="color: red">9位数以上</span>(建议10位)；  、小写字母、数字、特殊符号</p>' +
                    '</div>',
                yes: function (index, layero) {
                    layer.close(index)
                    febs.modal.view('请修改密码', 'password/update', {
                        area: $(window).width() <= 750 ? '90%' : '500px',
                        btn: ['确定'],
                        data: { flg: 1 },
                        yes: function () {
                            $('#user-password-update').find('#submit').trigger('click');
                        }
                    });
                }
            });
            $('.layui-layer-setwin').hide();
        }


        // febs.get(ctx + 'measureTableFill/getCurrentUserMeasuer', null, function (r) {
        //     if (!!r){
        //         r = r.data;
        //         $view.find("#fillProcess").html( r.fill.process);
        //         $view.find("#fillSmall").html( r.fill.small);
        //         $view.find("#fillBig").html( r.fill.big);
        //         $view.find("#fillPe").html( r.fill.pe);
        //         $view.find("#fillCarry").html( r.fill.carry);
        //         $view.find("#fill").html( r.fill.all);
        //         $view.find("#submitProcess").html( r.submit.process);
        //         $view.find("#submitSmall").html( r.submit.small);
        //         $view.find("#submitBig").html( r.submit.big);
        //         $view.find("#submitPe").html( r.submit.pe);
        //         $view.find("#submitCarry").html( r.submit.carry);
        //         $view.find("#submit").html( r.submit.all);
        //         $view.find("#check").html( r.check);
        //     }
        // });
        function formatDate(date) {
            let year = date.getFullYear();
            let month = String(date.getMonth() + 1).padStart(2, '0');
            let day = String(date.getDate()).padStart(2, '0');
            let hours = String(date.getHours()).padStart(2, '0');
            let minutes = String(date.getMinutes()).padStart(2, '0');
            let seconds = String(date.getSeconds()).padStart(2, '0');
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
        }

        function handleSuccess(data) {
            var hour = new Date().getHours();
            var time = hour < 6 ? '早上好' : (hour <= 11 ? '上午好' : (hour <= 13 ? '中午好' : (hour <= 18 ? '下午好' : '晚上好')));
            var welcomeArr = [
                '创建“零伤害”·“零事故”的工作场所。',
                '积极遵守安全法律法规和工作标准。',
                '节约能源、预防污染、推进产品环保化。',
                '自觉遵守环境法律法规。',
                '人人物物、危险予知。',
                '安全和健康是每个员工的自我需要和责任。',
                '我要安全，我会安全。',
                '安全  环保。',
                '落实防范对策，杜绝重复事故。',
                '工期再紧，安全优先。',
                '安全，从小事做起。',
                '依规则做事，按顺序作业。',
                '细心?小心?责任心，确保安全。',
                '安全就在我身边。',
                '危险预知，确认安全。',
                '在规定的时间，做好规定的事情。',
                '在正确的时间，做正确的事情。',
                '评估风险，正确作业，持续改善。',
                '自主确认，相互提醒，共同安全。',
                '发现问题，立即行动，持续改善。',
                '让安全成为习惯，让习惯更加安全。',
                '品质铸就品牌。',
                '强化品质管理，提升企业核心竞争力。',
                '质量是设计、制造出来的。',
                '第一次就把事情做对。',
                '不制造、不接受、不输送不合格品。',
                '不给下道工序添麻烦。',
                '质量靠团队协作。',
                '质量管理以人为本。',
                '发现问题，立即行动，持续改善。',
                '没有质量就没有客户。',
                '以独特性、先进性和创新性，构筑世界一流品牌。',
                '以人为本、诚信为上、同舟共济、精益求精。',
                '以稳健持久、多元战略和国际视野实现企业使命。',
                '精益管理, 优质高效。',
                '质量铸就品牌。',
                '安全与健康高于一切。',
                '人才是企业第一资源。',
                '清白做人，干净做事。',
                '创建“零伤害”·“零事故”的工作场所。',
                '人的安全与健康高于一切。',
                '确保安全是确保质量、成本、交货期的基础。',
                '在安全的作业环境中才能创造高质量的产品。',
                '人才是企业第一资源。',
                '安全是工作的前提。',
                '员工参与是必不可少的。',
                '打造和谐友善的工作环境。',
                '打造绿色环保产品的研发能力。',
                '打造消耗最低社会资源的建造方式。',
                '所有的风险都能加以控制。',
                '品质铸就品牌。',
                '强化品质管理，提升企业核心竞争力。',
                '安全与健康高于一切。'
            ];
            var index = Math.floor((Math.random() * welcomeArr.length));
            var welcomeMessage = time + '，<a id="febs-index-user">' + currentUser.truename + '</a>，' + welcomeArr[index];
            $view.find('#today-ip').text(data.todayIp).end()
                .find('#today-visit-count').text(data.todayVisitCount).end()
                .find('#total-visit-count').text(data.totalVisitCount).end()
                .find('#user-dept').text(currentUser.deptName ? currentUser.deptName : '暂无所属部门').end()
                .find('#user-role').text(currentUser.roleName ? currentUser.roleName : '暂无角色').end()
                .find('#last-login-time').text(currentUser.lastLoginTime ? formatDate(new Date(currentUser.lastLoginTime)) : '第一次访问系统').end()
                .find('#welcome-message').html(welcomeMessage).end()
                // .find('#user-avatar').attr('src', ctx + "febs/images/avatar/" + currentUser.avatar);
                .find('#user-avatar').attr('src', ctx + "sharefiles/avatar/" + currentUser.avatar);
        }

        function handlePlanData(data) {
            let bom = data['bom']
            let bomDelay = data['bomDelay'];
            let bomXData = [];
            let bomYData = [];
            let bomYDelayData = [];
            $.each(bom, function (i, v) {
                let hasDelay = false
                bomXData.push(v.shipNo)
                bomYData.push(v.count)
                $.each(bomDelay, function (index, item) {
                    if (item.shipNo === v.shipNo) {
                        bomYDelayData.push(item.count)
                        hasDelay = true
                    }
                })
                if (!hasDelay) {
                    bomYDelayData.push(0)
                }
            })
            var bomOptions = {
                chart: {
                    height: 350,
                    type: 'bar',
                    toolbar: {
                        show: false
                    }
                },
                plotOptions: {
                    bar: {
                        horizontal: false,
                        columnWidth: '32rem'
                    }
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    show: true,
                    width: 1,
                    colors: ['transparent']
                },
                series: [{
                    name: '总数',
                    data: bomYData,

                }, {
                    name: '拖期个数',
                    data: bomYDelayData,

                }],
                colors: ['#5470c6', '#ee6666'],
                xaxis: {
                    categories: bomXData,
                    axisTicks: {
                        show: true
                    },
                    axisBorder: {
                        show: true,
                        color: '#f1f1f1'
                    }
                },
                fill: {
                    opacity: 1
                },
                title: {
                    text: '托盘计划汇总',
                    align: 'left',
                    style: {
                        color: 'rgba(0, 0, 0, .65)'
                    }
                },
                tooltip: {
                    y: {
                        formatter: function (val) {
                            return val
                        }
                    }
                },
                grid: {
                    row: {
                        colors: ['transparent', 'transparent'],
                        opacity: 0.2
                    },
                    borderColor: '#f1f1f1'
                },
            };
            let draw = data['draw'];
            let drawDelay = data['drawDelay'];
            let drawXData = [];
            let drawYData = [];
            let drawYDelayData = [];
            $.each(draw, function (i, v) {
                let hasDelay = false
                drawXData.push(v.shipNo)
                drawYData.push(v.count)
                $.each(drawDelay, function (index, item) {
                    if (item.shipNo === v.shipNo) {
                        drawYDelayData.push(item.count)
                        hasDelay = true
                    }
                })
                if (!hasDelay) {
                    drawYDelayData.push(0)
                }
            })
            var drawOptions = {
                chart: {
                    height: 350,
                    type: 'bar',
                    toolbar: {
                        show: false
                    }
                },
                plotOptions: {
                    bar: {
                        horizontal: false,
                        columnWidth: '32rem'
                    }
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    show: true,
                    width: 1,
                    colors: ['transparent']
                },
                series: [{
                    name: '总数',
                    data: drawYData
                }, {
                    name: '拖期个数',
                    data: drawYDelayData,

                }],
                colors: ['#5470c6', '#ee6666'],
                xaxis: {
                    categories: drawXData,
                    axisTicks: {
                        show: true
                    },
                    axisBorder: {
                        show: true,
                        color: '#f1f1f1'
                    }
                },
                fill: {
                    opacity: 1
                },
                title: {
                    text: '设计计划汇总',
                    align: 'left',
                    style: {
                        color: 'rgba(0, 0, 0, .65)'
                    }
                },
                tooltip: {
                    y: {
                        formatter: function (val) {
                            return val
                        }
                    }
                },
                grid: {
                    row: {
                        colors: ['transparent', 'transparent'],
                        opacity: 0.2
                    },
                    borderColor: '#f1f1f1'
                },
            };
            let por = data['por'];
            let porDelay = data['porDelay'];
            let porXData = [];
            let porYData = [];
            let porYDelayData = [];
            $.each(por, function (i, v) {
                let hasDelay = false
                porXData.push(v.shipNo)
                porYData.push(v.count)
                $.each(porDelay, function (index, item) {
                    if (item.shipNo === v.shipNo) {
                        porYDelayData.push(item.count)
                        hasDelay = true
                    }
                })
                if (!hasDelay) {
                    porYDelayData.push(0)
                }
            })
            var porOptions = {
                chart: {
                    height: 350,
                    type: 'bar',
                    toolbar: {
                        show: false
                    }
                },
                plotOptions: {
                    bar: {
                        horizontal: false,
                        columnWidth: '32rem'
                    }
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    show: true,
                    width: 1,
                    colors: ['transparent']
                },
                series: [{
                    name: '总数',
                    data: porYData
                }, {
                    name: '拖期个数',
                    data: porYDelayData,

                }],
                colors: ['#5470c6', '#ee6666'],
                xaxis: {
                    categories: porXData,
                    axisTicks: {
                        show: true
                    },
                    axisBorder: {
                        show: true,
                        color: '#f1f1f1'
                    }
                },
                fill: {
                    opacity: 1
                },
                title: {
                    text: '协议计划汇总',
                    align: 'left',
                    style: {
                        color: 'rgba(0, 0, 0, .65)'
                    }
                },
                tooltip: {
                    y: {
                        formatter: function (val) {
                            return val
                        }
                    }
                },
                grid: {
                    row: {
                        colors: ['transparent', 'transparent'],
                        opacity: 0.2
                    },
                    borderColor: '#f1f1f1'
                },
            };

            new ApexCharts(
                document.querySelector("#chart"),
                drawOptions
            ).render();
            new ApexCharts(
                document.querySelector("#chart1"),
                porOptions
            ).render();
            new ApexCharts(
                document.querySelector("#chart2"),
                bomOptions
            ).render();
        }

        $view.on('click', '#febs-index-user', function () {
            febs.navigate("/user/profile");
        })

        $view.on('click', '#check', function () {
            febs.navigate("/measure/measureTableFillCheck");
        });

        $view.on('click', '#fillProcess', function () {
            febs.navigate("/measure/processMeasureTableFill");
        });
        $view.on('click', '#fillSmall', function () {
            febs.navigate("/measure/smallMeasureTableFill");
        });
        $view.on('click', '#fillBig', function () {
            febs.navigate("/measure/bigMeasureTableFill");
        });
        $view.on('click', '#fillPe', function () {
            febs.navigate("/measure/outPeMeasureTableFill");
        });
        $view.on('click', '#fillCarry', function () {
            febs.navigate("/measure/outCarryMeasureTableFill");
        });

        $view.on('click', '#submitProcess', function () {
            febs.navigate("/measure/processMeasureTableFill");
        });
        $view.on('click', '#submitSmall', function () {
            febs.navigate("/measure/smallMeasureTableFill");
        });
        $view.on('click', '#submitBig', function () {
            febs.navigate("/measure/bigMeasureTableFill");
        });
        $view.on('click', '#submitPe', function () {
            febs.navigate("/measure/outPeMeasureTableFill");
        });
        $view.on('click', '#submitCarry', function () {
            febs.navigate("/measure/outCarryMeasureTableFill");
        });


    });
</script>