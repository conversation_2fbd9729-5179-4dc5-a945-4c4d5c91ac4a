<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>手动排序功能测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #061729;
            color: white;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }

        #canvas {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .tools {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .topBtn {
            background: #3c648b;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .topBtn:hover {
            background: #49b1f5;
        }

        /* 手动排序相关样式 */
        .sort-module {
            position: absolute;
            width: 86px;
            height: 60px;
            background: rgba(52, 175, 210, 0.8);
            border: 2px solid #34afd2;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 11px;
            text-align: center;
            transition: all 0.3s ease;
            z-index: 1000;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .sort-module:hover {
            background: rgba(255, 114, 43, 0.9);
            border-color: #FF722B;
            transform: scale(1.05);
        }

        .sort-module-name {
            font-weight: bold;
            margin-bottom: 4px;
        }

        .sort-module-date {
            font-size: 12px;
            opacity: 0.8;
        }

        .sort-controls {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(6, 23, 41, 0.95);
            padding: 15px 20px;
            border-radius: 8px;
            border: 1px solid #34afd2;
            color: white;
            z-index: 2000;
            display: none;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
        }

        .sort-controls.active {
            display: block;
        }

        .sort-info {
            margin-bottom: 10px;
            font-size: 14px;
        }

        .sort-buttons {
            display: flex;
            gap: 10px;
        }

        .sort-btn {
            padding: 8px 16px;
            background: #3c648b;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s ease;
        }

        .sort-btn:hover {
            background: #49b1f5;
        }

        .sort-btn.primary {
            background: #49b1f5;
        }

        .sort-order-display {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(6, 23, 41, 0.95);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #34afd2;
            color: white;
            max-width: 300px;
            z-index: 2000;
            display: none;
        }

        .sort-order-display.active {
            display: block;
        }

        .sort-order-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #49b1f5;
        }

        .sort-order-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .sort-order-item {
            padding: 4px 0;
            border-bottom: 1px solid rgba(52, 175, 210, 0.3);
            font-size: 12px;
        }

        .sort-order-item:last-child {
            border-bottom: none;
        }

        .module-placeholder {
            position: absolute;
            width: 100px;
            height: 60px;
            background: rgba(255, 255, 255, 0.1);
            border: 2px dashed #34afd2;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #34afd2;
            font-size: 12px;
        }
    </style>
</head>

<body>
    <div id="canvas">
        <!-- 工具栏 -->
        <div class="tools">
            <button class="topBtn" id="manualSort">手动排序</button>
        </div>

        <!-- 手动排序控制面板 -->
        <div id="sortControls" class="sort-controls">
            <div class="sort-info">手动排序模式 - 点击模块进行搭载</div>
            <div class="sort-buttons">
                <button class="sort-btn primary" id="exitSortMode">退出排序</button>
                <button class="sort-btn" id="resetSort">重置排序</button>
            </div>
        </div>

        <!-- 排序顺序显示 -->
        <div id="sortOrderDisplay" class="sort-order-display">
            <div class="sort-order-title">搭载顺序</div>
            <div class="sort-order-list" id="sortOrderList">
                <!-- 动态生成排序列表 -->
            </div>
        </div>
    </div>

    <script>
        // 模拟数据 - 生成50个模块来测试大量模块的分布
        const mockModules = [];
        for (let i = 1; i <= 50; i++) {
            mockModules.push({
                name: `Module_${String.fromCharCode(64 + (i % 26) + 1)}${Math.floor((i - 1) / 26) + 1}`,
                carryDate: `2024-01-${String(15 + (i % 15)).padStart(2, '0')}`
            });
        }

        // 手动排序相关变量
        let isManualSortMode = false;
        let sortModules = [];
        let sortOrder = [];
        let originalPositions = new Map();

        // 初始化
        function init() {
            createModulePlaceholders();
        }

        // 创建模块占位符（模拟原始位置）
        function createModulePlaceholders() {
            const canvas = document.getElementById('canvas');
            const centerX = canvas.offsetWidth / 2;
            const centerY = canvas.offsetHeight / 2;

            mockModules.forEach((module, index) => {
                const placeholder = document.createElement('div');
                placeholder.className = 'module-placeholder';
                placeholder.textContent = module.name;

                // 计算原始位置（中心区域）
                const angle = (index / mockModules.length) * Math.PI * 2;
                const radius = 80;
                const x = centerX + Math.cos(angle) * radius - 50;
                const y = centerY + Math.sin(angle) * radius - 30;

                placeholder.style.left = x + 'px';
                placeholder.style.top = y + 'px';

                // 保存原始位置
                originalPositions.set(module.name, { x, y });

                canvas.appendChild(placeholder);
            });
        }

        // 进入手动排序模式
        function enterManualSortMode() {
            isManualSortMode = true;
            sortOrder = [];
            sortModules = [];

            // 显示控制面板
            document.getElementById('sortControls').classList.add('active');
            document.getElementById('sortOrderDisplay').classList.add('active');

            // 将模块分散到画布四周
            disperseModules();

            // 更新排序显示
            updateSortOrderDisplay();

            console.log('手动排序模式已开启');
        }

        // 分散模块
        function disperseModules() {
            const canvas = document.getElementById('canvas');

            mockModules.forEach((module, index) => {
                // 计算分散位置（严格四周分布，避免重叠）
                const totalModules = mockModules.length;
                const centerX = canvas.offsetWidth / 2;
                const centerY = canvas.offsetHeight / 2;
                const edgeDistance = 120; // 距离中心的距离
                const moduleSpacing = 95; // 模块之间的间距（卡片宽度86 + 间距9）

                // 动态计算每条边的模块数量，确保不重叠
                const maxModulesPerSide = Math.ceil(totalModules / 4);
                const actualModulesPerSide = Math.min(maxModulesPerSide, 15); // 限制每边最多15个

                let sideIndex = Math.floor(index / actualModulesPerSide);
                let positionInSide = index % actualModulesPerSide;

                // 如果模块太多，分配到多层
                if (index >= actualModulesPerSide * 4) {
                    const extraIndex = index - actualModulesPerSide * 4;
                    sideIndex = Math.floor(extraIndex / actualModulesPerSide);
                    positionInSide = extraIndex % actualModulesPerSide;
                }

                let uiX, uiY;
                const layerOffset = Math.floor(index / (actualModulesPerSide * 4)) * 30; // 多层时的偏移

                switch (sideIndex % 4) {
                    case 0: // 顶部
                        uiX = centerX + (positionInSide - (actualModulesPerSide - 1) / 2) * moduleSpacing;
                        uiY = edgeDistance - layerOffset;
                        break;
                    case 1: // 右侧
                        uiX = canvas.offsetWidth - edgeDistance + layerOffset;
                        uiY = centerY + (positionInSide - (actualModulesPerSide - 1) / 2) * (moduleSpacing * 0.7);
                        break;
                    case 2: // 底部
                        uiX = centerX + ((actualModulesPerSide - 1) / 2 - positionInSide) * moduleSpacing;
                        uiY = canvas.offsetHeight - edgeDistance + layerOffset;
                        break;
                    case 3: // 左侧
                    default:
                        uiX = edgeDistance - layerOffset;
                        uiY = centerY + ((actualModulesPerSide - 1) / 2 - positionInSide) * (moduleSpacing * 0.7);
                        break;
                }

                // 确保在画布范围内，但保持在边缘
                uiX = Math.max(10, Math.min(uiX, canvas.offsetWidth - 96));
                uiY = Math.max(10, Math.min(uiY, canvas.offsetHeight - 70));

                // 创建可点击的排序模块UI
                createSortModuleUI(module, uiX, uiY);
            });
        }

        // 创建排序模块UI
        function createSortModuleUI(module, x, y) {
            const canvas = document.getElementById('canvas');
            const moduleDiv = document.createElement('div');
            moduleDiv.className = 'sort-module';
            moduleDiv.dataset.moduleName = module.name;

            moduleDiv.innerHTML = `
                <div class="sort-module-name">${module.name}</div>
                <div class="sort-module-date">${module.carryDate || '未设定'}</div>
            `;

            // 直接使用计算好的边缘位置
            moduleDiv.style.left = x + 'px';
            moduleDiv.style.top = y + 'px';

            // 点击事件
            moduleDiv.addEventListener('click', (e) => {
                e.stopPropagation();
                if (isManualSortMode) {
                    assembleModule(module, moduleDiv);
                }
            });

            canvas.appendChild(moduleDiv);
            sortModules.push(moduleDiv);
        }

        // 搭载模块
        function assembleModule(module, moduleDiv) {
            // 添加到排序数组
            sortOrder.push({
                name: module.name,
                carryDate: module.carryDate,
                timestamp: Date.now()
            });

            // 获取原始位置
            const originalPos = originalPositions.get(module.name);
            if (originalPos) {
                // 动画移动到原始位置
                moduleDiv.style.transition = 'all 1.5s ease';
                moduleDiv.style.left = originalPos.x + 'px';
                moduleDiv.style.top = originalPos.y + 'px';
                moduleDiv.style.background = 'rgba(255, 114, 43, 0.9)';

                // 1.5秒后移除
                setTimeout(() => {
                    moduleDiv.remove();
                    const index = sortModules.indexOf(moduleDiv);
                    if (index > -1) {
                        sortModules.splice(index, 1);
                    }
                }, 1500);
            }

            // 更新排序显示
            updateSortOrderDisplay();

            // 检查是否完成所有搭载
            if (sortOrder.length === mockModules.length) {
                setTimeout(() => {
                    console.log('手动排序完成，搭载顺序:', sortOrder);
                    alert('所有模块搭载完成！正在退出排序模式...\n搭载顺序：' + sortOrder.map(item => item.name).join(' -> '));

                    // 自动退出排序模式
                    setTimeout(() => {
                        exitManualSortMode();
                    }, 1000);
                }, 1500);
            }
        }

        // 更新排序显示
        function updateSortOrderDisplay() {
            const listElement = document.getElementById('sortOrderList');
            listElement.innerHTML = '';

            sortOrder.forEach((item, index) => {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'sort-order-item';
                itemDiv.innerHTML = `${index + 1}. ${item.name} (${item.carryDate || '未设定'})`;
                listElement.appendChild(itemDiv);
            });
        }

        // 退出手动排序模式
        function exitManualSortMode() {
            isManualSortMode = false;

            // 隐藏控制面板
            document.getElementById('sortControls').classList.remove('active');
            document.getElementById('sortOrderDisplay').classList.remove('active');

            // 移除排序模块UI
            sortModules.forEach(module => module.remove());
            sortModules = [];

            // 清空数据
            sortOrder = [];

            console.log('已退出手动排序模式');
        }

        // 重置排序
        function resetSort() {
            if (!isManualSortMode) return;

            // 清空排序记录
            sortOrder = [];

            // 移除现有UI
            sortModules.forEach(module => module.remove());
            sortModules = [];

            // 重新分散模块
            disperseModules();

            // 更新显示
            updateSortOrderDisplay();

            console.log('排序已重置');
        }

        // 事件监听器
        document.getElementById('manualSort').addEventListener('click', () => {
            if (isManualSortMode) {
                exitManualSortMode();
            } else {
                enterManualSortMode();
            }
        });

        document.getElementById('exitSortMode').addEventListener('click', exitManualSortMode);
        document.getElementById('resetSort').addEventListener('click', resetSort);

        // 初始化
        init();
    </script>
</body>

</html>