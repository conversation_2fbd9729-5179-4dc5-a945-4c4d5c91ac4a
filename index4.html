<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns="http://www.w3.org/1999/html">

<head>
    <th:block th:include="include::header('生设图纸清单')" />
    <style type="text/css">
        .layui-table-view .layui-table[lay-size=sm] td[data-field=strId] .layui-table-cell,
        .layui-table-view .layui-table[lay-size=sm] td[data-field=checkUserId] .layui-table-cell,
        .layui-table-view .layui-table[lay-size=sm] td[data-field=verifyUserId] .layui-table-cell {
            overflow: visible;
            padding-top: 0px;
        }

        #febs-designDrawManagerList {
            height: calc(100% - 15px);
        }

        #febs-designDrawManagerList .yfs-list {
            background-color: #f7f7f7;
            height: 100%;
        }

        #febs-designDrawManagerList .ztree li span.button.add {
            margin-left: 2px;
            margin-right: -1px;
            background-position: -144px 0;
            vertical-align: top;
        }

        #febs-designDrawManagerList .ztree li a {
            height: 25px;
        }

        #febs-designDrawManagerList .ztree * {
            padding: 0;
            margin: 0;
            font-size: 16px;
            font-family: Verdana, Arial, Helvetica, AppleGothic, sans-serif;
        }

        #febs-designDrawManagerList .button .ico_open {
            width: 20px;
            height: 20px;
        }

        #febs-designDrawManagerList .tree-selected {
            color: #2F9688;
            font-weight: bold;
        }

        .layui-table tbody tr:hover,
        .layui-table-hover {
            background-color: #F8F8F8;
        }

        .layui-table-checked.layui-table-hover {
            background-color: #74b9ff !important;
        }

        #febs-designDrawManagerList .right-style {
            z-index: 999;
            display: none;
            position: fixed;
            padding-left: 15px;
            padding-right: 15px;
            padding-bottom: 10px;
            background-color: #FBFBFB;
            border: 2px solid #F0F0F0;
            cursor: pointer;
        }

        #febs-designDrawManagerList .edited {
            background-color: #74b9ff !important;
        }

        .por .layui-icon:after {
            content: '\e697';
            color: #16b777;
        }

        #febs-designDrawManagerList .layui-form-item .layui-inline {
            margin-right: 0px !important;
        }

        #febs-designDrawManagerList .jhg-body-search .layui-form-item .layui-form-label {
            padding: 5px 5px;
            width: 72px;
        }

        .username {
            background-color: rgb(93, 177, 255);
            position: relative;
            border-radius: 3px;
            color: #FFF;
            text-align: center;
            padding-left: 10px;
            padding-right: 10px;
            margin-right: 2px;
            margin-left: 2px;
            display: inline-table;
        }

        .layui-badge-rim {
            height: 21px;
            line-height: 19px;
        }

        #mainDrawTable xm-select>.xm-label .xm-label-block>span {
            color: #333333;
        }

        /* 为隔行变色定义CSS */
        .layui-table tbody tr:nth-child(odd) {
            background-color: #ffffff;
            /* 奇数行背景色 */
        }

        .layui-table tbody tr:nth-child(even) {
            background-color: #f2f2f2;
            /* 偶数行背景色 */
        }

        table xm-select {
            min-height: 29px !important;
            border: none;
            background: transparent;
        }

        #headerForm .layui-form-label {
            width: 72px !important;
        }

        #headerForm .layui-btn {
            margin: 0 1px;
        }

        #headerForm .layui-input-inline {
            width: 180px;
        }

        #headerForm .layui-inline {
            margin: 3px 0px;
        }

        .shipNoText {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 150px;
            position: absolute;
            right: 10px;
        }

        #shipNoSelect xm-select>.xm-body {
            width: 350px;
        }

        .cell-cxqy {
            text-align: left;
            padding-left: 10px;
            padding-top: 4px;
        }

        /* 高级检索按钮样式 */
        #advancedSearchBtn {
            padding: 0 8px !important;
            font-size: 12px !important;
            margin-left: 5px !important;
        }
    </style>
</head>

<body>
    <div class="layui-fluid layui-anim febs-anim page-body" id="febs-designDrawManagerList" lay-title="生设图纸清单">
        <form class="layui-form search-form" id="headerForm">
            <div class="jhg-body-search">
                <!-- 第一行：基本检索条件 -->
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm">船号:</label>
                        <div class="layui-input-inline">
                            <div id="shipNoSelect"></div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm">设计专业:</label>
                        <div class="layui-input-inline">
                            <div id="majorSelect"></div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm">船型区域:</label>
                        <div class="layui-input-inline">
                            <div id="regions"></div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm">图纸类型:</label>
                        <div class="layui-input-inline">
                            <div id="typeSelect"></div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class=" layui-form-label layui-form-label-sm">编号/描述:</label>
                        <div class="layui-input-inline">
                            <input type="text" id="ddNo" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline" style="margin-left: 10px;"
                        shiro:hasPermission="designDrawManagerView:view">
                        <div id="query" class="layui-btn searchBlue layui-btn-sm">
                            <em class="layui-icon">&#xe615;</em> 检索
                        </div>
                        <div id="advancedSearchBtn" class="layui-btn layui-btn-sm layui-btn-normal">
                            <em class="layui-icon">&#xe671;</em> 高级检索
                        </div>
                    </div>
                </div>

                <!-- 第二行：高级检索条件，默认隐藏 -->
                <div class="layui-form-item" id="advancedSearchRow" style="display: none;">
                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm">设计作业:</label>
                        <div class="layui-input-inline">
                            <div id="processSelect"></div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm">设计阶段:</label>
                        <div class="layui-input-inline">
                            <div id="stageSelect"></div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm">作业人员:</label>
                        <div class="layui-input-inline">
                            <div id="ssWorkUserSelect"></div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm">有效状态:</label>
                        <div class="layui-input-inline">
                            <select id="drawNeedFlg" lay-filter="drawNeedFlg">
                                <option value="">所有</option>
                                <option value="0">是</option>
                                <option value="1">否</option>
                            </select>
                        </div>
                    </div>
                </div>
                <br>

                <!--                <div class="layui-inline">-->
                <!--                    <div id="addGoods" class="layui-btn jhg-normal-btn layui-btn-sm">-->
                <!--                        <em class="layui-icon">&#xe7a8;</em> 新增物资-->
                <!--                    </div>-->
                <!--                </div>-->

                <div class="layui-inline" style="margin-left: 12px;" shiro:hasPermission="designDrawManagerView:edit">
                    <div id="add" class="layui-btn layui-btn-sm blueBtm" lay-event="add">
                        <i class="layui-icon layui-icon-add-1"></i> 新增
                    </div>
                </div>
                <div class="layui-inline" shiro:hasPermission="designDrawManagerView:edit">
                    <div id="import" class="layui-btn blueBtm layui-btn-sm">
                        <em class="layui-icon">&#xe7aa;</em> 导入
                    </div>
                </div>
                <div class="layui-inline" shiro:hasPermission="designDrawManagerView:view">
                    <div id="expData" class="layui-btn layui-btn-sm blueBtm">
                        <i class="layui-icon layui-icon-export"></i> 导出
                    </div>
                </div>
                <div class="layui-inline" shiro:hasPermission="designDrawManagerView:edit">
                    <div id="copy" class="layui-btn blueBtm layui-btn-sm">
                        <em class="layui-icon">&#xe7ef;</em> 图纸复制
                    </div>
                </div>
                <div class="layui-inline" shiro:hasPermission="designDrawManagerView:edit">
                    <div id="userHour" class="layui-btn blueBtm layui-btn-sm">
                        <i class="layui-icon layui-icon-edit"></i> 人员编辑
                    </div>
                </div>
                <div class="layui-inline" shiro:hasPermission="designDrawManagerView:edit">
                    <div id="deleteBatch" class="layui-btn layui-btn-sm layui-bg-red">
                        <i class="layui-icon layui-icon-delete"></i> 删除
                    </div>
                </div>
            </div>
        </form>

        <div class="layui-row" style="display: flex;flex-direction: column; height:calc(100% - 125px) ">
            <div class="layui-form" style="display: flex;height: 100%">
                <div id='mymenu' class="right-style"></div>

                <!--            <div class="layui-form" style="display: flex; height: 100%; width: 13%; align-content: flex-start;flex-wrap: wrap;">-->
                <!--                <div style="width: 100%; height: 96%; background-color: white;margin: 3px;overflow: hidden;">-->
                <!--                    <div class="layui-card-header popTitle">-->
                <!--                        <label></label>-->
                <!--                        <span>总段/分段/区域</span>-->
                <!--                    </div>-->
                <!--                    <div style="overflow : auto; height: 87%">-->
                <!--                        <div id="treeDraw"></div>-->
                <!--                    </div>-->
                <!--                </div>-->
                <!--            </div>-->
                <div id="mainDrawTable" class="layui-form" style="width:100%;background-color: white;overflow: auto; ">
                    <div class="jhg-body-table">
                        <table class="layui-hide" id="drawList">
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
    <script type="text/html" id="designDrawDescColor">
    <ul>
        <li>{{d.designDrawDesc}}</li>
    </ul>
</script>
    <th:block th:include="include::foot" />


    <script data-th-inline="none" type="text/javascript">
        layui.config({
            base: ctx + 'febs/'
        }).extend({
            febs: 'lay/modules/febs',
            validate: 'lay/modules/validate',
            formSelects: 'lay/extends/formSelects-v4.min',
            jqueryui: 'lay/extends/jquery-ui.min',
            echarts: 'lay/extends/echarts.min',
            commonJS: 'lay/extends/common'
        }).use(['tree', 'jquery', 'validate', 'table', 'laypage', 'form', 'febs', 'commonJS', 'dropdown', 'rpcJs'], function () {
            var $ = layui.jquery,
                $view = $('#febs-designDrawManagerList'),
                febs = layui.febs,
                form = layui.form,
                table = layui.table,
                tree = layui.tree,
                commonJS = layui.commonJS,
                dropdown = layui.dropdown,
                rpcJs = layui.rpcJs,
                treeData,
                shipNoSelect,
                allShips = [],
                shipId,
                tableIns,
                regions,
                shipTypeId,
                block,
                pwdMap = new Map(),
                pdpdMap = new Map(),
                pptdMap = new Map(),
                deptMap = new Map(),
                pdtdMap = new Map(),
                regionsMap = new Map(),
                regionsArr,
                processSelect,
                stageSelect,
                majorSelect,
                ssWorkUserSelect,
                checkArr = [],
                verifyArr = [],
                typeSelect;
            form.render();
            let editFlg = currentUser.permissionSet.indexOf('designDrawManagerView:edit') == -1 ? false : true
            autoHeight();
            $(window).resize(function () {
                // 窗口大小改变时也调整表格高度
                adjustTableHeight();
            })
            getCheckUserList();
            getVerifyUserList();
            initDict();
            setSsWorkUser(null)
            setShipNo();
            setRegions([]);
            initTable();

            // 动态调整表格高度
            function adjustTableHeight() {
                setTimeout(function () {
                    var $headerForm = $('#headerForm');
                    var $tableContainer = $('.layui-row').first();
                    var $mainDrawTable = $('#mainDrawTable');
                    var headerHeight = $headerForm.outerHeight();

                    // 根据搜索表单的实际高度动态调整表格容器高度
                    $tableContainer.css('height', 'calc(100% - ' + headerHeight + 'px)');

                    // 同时调整表格数据展示部分的高度
                    setTimeout(function () {
                        var mainDrawTableHeight = $mainDrawTable.height();
                        var $tableBody = $mainDrawTable.find(".layui-table-body.layui-table-main");
                        if ($tableBody.length > 0) {
                            // 减去表格头部高度，通常是38px左右
                            var tableHeaderHeight = $mainDrawTable.find(".layui-table-header").outerHeight() || 38;
                            var newTableBodyHeight = mainDrawTableHeight - tableHeaderHeight;
                            $tableBody.css("height", newTableBodyHeight + "px");

                            // 触发layui表格的resize事件，确保表格正确重新渲染
                            if (typeof table !== 'undefined' && table.resize) {
                                table.resize('drawList');
                            }
                        }
                    }, 50); // 稍微延迟确保容器高度已经更新
                }, 350); // 等待动画完成后调整
            }

            // 初始化时调整一次高度
            adjustTableHeight();

            // 高级检索按钮事件处理
            $('#advancedSearchBtn').on('click', function () {
                var $advancedRow = $('#advancedSearchRow');
                var $btn = $(this);
                var $icon = $btn.find('.layui-icon');

                if ($advancedRow.is(':visible')) {
                    // 当前显示，点击后隐藏
                    $advancedRow.slideUp(300, function () {
                        // 隐藏后调整表格高度（增加高度）
                        adjustTableHeight();
                    });
                    $btn.html('<em class="layui-icon">&#xe671;</em> 高级检索');
                    $btn.removeClass('layui-btn-danger').addClass('layui-btn-normal');
                } else {
                    // 当前隐藏，点击后显示
                    $advancedRow.slideDown(300, function () {
                        // 显示后调整表格高度（减少高度）
                        adjustTableHeight();
                    });
                    $btn.html('<em class="layui-icon">&#xe672;</em> 隐藏检索');
                    $btn.removeClass('layui-btn-normal').addClass('layui-btn-danger');
                }
            });

            function autoHeight() {
                //选项卡tab高度
                var tabHeight = $(".febs-tabs-wrap").height() == null ? "0" : $(".febs-tabs-wrap").height();
                console.log(tabHeight)
                //标题头的高度
                var appHeight = $("#app-header").height() == null ? "0" : $("#app-header").height();
                var diff = tabHeight == 0 ? 16 : 52;
                var tempheight = $(window).height() - tabHeight - appHeight - diff;
                $view.find('.yfs-body').height(tempheight);
            }
            function getCheckUserList() {
                let param = {
                }
                febs.postArraySync(ctx + 'plan/produceProfessionTypeDict/detailCheck', param, function (e) {
                    if (e.code == 200) {
                        $.each(e.data, function (i, v) {
                            checkArr.push({
                                name: v.truename,
                                value: v.checkUserId,
                                pptdId: v.professionId
                            })
                        })
                    }
                })
            }
            function getVerifyUserList() {
                let param = {
                }
                febs.postArraySync(ctx + 'plan/produceProfessionTypeDict/detailVerify', param, function (e) {
                    if (e.code == 200) {

                        $.each(e.data, function (i, v) {
                            verifyArr.push({
                                name: v.truename,
                                value: v.verifyUserId,
                                pptdId: v.professionId
                            })
                        })
                    }
                })
            }
            function getRegions(shipTypeId) {
                let arr = []
                febs.getSyn(ctx + 'basic/shipTypeArea/all', { shipTypeId: shipTypeId }, function (e) {
                    if (e.code == 200) {
                        $.each(e.data, function (i, v) {
                            regionsMap.set(v.strId, v.strCodeName);
                            arr.push({
                                name: v.strCodeName,
                                value: v.strId,
                                showname: v.strCode
                            })
                        })
                    }
                })
                return arr
            }
            //设置生设作业人员
            function setSsWorkUser(pptdId) {
                let arr = []
                febs.postArraySync(ctx + 'plan/produceProfessionTypeDict/detailWork', { pptdId: pptdId }, function (data) {
                    if (data.code == 200) {
                        $.each(data.data, function (i, v) {
                            arr.push({
                                name: v.truename,
                                value: v.userId,
                            })
                        })
                    }
                })
                ssWorkUserSelect = xmSelect.render({
                    el: '#ssWorkUserSelect',
                    data: arr,
                    filterable: true,
                    tips: "请选择",
                    radio: true,
                    clickClose: true,
                })
            }
            //船号选择
            function setShipNo() {
                let arr = [];
                let resp = rpcJs.getShipDataList();
                if (resp.code == 200) {
                    allShips = resp.data;
                    $.each(resp.data, function (i, v) {
                        arr.push({
                            name: v.shipNo,
                            value: v.shipId,
                            showname: v.showName,
                            typeId: v.typeId
                        })
                    })
                }
                shipNoSelect = xmSelect.render({
                    el: '#shipNoSelect',
                    data: arr,
                    filterable: true,
                    template({ item }) {
                        return item.name + '<span class="shipNoText" title="' + item.showname + '">' + item.showname + '</span>'
                    },
                    radio: true,
                    clickClose: true,
                    model: {
                        label: {
                            block: {
                                template(item, sels) {
                                    return item.name + '(' + item.showname + ')'
                                }
                            }
                        }
                    },
                    // on:function (data){
                    //     febs.get(ctx + 'plan/designDrawInfo/getBlockDrawTreeList', {shipId: data.change[0].value}, function (data) {
                    //         if (data.code == 200) {
                    //             treeData = data.data;
                    //             // 树组件渲染
                    //             tree.render({
                    //                 elem: "#treeDraw",
                    //                 id: 'demoId',
                    //                 data: treeData,
                    //                 onlyIconControl: true,
                    //                 click: function (obj) {
                    //                     $view.find('#treeDraw').find('.tree-selected').removeClass('tree-selected')
                    //                     $view.find('#treeDrawArea').find('.tree-selected').removeClass('tree-selected')
                    //                     $(obj.elem).children('.layui-tree-entry').find('.layui-tree-txt').addClass('tree-selected')
                    //                     combinationClick(obj)
                    //                 },
                    //             });
                    //         }
                    //     })
                    // }
                    on: function (data) {
                        if (data.arr[0] != undefined) {
                            shipTypeId = data.arr[0].typeId
                            let arr = getRegions(shipTypeId)
                            regionsArr = arr;
                            setRegions(arr);
                        }
                    }
                })
            }
            //设置船型区域
            function setRegions(arr) {
                regions = xmSelect.render({
                    el: '#regions',
                    data: arr,
                    filterable: true,
                    tips: arr.length === 0 ? "请先选择船号" : "请选择",
                    template({ item }) {
                        return item.name + '<span class="shipNoText" title="' + item.showname + '">' + item.showname + '</span>'
                    },
                    radio: true,
                    clickClose: true,
                })
            }

            //查询
            $('#query').on('click', function () {
                if (shipNoSelect.getValue('value').length === 0) {
                    febs.alert.warn('请选择船号')
                    return false;
                } else {
                    shipId = shipNoSelect.getValue('valueStr');
                    let ddNo = $view.find('#ddNo').val();
                    let processSelected = processSelect.getValue('valueStr');
                    let stageSelected = stageSelect.getValue('valueStr');
                    let majorSelected = majorSelect.getValue('valueStr');
                    let typeSelected = typeSelect.getValue('valueStr');
                    let strSelected = regions.getValue('valueStr');
                    let drawNeedFlg = $view.find('#drawNeedFlg').val();
                    let proChildren = majorSelect.getValue('children').length == 0 ? [] : majorSelect.getValue('children')[0].children;
                    //0 父级别 1 子级别
                    let proParentType = '';
                    if (proChildren == undefined) {
                        proParentType = '1';
                    } else {
                        proParentType = '0';
                    }

                    let param = {
                        designDrawNo: ddNo,
                        shipId: shipNoSelect.getValue('valueStr'),
                        pdwId: processSelected,
                        pptdId: majorSelected,
                        pdpdId: stageSelected,
                        pdtdId: typeSelected,
                        strId: strSelected,
                        workUserIdList: ssWorkUserSelect.getValue('value'),
                        drawNeedFlg: drawNeedFlg,
                        proParentType: proParentType,
                        type: "asc"
                    }
                    tableIns.reload({
                        where: param,
                        url: ctx + 'plan/designDrawInfo/page',
                        page: { curr: 1 }
                    });
                    // param.pageNum = 1 ;
                    // param.pageSize = 30;
                    // febs.get(ctx + 'plan/designDrawInfo/page', param, function (e) {
                    //     if (e.code == 200){
                    //         let total = e.data.total;
                    //         let totalPages = Math.ceil(total/param.pageSize);
                    //         createMainTable(e.data.rows)
                    //         let url = 'plan/designDrawInfo/page';
                    //         let tableId = 'drawList';
                    //         rpcJs.flowTable(url,param,tableId,totalPages,8)
                    //     }
                    // })
                }
            });

            window.queryOther = function () {
                const oldP = $(".layui-laypage-skip").find("input").val();
                table.reloadData(tableIns.config.id, {
                    scrollPos: 'fixed'
                })
                // 创建一个 MutationObserver 实例 (用来监听页面DOM元素)
                const observer = new MutationObserver(function (mutationsList, observer) {
                    for (let mutation of mutationsList) {
                        if (mutation.type === 'childList') {
                            const newP = $(".layui-table-main").find("div[class='layui-none']").text();
                            if (newP !== undefined && newP.trim() === '无数据') {
                                table.reload(tableIns.config.id, {
                                    page: {
                                        curr: (oldP > 1 ? oldP - 1 : 1)
                                    }
                                })
                                // 停止观察
                                observer.disconnect();
                            }
                        }
                    }
                });
                // 配置观察选项
                const config = { childList: true, subtree: true };
                // 开始观察目标节点
                observer.observe(document.querySelector('.layui-table-main'), config);
            }

            //初始化字典数据
            function initDict() {
                febs.get(ctx + 'plan/produceDesignWorkDict/all', {}, function (e) {
                    if (e.code == 200) {
                        let arr = []
                        $.each(e.data, function (i, v) {
                            pwdMap.set(v.pdwId, v.pdwCodeName)
                            arr.push({
                                name: v.pdwCodeName,
                                value: v.pdwId,
                                code: v.pdwCode
                            })
                        })
                        processSelect = xmSelect.render({
                            el: '#processSelect',
                            data: arr,
                            radio: true,
                            filterable: true,
                            template({ item }) {
                                return item.name + '<span style="position: absolute;right: 10px;color: #90a4ae">' + item.code + '</span>'
                            },
                            clickClose: true,
                        })
                    }
                })
                febs.get(ctx + 'plan/produceDesignProgressDict/all', {}, function (e) {
                    if (e.code == 200) {
                        let arr = []
                        $.each(e.data, function (i, v) {
                            pdpdMap.set(v.pdpdId, v.pdpdCodeName)
                            arr.push({
                                name: v.pdpdCodeName,
                                value: v.pdpdId,
                                code: v.pdpdCode
                            })
                        })
                        stageSelect = xmSelect.render({
                            el: '#stageSelect',
                            data: arr,
                            radio: true,
                            filterable: true,
                            template({ item }) {
                                return item.name + '<span style="position: absolute;right: 10px;color: #90a4ae">' + item.code + '</span>'
                            },
                            clickClose: true,
                        })
                    }
                })
                febs.get(ctx + 'plan/produceProfessionTypeDict/all', {}, function (e) {
                    if (e.code == 200) {
                        $.each(e.data, function (i, v) {
                            pptdMap.set(v.pptdId, v.pptdCodeName)
                            deptMap.set(v.pptdId, v.deptId)
                        })
                    }
                })
                febs.get(ctx + 'plan/produceProfessionTypeDict/tree', {}, function (e) {
                    if (e.code == 200) {
                        majorSelect = xmSelect.render({
                            el: '#majorSelect',
                            data: e.data,
                            radio: true,
                            filterable: true,
                            clickClose: true,
                            tree: {
                                show: true,
                                strict: false,
                            },
                            on: function (data) {
                                if (data.isAdd) {
                                    setSsWorkUser(data.arr[0].value)
                                } else {
                                    setSsWorkUser(null)
                                }
                            }
                        })
                    }
                })

                febs.get(ctx + 'plan/produceDrawTypeDict/getAll', {}, function (e) {
                    if (e.code == 200) {
                        let arr = []
                        $.each(e.data, function (i, v) {
                            pdtdMap.set(v.pdtId, v.pdtCodeName)
                            arr.push({
                                name: v.pdtCodeName,
                                value: v.pdtId,
                                code: v.pdtCode
                            })
                        })
                        typeSelect = xmSelect.render({
                            el: '#typeSelect',
                            data: arr,
                            radio: true,
                            filterable: true,
                            template({ item }) {
                                return item.name + '<span style="position: absolute;right: 10px;color: #90a4ae">' + item.code + '</span>'
                            },
                            clickClose: true,
                        })
                    }
                })
            }
            function initTable() {
                let arr = []
                createMainTable(arr)
            }
            //生成表格数据
            function createMainTable(data) {
                tableIns = febs.table.init({
                    elem: $view.find('#drawList'),
                    id: 'drawList',
                    data: data,
                    even: true,
                    // autoSort: true,
                    // sort: true,
                    height: '#mainDrawTable-1',
                    limit: 1000,
                    page: true,
                    cols: [
                        [
                            { type: 'checkbox', fixed: 'left' },
                            {
                                field: 'designDrawNo', title: '设计图纸编号', sort: true, align: 'center', fixed: 'left', minWidth: 125, templet(d) {
                                    return '<div class="cell-left">' + d.designDrawNo + '</div>'
                                }
                            },
                            {
                                field: 'designDrawDesc', title: '<span style="color: #1b83f0;font-weight: bold">设计图纸描述</span>', align: 'center', fixed: 'left', minWidth: 200, edit: function (d) {
                                    if (d.drawUseStatus == 0 && editFlg) {
                                        return "text"
                                    }
                                }, templet(d) {
                                    return '<div class="cell-left">' + d.designDrawDesc + '</div>'
                                }
                            },
                            {
                                field: 'designDrawDescEn', title: '<span style="color: #1b83f0;font-weight: bold">设计图纸英文描述</span>', align: 'center', fixed: 'left', minWidth: 200, edit: function (d) {
                                    if (d.drawUseStatus == 0 && editFlg) {
                                        return "text"
                                    }
                                }, templet(d) {
                                    return '<div class="cell-left">' + (d.designDrawDescEn != null ? d.designDrawDescEn : '') + '</div>'
                                }
                            },
                            {
                                field: 'produceDate', title: '生产需求日', sort: true, align: 'center', fixed: 'left', minWidth: 120, templet(d) {
                                    if (d.produceDate == null) {
                                        return ''
                                    } else {
                                        return commonJS.formatDate(new Date(d.produceDate), 'yyyy-MM-dd')
                                    }
                                }
                            },
                            {
                                field: 'strId', title: '<span style="color: #1b83f0;font-weight: bold">船型区域</span>', align: 'center', minWidth: 200, templet: function (d) {
                                    return '<div id="CXQY-' + d.designDrawId + '" style="height: 100%" lay-event="cxqySelect">' +
                                        '<div class="cell-cxqy">' + regionsMap.get(d.strId) + '</div></div>'
                                }
                            },
                            {
                                field: 'drawWorkUserInfoList', title: '作业人员', align: 'center', minWidth: 150, templet(d) {
                                    if (d.drawWorkUserInfoList != null) {
                                        /*                                    let html = ''
                                                                            d.drawWorkUserInfoList.forEach(userInfo => {
                                                                                html += '<span class="username layui-badge-rim"> '+userInfo.truename+' </span>'
                                                                            })
                                                                            return html*/
                                        let truenameList = []
                                        d.drawWorkUserInfoList.forEach(userInfo => {
                                            truenameList.push(userInfo.truename);
                                        })
                                        return truenameList.join(',');
                                    } else {
                                        return ''
                                    }
                                }
                            },
                            {
                                field: 'checkUserName', title: '校验人员', align: 'center', minWidth: 80, templet: function (d) {
                                    if (d.checkUserName != null && d.checkUserName != undefined) {
                                        return d.checkUserName
                                        // return '<span class="username"> '+d.checkUserName+' </span>'
                                    }
                                    if (d.checkUserId != null && (d.checkUserName == null || d.checkUserName == undefined)) {
                                        return '人员已删除'
                                        // return '<span class="username">人员已删除</span>'
                                    }
                                    if (d.checkUserId == null) {
                                        return '';
                                    }
                                }
                            },
                            {
                                field: 'verifyUserName', title: '审核人员', align: 'center', minWidth: 80, templet: function (d) {
                                    if (d.verifyUserName != null && d.verifyUserName != undefined) {
                                        return d.verifyUserName
                                        // return '<span class="username"> '+d.verifyUserName+' </span>'
                                    }
                                    if (d.verifyUserId != null && (d.verifyUserName == null || d.verifyUserName == undefined)) {
                                        return '人员已删除'
                                        // return '<span class="username">人员已删除</span>'
                                    }
                                    if (d.verifyUserId == null) {
                                        return '';
                                    }
                                }
                            },
                            {
                                field: 'totalHour', title: '总工时', align: 'center', width: 60, style: 'color:#1b83f0;cursor:pointer;', templet(d) {
                                    if (d.totalHour !== null) {
                                        return '<span lay-event="detailUser" style="text-decoration: underline;">' + (d.totalHour.toFixed(2)) + '</span>';
                                    } else {
                                        return '<span lay-event="detailUser" style="text-decoration: underline;">0</span>';
                                    }
                                }
                            },
                            {
                                field: 'pwdId', title: '生产设计类别', align: 'center', minWidth: 120, templet(d) {
                                    return pwdMap.get(d.pdwId) == null ? '类别已删除' : pwdMap.get(d.pdwId)
                                }
                            },
                            {
                                field: 'pdpdId', title: '生产设计阶段', align: 'center', minWidth: 120, templet(d) {
                                    return pdpdMap.get(d.pdpdId) == null ? '阶段已删除' : pdpdMap.get(d.pdpdId)
                                }
                            },
                            {
                                field: 'pptdId', title: '生产设计专业', align: 'center', minWidth: 120, templet(d) {
                                    return pptdMap.get(d.pptdId) == null ? '专业已删除' : pptdMap.get(d.pptdId)
                                }
                            },
                            {
                                field: 'pdtdId', title: '生产图纸类型', align: 'center', minWidth: 120, templet(d) {
                                    return pdtdMap.get(d.pdtdId) == null ? '类型已删除' : pdtdMap.get(d.pdtdId)
                                }
                            },
                            {
                                field: 'remark', title: '<span style="color: #1b83f0;font-weight: bold">备注</span>', align: 'center', minWidth: 120, edit: function () {
                                    if (editFlg) {
                                        return 'text'
                                    }
                                }, templet(d) {
                                    return '<div class="cell-left">' + (d.remark == null ? '' : d.remark) + '</div>'
                                }
                            },
                            {
                                fixed: 'right', field: 'trayInitFlg', title: '托盘初始化状态', align: 'center', minWidth: 80, templet: function (d) {
                                    if (d.trayInitFlg == 1) {
                                        // return '<input type="checkbox" name="drawUseSwitch" title="是|否" disabled = "disabled" \n' +
                                        //     '  lay-skin="switch"  checked>'
                                        return `<span class="layui-badge" style="background-color: #4dbeba">是</span>`
                                    } else {
                                        // return '<input type="checkbox" name="drawUseSwitch" title="是|否" disabled = "disabled" \n' +
                                        //     '  lay-skin="switch"  >'
                                        return `<span class="layui-badge" style="background-color: #e7d9cc">否</span>`
                                    }
                                }
                            },
                            {
                                fixed: 'right', field: 'drawNeedFlg', title: '<span style="color: #1b83f0;font-weight: bold">有效状态</span>', align: 'center', minWidth: 80, templet: function (d) {
                                    if (d.drawUseStatus == 1 || !editFlg) {
                                        if (d.drawNeedFlg == 0) {
                                            return '<input type="checkbox" name="drawNeedSwitch" title="是|否" disabled = "disabled" \n' +
                                                '  lay-skin="switch" lay-filter="use-templet-status" checked>'
                                        } else {
                                            return '<input type="checkbox" name="drawNeedSwitch" title="是|否" disabled = "disabled" \n' +
                                                '  lay-skin="switch" lay-filter="use-templet-status"  >'
                                        }
                                    } else {
                                        if (d.drawNeedFlg == 0) {
                                            return '<input type="checkbox" name="drawNeedSwitch" title="是|否" \n' +
                                                '  lay-skin="switch" lay-filter="use-templet-status" checked>'
                                        } else {
                                            return '<input type="checkbox" name="drawNeedSwitch" title="是|否" \n' +
                                                '  lay-skin="switch" lay-filter="use-templet-status"  >'
                                        }
                                    }
                                }
                            },
                            // {fixed: 'right', title:'操作', width: 200, minWidth: 125,align:'center', templet: function (d) {
                            //         if(editFlg){
                            //             return '    <div class="layui-clear-space">\n' +
                            //                 '        <a class="layui-btn layui-btn-xs layui-bg-blue" lay-event="userEdit">人员编辑</a>\n' +
                            //                 // '        <a class="layui-btn layui-btn-xs layui-bg-blue" lay-event="save">保存</a>\n' +
                            //                 // '        <a class="layui-btn layui-btn-xs layui-bg-red" lay-event="del">删除</a>\n' +
                            //                 '    </div>'
                            //         }else {
                            //             return ''
                            //         }
                            //
                            //     }
                            // }
                        ]
                    ],
                })
            }

            // 监听排序
            table.on('sort(drawList)', function (d) {
                let field = d.field;
                let type = d.type;

                if (shipNoSelect.getValue('value').length === 0) {
                    febs.alert.warn('请选择船号')
                    return false;
                } else {
                    shipId = shipNoSelect.getValue('valueStr');
                    let ddNo = $view.find('#ddNo').val();
                    let processSelected = processSelect.getValue('valueStr');
                    let stageSelected = stageSelect.getValue('valueStr');
                    let majorSelected = majorSelect.getValue('valueStr');
                    let typeSelected = typeSelect.getValue('valueStr');
                    let strSelected = regions.getValue('valueStr');
                    let drawNeedFlg = $view.find('#drawNeedFlg').val();
                    let proChildren = majorSelect.getValue('children').length == 0 ? [] : majorSelect.getValue('children')[0].children;
                    //0 父级别 1 子级别
                    let proParentType = '';
                    if (proChildren == undefined) {
                        proParentType = '1';
                    } else {
                        proParentType = '0';
                    }

                    let param = {
                        designDrawNo: ddNo,
                        shipId: shipNoSelect.getValue('valueStr'),
                        pdwId: processSelected,
                        pptdId: majorSelected,
                        pdpdId: stageSelected,
                        pdtdId: typeSelected,
                        workUserIdList: ssWorkUserSelect.getValue('value'),
                        strId: strSelected,
                        drawNeedFlg: drawNeedFlg,
                        proParentType: proParentType,
                        type: type,
                        field: field
                    }
                    tableIns.reload({
                        where: param,
                        url: ctx + 'plan/designDrawInfo/page',
                        page: { curr: 1 }
                    });
                    // param.pageNum = 1 ;
                    // param.pageSize = 30;
                    // febs.get(ctx + 'plan/designDrawInfo/page', param, function (e) {
                    //     if (e.code == 200){
                    //         let total = e.data.total;
                    //         let totalPages = Math.ceil(total/param.pageSize);
                    //         createMainTable(e.data.rows)
                    //         let url = 'plan/designDrawInfo/page';
                    //         let tableId = 'drawList';
                    //         rpcJs.flowTable(url,param,tableId,totalPages,8)
                    //     }
                    // })
                }

                // var data = table.cache['drawList'];
                // console.log(data)
                // data.sort(function (a,b) {
                //     for (var i = 0; i < 2; i++){
                //         var vlaueA = pdwCodeMap.get(a.pdwId).charCodeAt(i);
                //         var vlaueB = pdwCodeMap.get(b.pdwId).charCodeAt(i);
                //         if (vlaueA != vlaueB || i == 1){
                //             return d.type == 'asc' ? vlaueA - vlaueB : vlaueB - vlaueA;
                //         }
                //     }
                // })
                // table.renderData('drawList');
            })

            //库存使用状态切换
            form.on('switch(use-templet-status)', function (obj) {
                var index = $(obj.elem).closest('tr').data('index');
                if (obj.elem.checked) {
                    table.cache['drawList'][index].drawNeedFlg = 0
                } else {
                    table.cache['drawList'][index].drawNeedFlg = 1
                }
                // $(obj.elem).closest('tr').addClass('edited')
                // let index2 = $(obj.elem).closest('tr').data('index');
                // table.setRowChecked('drawList',{
                //     index: index2,
                //     checked:true
                // })
                let drawInfo = {
                    designDrawId: table.cache['drawList'][index].designDrawId,
                    drawNeedFlg: table.cache['drawList'][index].drawNeedFlg
                }
                febs.postArray(ctx + 'plan/designDrawInfo/update', drawInfo, function (data) {
                    // if (data.code != 200){
                    //     febs.alert.error('修改失败');
                    // }
                    layer.closeAll();
                    febs.alert.success('有效状态修改成功');
                    // $(obj.tr[0]).removeClass('edited')
                });
            });
            //跳转Draw新增页面
            $view.on('click', '#addDraw', function () {
                console.log(shipTypeId);
                let blockInfo = {
                    shipId: $view.find('#addDraw').data('shipid'),
                    shipTypeId: shipTypeId,
                    dataId: $view.find('#addDraw').data('blockid'),
                    dataName: $view.find('#addDraw').data('blockno'),
                    dataType: $view.find('#addDraw').data('type')
                }
                febs.modal.open('设计图纸新增', 'plan/designDrawManager/add', {
                    area: ['900px', '500px'],
                    btn: ['确定'],
                    data: { blockInfo: blockInfo },
                    yes: function () {
                        $('#designDrawEdit').find('#submit').trigger('click');
                        tableIns.reload()
                    }
                });
            })
            // mainDrawTable触发单元格工具事件
            table.on('tool(drawList)', function (obj) { // 双击 toolDouble
                if (obj.event === 'del') { //退回
                    deleteDraw(obj)
                } else if (obj.event === 'userEdit') {
                    userEdit(obj);
                } else if (obj.event === 'save') {
                    save(obj);
                }
            });
            //删除船体BOM
            function deleteDraw(obj) {
                if (obj.data.drawUseStatus == 1) {
                    febs.alert.warn('已使用的图纸不能删除');
                    return false;
                }
                if (obj.data.trayInitFlg == 1) {
                    febs.alert.warn('已初始化托盘的图纸不能删除');
                    return false;
                }
                febs.modal.confirm('删除', '是否确定删除选中的图纸信息', function () {
                    febs.get(ctx + 'plan/designDrawInfo/delete/' + obj.data.designDrawId, {}, function (e) {
                        if (e.code == 200) {
                            febs.alert.success('删除成功');
                            table.reload('drawList')
                        }
                    })
                })
            }
            /*        //编辑人员工时
                    function userEdit(obj) {
                        febs.modal.open('图纸作业人员编辑', 'plan/designDrawManager/drawWorkUserInfo', {
                            area: ['500px', '580px'],
                            btn: ['确定'],
                            data: {
                                drawInfo: obj.data,
                                tableIns: tableIns,
                                deptId: deptMap.get(obj.data.pptdId),
                                pptdId: obj.data.pptdId,
                                type : 'single',
                                shipId: shipId
                            },
                            yes: function () {
                                $('#drawWorkUserInfo').find('#submit').trigger('click');
                            }
                        });
                    }*/
            //编辑人员工时
            function userEdit(obj) {
                febs.modal.open('图纸作业人员编辑', 'plan/designDrawManager/chooseSsUser', {
                    area: ['500px', '580px'],
                    btn: ['确定'],
                    data: {
                        drawInfo: obj.data,
                        tableIns: tableIns,
                        deptId: deptMap.get(obj.data.pptdId),
                        pptdId: obj.data.pptdId,
                        type: 'single',
                        shipId: shipId
                    },
                    yes: function () {
                        $('#drawWorkUserInfo').find('#submit').trigger('click');
                    }
                });
            }
            //人员工时批量编辑
            $('#userHour').on('click', function () {
                let pptdIdSet = new Set();
                let pptdId;
                let type;
                let designDrawNoList = [];
                let designDrawIdList = [];
                let checkStatus = table.checkStatus('drawList');
                if (checkStatus.data.length === 0) {
                    febs.alert.warn("请至少选择一条数据")
                    return false;
                }
                $.each(checkStatus.data, function (i, v) {
                    pptdId = v.pptdId;
                    pptdIdSet.add(v.pptdId);
                    designDrawNoList.push(v.designDrawNo);
                    designDrawIdList.push(v.designDrawId);
                })
                if (pptdIdSet.size > 1) {
                    febs.alert.warn("请选择同一个生产设计专业的图纸")
                    return false;
                }
                /*
                            febs.modal.open('图纸作业人员编辑', 'plan/designDrawManager/drawWorkUserInfo', {
                                area: ['500px', '580px'],
                                btn: ['确定'],
                                data: {
                                    designDrawIdList: designDrawIdList,
                                    designDrawNoList: designDrawNoList,
                                    tableIns: tableIns,
                                    deptId: deptMap.get(pptdId),
                                    pptdId:pptdId,
                                    shipId: shipId,
                                    type : 'batch'
                                },
                                yes: function () {
                                    $('#drawWorkUserInfo').find('#submit').trigger('click');
                                }
                            });*/
                if (checkStatus.data.length === 1) {
                    type = '1'
                } else {
                    type = '2'
                }
                febs.modal.open('图纸作业人员编辑', 'plan/designDrawManager/chooseSsUser', {
                    area: ['1200px', '800px'],
                    btn: ['确定'],
                    data: {
                        designDrawIdList: designDrawIdList,
                        designDrawNoList: designDrawNoList,
                        tableIns: tableIns,
                        deptId: deptMap.get(pptdId),
                        professionId: pptdId,
                        shipId: shipId,
                        drawInfo: checkStatus.data[0],
                        type: type,
                        dataList: checkStatus.data
                    },
                    yes: function () {
                        $('#febs-chooseUser').find('#submit').trigger('click');
                    }
                });
            });
            // 批量保存
            $('#saveBatch').on('click', function () {
                let saveData;
                let msg = '是否确定批量保存已修改的数据';
                let thisCache = table.cache['drawList'] || {}
                let checkStatus = table.checkStatus('drawList');
                if (checkStatus.data.length === 0) {
                    saveData = thisCache;
                    msg = '未勾选要保存的数据，将全部保存';
                } else {
                    saveData = checkStatus.data;
                }
                if (saveData === null || saveData.length === 0) {
                    febs.alert.warn('当前页面无数据，无需保存');
                    return false;
                }
                let drawInfos = [];
                $.each(saveData, function (i, v) {
                    let drawInfo = {
                        designDrawId: v.designDrawId,
                        designDrawDesc: v.designDrawDesc,
                        drawNeedFlg: v.drawNeedFlg,
                        strId: v.strId,
                        checkUserId: v.checkUserId,
                        verifyUserId: v.verifyUserId
                    }
                    drawInfos.push(drawInfo);
                })
                febs.modal.confirm('批量保存', msg, function () {
                    febs.postArray(ctx + 'plan/designDrawInfo/updateBatch', drawInfos, function () {
                        layer.closeAll();
                        febs.alert.success('修改成功');
                        tableIns.reloadData()
                    });
                })
            });
            // 批量删除
            $('#deleteBatch').on('click', function () {
                let checkStatus = table.checkStatus('drawList');
                let drawInfoIds = [];
                let flag = 0;
                if (checkStatus.data.length === 0) {
                    febs.alert.warn("请至少选择一条数据")
                    return false;
                }
                $.each(checkStatus.data, function (i, v) {
                    drawInfoIds.push(v.designDrawId);
                    if (v.drawUseStatus == "1") {
                        flag = 1;
                        febs.alert.warn("该图纸" + v.designDrawNo + "已被使用，无法删除！");
                        return false;
                    }
                    if (v.trayInitFlg == "1") {
                        flag = 1;
                        febs.alert.warn("该图纸" + v.designDrawNo + "已存在托盘，无法删除!");
                        return false;
                    }
                })
                if (flag === 0) {

                    febs.modal.confirm('删除', '是否确定删除选中的图纸信息', function () {
                        febs.get(ctx + 'plan/designDrawInfo/delete/' + drawInfoIds, {}, function (e) {
                            if (e.code == 200) {
                                febs.alert.success('删除成功');
                                $.each(checkStatus.data, function (i, v) {
                                    const $tr = $(`div[lay-table-id="${tableIns.config.id}"]`).find(`table tr[data-index="${v.rowIndex}"]`);
                                    table.setRowChecked(tableIns.config.id, {
                                        index: v.rowIndex,
                                        checked: false
                                    })
                                    $tr.remove();
                                })
                            }
                        })
                    })
                }
            });

            //保存
            function save(obj) {
                var index = obj.index;
                let drawInfo = {
                    designDrawId: obj.data.designDrawId,
                    designDrawDesc: obj.data.designDrawDesc,
                    designDrawDescEn: obj.data.designDrawDescEn,
                    drawNeedFlg: obj.data.drawNeedFlg,
                    strId: obj.data.strId,
                    remark: obj.data.remark,
                }
                febs.postArray(ctx + 'plan/designDrawInfo/update', drawInfo, function (data) {
                    if (data.code == 200) {
                        table.cache['drawList'][index].designDrawDesc = drawInfo.designDrawDesc;
                        table.cache['drawList'][index].designDrawDescEn = drawInfo.designDrawDescEn;
                        table.cache['drawList'][index].remark = drawInfo.remark;
                        febs.alert.success('修改成功');
                    }
                });
            }
            // //右上表格数据点击事件选中
            // table.on('row(drawList)', function (obj) {
            //     // 标注当前点击行的选中状态
            //     obj.setRowChecked({
            //         type: 'checkbox' // radio 单选模式；checkbox 复选模式
            //     });
            // })
            //设置右击菜单
            $view.on('contextmenu', '.layui-tree-set', function (e) {
                if (shipNoSelect.getValue('value').length === 0) {
                    febs.alert.warn('请选择船号');
                    return false;
                }
                shipId = shipNoSelect.getValue('valueStr')
                $this = $(this)
                let dataNo = $(this).children('.layui-tree-entry').find('.layui-tree-txt').text()
                let type;
                let typeStr = $this.data('id').split('_')[0]
                let dataId = $this.data('id').split('_')[1]
                if (typeStr === 'PE') {
                    type = 0
                } else if (typeStr === 'BLOCK') {
                    type = 1
                } else if (typeStr === 'REGION') {
                    type = 2
                    dataNo = dataNo.split('(')[1].split(')')[0];
                }
                if (dataNo.length !== 3) {
                    febs.alert.warn('编码长度不符合规范，应为三位编码');
                    return false;
                }
                //右击加工分段生成右击菜单
                $view.find("#mymenu").empty()
                let html = ''
                html += '<div id="addDraw" data-type="' + type + '" data-blockno="' + dataNo + '" data-blockid="' + dataId + '" data-shipid="' + shipId + '" style="margin-top: 5px">新增</div>'
                $view.find("#mymenu").append(html)
                if ($view.height() - e.clientY >= 38) {
                    $view.find('#mymenu').css({
                        display: 'block',
                        left: e.clientX,
                        top: e.clientY
                    })
                } else {
                    $view.find('#mymenu').css({
                        display: 'block',
                        left: e.clientX,
                        top: e.clientY - 38
                    })
                }
                return false
            })
            //鼠标离开隐藏右击菜单
            $view.on("mouseleave", "#mymenu", function () {
                $view.find('#mymenu').css('display', 'none')
            })

            // 单元格编辑事件
            table.on('edit(drawList)', function (obj) {
                // $(obj.tr[0]).addClass('edited')
                // let index2 = $(obj.tr[0]).data('index');
                // table.setRowChecked('drawList',{
                //     index: index2,
                //     checked:true
                // })
                save(obj);
            });
            // 新增
            $('#add').on('click', function () {
                if (shipNoSelect.getValue('value').length === 0) {
                    febs.alert.warn('请选择船号')
                    return false;
                }
                febs.modal.open('设计图纸新增', 'plan/designDrawManager/add', {
                    area: ['600px', '600px'],
                    btn: ['确定'],
                    data: { shipId: shipNoSelect.getValue('valueStr') },
                    yes: function () {
                        $('#designDrawEdit').find('#submit').trigger('click');
                        tableIns.reload()
                    }
                });
            });
            //导入
            $('#import').on('click', function () {
                if (shipNoSelect.getValue('valueStr') === '') {
                    febs.alert.warn('请选择船号')
                    return false;
                }
                febs.modal.open('导入', 'plan/designDrawManager/import', {
                    btn: ['开始上传'],
                    area: ['700px', '480px'],
                    data: { shipNo: shipNoSelect.getValue('nameStr'), shipTypeId: shipTypeId },
                    yes: function (e) {
                        $('#importDesignDraw').find('#test9').trigger('click');
                    }
                });
            });
            //复制
            $('#copy').on('click', function () {
                febs.modal.open('复制', 'plan/designDrawManager/copy', {
                    btn: ['提交'],
                    area: ['540px', '400px'],
                    data: { shipNoSelect: allShips },
                    yes: function (e) {
                        $('#copyCarryNo').find('#submit').trigger('click');
                    }
                });
            });

            //导出
            $('#expData').on('click', function () {
                if (shipNoSelect.getValue('value').length === 0) {
                    febs.alert.warn('请选择船号')
                    return false;
                } else {
                    let shipId = shipNoSelect.getValue('valueStr');
                    //编号
                    let ddNo = $view.find('#ddNo').val();
                    //设计作业
                    let processSelected = processSelect.getValue('valueStr');
                    //设计阶段
                    let stageSelected = stageSelect.getValue('valueStr');
                    //设计专业
                    let majorSelected = majorSelect.getValue('valueStr');
                    //图纸类型
                    let typeSelected = typeSelect.getValue('valueStr');
                    let param = {
                        designDrawNo: ddNo,
                        shipId: shipNoSelect.getValue('valueStr'),
                        pdwId: processSelected,
                        pptdId: majorSelected,
                        pdpdId: stageSelected,
                        pdtdId: typeSelected
                    }
                    febs.download(ctx + 'plan/designDrawInfo/exportDesignDrawInfoExcel',
                        param,
                        '图纸清单导出' + new Date().getTime() + '.xlsx');
                    console.log(param)
                }
            })
            table.on('tool(drawList)', function (obj) {
                // 根据不同的事件名进行相应的操作
                switch (obj.event) { // 对应模板元素中的 lay-event 属性值
                    case 'detailUser':
                        detailUser(obj.data);
                        break;
                    case 'cxqySelect':
                        cxqySelect(obj);
                        break
                }
                return false;
            });
            function detailUser(data) {
                febs.modal.open('生产设计专业作业人员信息', 'plan/designDrawManager/chooseSsUser', {
                    area: ['1200px', '700px'],
                    data: {
                        drawInfo: data,
                        professionId: data.pptdId,
                        type: '0'
                    }
                });
            }

            function cxqySelect(obj) {
                var index = obj.index;
                let item = obj.data;
                $view.find('#CXQY-' + item.designDrawId).empty()
                xmSelect.render({
                    el: '#CXQY-' + item.designDrawId,
                    autoRow: true,
                    model: {
                        label: {
                            type: 'text',
                        }
                    },
                    radio: true,
                    clickClose: true,
                    data: regionsArr,
                    template({ item }) {
                        return item.name + '<span style="position: absolute;right: 10px;color: #90a4ae">' + item.showname + '</span>'
                    },
                    disabled: item.drawUseStatus == 0 && editFlg ? false : true,
                    // regionsMap.set(v.strId,v.strCodeName);
                    initValue: item.strId == null ? [] : [item.strId],
                    on: function (e) {
                        if (e.isAdd) {
                            item.strId = e.arr[0].value;
                            let drawInfo = {
                                designDrawId: item.designDrawId,
                                strId: item.strId
                            }
                            febs.postArray(ctx + 'plan/designDrawInfo/update', drawInfo, function (data) {
                                if (data.code == 200) {
                                    table.cache['drawList'][index].strId = e.arr[0].value;
                                    febs.alert.success('修改成功');
                                }
                            });
                        }
                    }
                })
            }
        });
    </script>
</body>

</html>