<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <th:block th:include="include::header('搭载')" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" th:href="@{/febs/views/css/screen/commonScreen.css}" media="all">
    <link rel="stylesheet" th:href="@{/plugins/animsition/css/animsition.min.css}" media="all">
    <link rel="stylesheet" th:href="@{/febs/views/css/screen/shipModel.css}" media="all">
    <link rel="stylesheet" th:href="@{/febs/views/css/screen/steps.css}" media="all">
    <link rel="stylesheet" th:href="@{/plugins/otherLinks/otherLinks.css}" media="all">
    <style type="text/css">
        .layui-layer-title {
            display: none;
        }

        .layui-layer {
            background-color: #061729;
        }

        /* 滚动条轨道 */
        ::-webkit-scrollbar-track {
            background: #49b1f5;
        }

        ::-webkit-scrollbar {
            display: block !important;
        }

        ::-webkit-scrollbar-thumb {
            background-color: #3c648b !important;
        }

        ::-webkit-scrollbar-track {
            background-color: #07325b;
        }

        #stepContent {
            scroll-behavior: smooth;
        }

        /* 排序面板样式 */
        #sortPanel {
            position: fixed;
            top: 50px;
            right: 20px;
            width: 350px;
            height: calc(100vh - 100px);
            background: rgba(6, 23, 41, 0.95);
            border: 1px solid #3c648b;
            border-radius: 8px;
            padding: 20px;
            z-index: 1000;
            display: none;
            overflow-y: auto;
        }

        #sortPanel h3 {
            color: white;
            margin: 0 0 15px 0;
            font-size: 16px;
            text-align: center;
            border-bottom: 1px solid #3c648b;
            padding-bottom: 10px;
        }

        .sort-controls {
            margin-bottom: 15px;
        }

        .sort-controls label {
            color: #49b1f5;
            font-size: 12px;
            margin-bottom: 5px;
            display: block;
        }

        .sort-controls select {
            width: 100%;
            padding: 5px;
            background: #07325b;
            color: white;
            border: 1px solid #3c648b;
            border-radius: 4px;
            font-size: 12px;
        }

        .sort-item {
            background: rgba(52, 175, 210, 0.1);
            border: 1px solid #3c648b;
            border-radius: 4px;
            padding: 8px;
            margin-bottom: 8px;
            cursor: move;
            transition: all 0.3s ease;
        }

        .sort-item:hover {
            background: rgba(52, 175, 210, 0.2);
            border-color: #49b1f5;
        }

        .sort-item.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }

        .sort-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }

        .sort-item-name {
            color: white;
            font-weight: bold;
            font-size: 13px;
        }

        .sort-item-date {
            color: #49b1f5;
            font-size: 12px;
        }

        .sort-item-details {
            color: #ccc;
            font-size: 12px;
            line-height: 1.3;
        }

        .sort-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .sort-btn {
            flex: 1;
            padding: 8px;
            background: #3c648b;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s ease;
        }

        .sort-btn:hover {
            background: #49b1f5;
        }

        .sort-btn.primary {
            background: #49b1f5;
        }

        .sort-btn.primary:hover {
            background: #34afd2;
        }

        .toggle-sort-btn {
            position: fixed;
            top: 54.4%;
            right: 20px;
            width: 40px;
            height: 40px;
            background: #3c648b;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            z-index: 1001;
            transition: all 0.3s ease;
            transform: translateY(-50%);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            padding-right: 2px;
            padding-top: 1px;
        }

        .toggle-sort-btn:hover {
            background: #49b1f5;
            transform: translateY(-50%) scale(1.1);
        }

        .toggle-sort-btn.expanded {
            right: 352px;
            padding-left: 3px;
            padding-bottom: 1px;
        }
    </style>

</head>

<body style="overflow: hidden">
    <div id="shipModel" class="groupBox animsition" data-animsition-in-duration="1200">

        <div class="mainContent">
            <div class="menuLine">
                <img id="returnBack" th:src="@{/febs/images/virual/backBtn.png}" alt="" class="backBtn"
                    style="position: absolute;right: 40px;top: 12px;cursor: pointer;z-index: 9999;">
                <div>
                    <div class="shipNo" style="left: 9%"></div>
                    <div class="shipInfo">
                        <div class="shipInfoDetail">
                            <span style="color: darkgray">交船日期:</span>
                            <span class="dateVal"></span>
                        </div>
                        <div class="shipInfoDetail">
                            <span style="color: darkgray">船型:</span>
                            <span class="typeVal"></span>
                        </div>
                        <div class="shipInfoDetail">
                            <span style="color: darkgray">船东:</span>
                            <span class="ownerVal"></span>
                        </div>
                    </div>
                </div>

                <div id="stepContent">
                </div>
                <div class="tools">
                    <div style="padding: 16px;" class="toolsDetail">
                        <div class="topBtn" id="splitpy" style="display: flex;">平移分解</div>
                        <div class="topBtn" id="splitbz" style="display: flex;">爆炸分解</div>
                        <div class="topBtn" id="dzmn" style="display: flex;">搭载模拟</div>
                        <!--                    <div class="topBtn" id="hidemodel" style="display: flex;">隐藏模型</div>-->
                        <div class="topBtn" id="merge" style="display: flex;">合并模型</div>
                        <div class="topBtn" id="showUp" style="display: flex;">旋转模型</div>
                        <div class="topBtn" id="recoverBtn" style="display: flex;">还原</div>
                        <!--                                <button class="topBtn" id="modelsChange" style="display: flex;">批量操作</button>-->
                    </div>
                </div>
                <div id="tipsBox" class="febs-hide">
                </div>
                <div id="blockDetail" class="febs-hide">
                </div>
            </div>
            <!-- <img class="changeRight" src="/visual/febs/images/virual/group/arrR.png" alt="">-->
            <div class="menubox">
                <select id="nameOption">
                    <option value=""></option>
                </select>
                <button id="search" style="display: flex;">查找</button>
            </div>
            <!-- <div id="boxMenu" class="boxmenu">
<ul id="menuList">
</ul>
</div> -->
            <div id="canvas"></div>

        </div>

        <!-- 排序功能切换按钮 -->
        <button class="toggle-sort-btn" id="toggleSortBtn">◀</button>

        <!-- 排序面板 -->
        <div id="sortPanel">
            <h3>搭载顺序排序</h3>

            <div class="sort-controls">
                <label for="sortBy">排序方式:</label>
                <select id="sortBy">
                    <option value="carryDate">按搭载日期</option>
                    <option value="peName">按部件名称</option>
                    <option value="length">按长度</option>
                    <option value="width">按宽度</option>
                    <option value="height">按高度</option>
                    <option value="manual">手动排序</option>
                </select>
            </div>

            <div class="sort-controls">
                <label for="sortOrder">排序顺序:</label>
                <select id="sortOrder">
                    <option value="asc">升序</option>
                    <option value="desc">降序</option>
                </select>
            </div>

            <div class="sort-buttons">
                <button class="sort-btn" id="applySortBtn">应用排序</button>
                <button class="sort-btn" id="resetSortBtn">重置</button>
            </div>

            <div id="sortableList" style="margin-top: 15px; max-height: calc(100vh - 420px); overflow-y: auto;">
                <!-- 排序项目将在这里动态生成 -->
            </div>

            <div class="sort-buttons" style="margin-top: 15px;">
                <button class="sort-btn primary" id="saveSortBtn">保存新数组</button>
            </div>
        </div>
    </div>
    <th:block th:include="include::foot" />
    <script th:src="@{/plugins/animsition/js/animsition.min.js}"></script>
    <script th:src="@{/plugins/otherLinks/jquery-otherlinks.js}"></script>
    <script th:src="@{/plugins/tween.js-23.1.3/dist/tween.umd.js}"></script>
    <script th:src="@{/plugins/typed.umd.js}"></script>
    <script th:inline="javascript">
    </script>
    <script type="importmap" data-turbo-track="reload">

        {
            "imports": {
                "three": "[[@{/plugins/threejs/build/three.module.js}]]",
                "three/addons/": "[[@{/plugins/threejs/examples/jsm/}]]"
            }
        }

    </script>
    <script data-th-inline="javascript" type="text/javascript">
        $(document).ready(function () {

            $(".animsition").animsition({
                inClass: 'zoom-in',
                outClass: 'zoom-out',
                inDuration: 1200,
                outDuration: 1200,
                linkElement: '.animsition-link',
                // e.g. linkElement   :   'a:not([target="_blank"]):not([href^=#])'
                loading: true,
                loadingParentElement: 'body', //animsition wrapper element
                loadingClass: 'animsition-loading',
                unSupportCss: ['animation-duration',
                    '-webkit-animation-duration',
                    '-o-animation-duration'
                ],
                overlay: false,
                overlayClass: 'animsition-overlay-slide',
                overlayParentElement: 'body'
            });

            // 屏幕自适应
            function resize() {
                var ratioX = $(window).width() / 1920;
                var ratioY = $(window).height() / 1080;
                $("body").css({
                    transform: "scale(" + ratioX + "," + ratioY + ")",
                    transformOrigin: "left top",
                    backgroundSize: "100% 100%"
                });
                $("html").css({ 'overflow': 'hidden' })
            }

            $(window, document).resize(function () {
                resize();
            });
            resize();
            $("#returnBack").on('click', function () {//返回按钮
                ctx = ctx == "\/" ? "" : ctx;
                window.history.back()
            })
        })
        var ctx = [[@{/}]];
        ctx = ctx == "\/" ? "" : ctx;
        var currentUser = [[${ user }]];

        var shipId = [[${ shipId }]];
        var shipNo = [[${ shipNo }]];


    </script>
    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js';
        import { FBXLoader } from 'three/addons/loaders/FBXLoader.js';
        import { CSS2DRenderer, CSS2DObject } from 'three/addons/renderers/CSS2DRenderer.js';
        let camera, scene, renderer, controls, container, labelRenderer, light, modul, itemList = [], chooseModels = [];

        var febs = null;

        layui.config({
            base: ctx + '/febs/',
            debug: true
        }).extend({
            febs: 'lay/modules/febs',
            request: 'lay/modules/request',
            formSelects: 'lay/extends/formSelects-v4.min',
            treeSelect: 'lay/extends/treeSelect',
            apexcharts: 'lay/extends/apexcharts.min',
            eleTree: 'lay/extends/eleTree',
            selectN: 'lay/extends/selectN',
            selectM: 'lay/extends/selectM',
            commonJS: 'lay/extends/common',
            jqueryui: 'lay/extends/jquery-ui.min',
            echarts: 'lay/extends/echarts.min',
            treetable: 'lay/extends/treetable',
            tagsinput: 'lay/extends/jquery.tagsinput',
            media: 'lay/extends/jquery.media',
            steps: 'lay/extends/steps',
        }).use(['febs', 'steps'], function () {
            var $ = layui.$,
                febs = layui.febs,
                $view = $("#shipModel"),
                steps = layui.steps;

            var stepData = [];
            let carryData = [];
            var modelURL = ''
            var modelScalar = undefined
            let isSimulating = false; //是否正在搭载
            let carryAll;
            function carryList() {

                febs.getSyn(ctx + "dockship/carryDateList", { shipId: shipId }, function (data) {
                    const stepContent = document.getElementById('stepContent');

                    let arr = data.data;
                    let index = 0;
                    let html = '';
                    let toJump = 0;
                    let date;
                    $.each(data.data, function (i, val) {
                        if (val.isCurrentOrNearest == 0 && i > 4) {
                            toJump = (i - 4) * 214;
                            date = val.carryDate
                        } else if (val.isCurrentOrNearest == 0) {
                            date = val.carryDate
                        }
                        html += `
                     <div class="nodeItem" data-carrydate="${val.carryDate}" style="display: flex;flex-direction: column;align-items: center" data-timeFlg="${val.timeFlg}" data-isCurrentOrNearest="${val.isCurrentOrNearest}" >
                        <div data-carrydate="${val.carryDate}" class="carryDate" style="${val.timeFlg === 0 || val.isCurrentOrNearest === 1 ? 'color: white' : 'color: darkgray'}">
                           ${val.carryDate}
                        </div>

                        <img class="noClick" src="${ctx}febs/images/virual/carry/${val.timeFlg === 0 ? 'checked.png' : 'noChecked.png'}">
                        <img  class="febs-hide click" src="${ctx}febs/images/virual/carry/${val.timeFlg === 0 ? 'checkedClick.png' : 'noCheckedClick.png'}">
                        <div  style="${val.timeFlg === 0 ? 'color: white' : 'color: darkgray'}">
                                ${val.completeCount}/${val.totalCount}
                        </div>
                     </div>
                    `;
                        if ((data.data.length - 1) != i) {
                            html += `
                            <img class="connectLine" src="${ctx}febs/images/virual/carry/connectLine.png">
                        `
                        }
                        stepData.push(val.carryDate)
                    })
                    $view.find("#stepContent").append(html);


                    // 确保 DOM 更新后触发
                    setTimeout(() => {
                        const $target = $view.find(".nodeItem[data-carrydate='" + date + "']");
                        if ($target.length) {
                            $target.trigger("click");
                            stepContent.scrollTo({
                                left: stepContent.scrollLeft + toJump,
                                behavior: 'smooth' // 启用平滑滚动
                            });
                        } else {
                        }
                    }, 100);
                })


            }





            febs.getSyn(ctx + "dockship/getShipDetail", { shipId: shipId }, function (data) {
                let ship = data.data;
                $view.find(".dateVal").text(ship.deliverDay)
                $view.find(".typeVal").text(ship.typeName)
                $view.find(".ownerVal").text(ship.shipOwner)
                $view.find(".shipNo").text(ship.shipNo)
            })

            $('#title1').html(shipNo)
            $('#tipsBox').hide()
            $("#blockDetail").hide()

            var oldintersects = [];


            let changeBox = document.createElement('div')
            changeBox.classList.add('changeBox')
            document.body.appendChild(changeBox);


            function init(date) {
                container = document.getElementById('canvas');
                camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 0.3, 12000);
                camera.position.set(0, 10, 50);
                scene = new THREE.Scene();
                // scene.background = new THREE.Color(0xFAD7A0);
                light = new THREE.AmbientLight(0xffffff, 1);
                // const axesHelper = new THREE.AxesHelper(50)
                scene.add(light);
                // scene.add(axesHelper)




                // febs.getSyn(ctx + "shipCategory/getModel", { shipId: shipId }, function (data) {
                //     modelURL = ctx + "/images/ship/categoryFile/" + data.data.catePic
                //     modelScalar=data.data.scalar;
                //
                //     // modelScalar = 1
                // })

                febs.getSyn(ctx + "dockship/carryDetail", { shipId: shipId, date: date }, function (data) {

                    modelURL = ctx + "/images/ship/categoryFile/" + data.data.catePic;
                    modelScalar = data.data.scalar;
                    // carryAll = data.data.carryAll;
                    carryAll = [
                        {
                            "carryDate": "2025-06-07",
                            "peName": "10A",
                            "length": 1,
                            "width": 1,
                            "height": 1,
                            "blockNoList": [
                                "101",
                                "102",
                                "110",
                                "121",
                                "131",
                                "103"
                            ]
                        },
                        {
                            "carryDate": "2025-07-13",
                            "peName": "105",
                            "length": 1,
                            "width": 1,
                            "height": 1,
                            "blockNoList": [
                                "105"
                            ]
                        },
                        {
                            "carryDate": "2025-06-20",
                            "peName": "10C",
                            "length": 125.84,
                            "width": 286.5,
                            "height": 109.95,
                            "blockNoList": [
                                "111",
                                "181",
                                "182",
                                "191",
                                "192"
                            ]
                        },
                        {
                            "carryDate": "2025-06-18",
                            "peName": "12B",
                            "length": 196.6,
                            "width": 150.99,
                            "height": 96.32,
                            "blockNoList": [
                                "122",
                                "123",
                                "142",
                                "143"
                            ]
                        },
                        {
                            "carryDate": "2025-06-18",
                            "peName": "13B",
                            "length": 196.6,
                            "width": 171.09,
                            "height": 96.32,
                            "blockNoList": [
                                "132",
                                "133",
                                "152",
                                "153"
                            ]
                        },
                        {
                            "carryDate": "2025-06-04",
                            "peName": "20A",
                            "length": 1,
                            "width": 1,
                            "height": 1,
                            "blockNoList": [
                                "202",
                                "621",
                                "622",
                                "631",
                                "632"
                            ]
                        },
                        {
                            "carryDate": "2025-06-04",
                            "peName": "203",
                            "length": 13.58,
                            "width": 18.3,
                            "height": 1.78,
                            "blockNoList": [
                                "203"
                            ]
                        },
                        {
                            "carryDate": "2025-06-04",
                            "peName": "204",
                            "length": 13.58,
                            "width": 18.3,
                            "height": 1.78,
                            "blockNoList": [
                                "204"
                            ]
                        },
                        {
                            "carryDate": "2025-06-01",
                            "peName": "205",
                            "length": 13.58,
                            "width": 18.3,
                            "height": 1.78,
                            "blockNoList": [
                                "205"
                            ]
                        },
                        {
                            "carryDate": "2025-06-01",
                            "peName": "206",
                            "length": 14.98,
                            "width": 18.3,
                            "height": 1.78,
                            "blockNoList": [
                                "206"
                            ]
                        },
                        {
                            "carryDate": "2025-06-07",
                            "peName": "207",
                            "length": 14.98,
                            "width": 18.3,
                            "height": 1.78,
                            "blockNoList": [
                                "207"
                            ]
                        },
                        {
                            "carryDate": "2025-06-07",
                            "peName": "208",
                            "length": 14.2,
                            "width": 18.3,
                            "height": 1.78,
                            "blockNoList": [
                                "208"
                            ]
                        },
                        {
                            "carryDate": "2025-06-07",
                            "peName": "209",
                            "length": 14.2,
                            "width": 18.3,
                            "height": 1.78,
                            "blockNoList": [
                                "209"
                            ]
                        },
                        {
                            "carryDate": "2025-06-07",
                            "peName": "211",
                            "length": 14.2,
                            "width": 18.3,
                            "height": 1.78,
                            "blockNoList": [
                                "211"
                            ]
                        },
                        {
                            "carryDate": "2025-07-06",
                            "peName": "20B",
                            "length": 142,
                            "width": 314.28,
                            "height": 97.5,
                            "blockNoList": [
                                "212",
                                "642",
                                "652"
                            ]
                        },
                        {
                            "carryDate": "2025-07-10",
                            "peName": "301",
                            "length": 9.5,
                            "width": 1,
                            "height": 9.5,
                            "blockNoList": [
                                "301"
                            ]
                        },
                        {
                            "carryDate": "2025-06-19",
                            "peName": "40B",
                            "length": 183,
                            "width": 176.18,
                            "height": 107.83,
                            "blockNoList": [
                                "403",
                                "503"
                            ]
                        },
                        {
                            "carryDate": "2025-06-22",
                            "peName": "40C",
                            "length": 183,
                            "width": 176.18,
                            "height": 107.83,
                            "blockNoList": [
                                "405",
                                "505"
                            ]
                        },
                        {
                            "carryDate": "2025-07-01",
                            "peName": "40D",
                            "length": 183,
                            "width": 176.18,
                            "height": 107.83,
                            "blockNoList": [
                                "407",
                                "507"
                            ]
                        },
                        {
                            "carryDate": "2025-07-01",
                            "peName": "40E",
                            "length": 183,
                            "width": 176.18,
                            "height": 107.83,
                            "blockNoList": [
                                "411",
                                "511"
                            ]
                        },
                        {
                            "carryDate": "2025-06-22",
                            "peName": "501",
                            "length": 4.3,
                            "width": 16.4,
                            "height": 2.7,
                            "blockNoList": [
                                "501"
                            ]
                        },
                        {
                            "carryDate": "2025-07-07",
                            "peName": "512",
                            "length": 3.89,
                            "width": 16.4,
                            "height": 2.7,
                            "blockNoList": [
                                "512"
                            ]
                        },
                        {
                            "carryDate": "2025-07-10",
                            "peName": "513",
                            "length": 0,
                            "width": 0,
                            "height": 0,
                            "blockNoList": [
                                "513"
                            ]
                        },
                        {
                            "carryDate": "2025-07-10",
                            "peName": "514",
                            "length": 0,
                            "width": 0,
                            "height": 0,
                            "blockNoList": [
                                "514"
                            ]
                        },
                        {
                            "carryDate": "2025-07-10",
                            "peName": "515",
                            "length": 0,
                            "width": 0,
                            "height": 0,
                            "blockNoList": [
                                "515"
                            ]
                        },
                        {
                            "carryDate": "2025-07-10",
                            "peName": "516",
                            "length": 0,
                            "width": 0,
                            "height": 0,
                            "blockNoList": [
                                "516"
                            ]
                        },
                        {
                            "carryDate": "2025-07-12",
                            "peName": "517",
                            "length": 0,
                            "width": 0,
                            "height": 0,
                            "blockNoList": [
                                "517"
                            ]
                        },
                        {
                            "carryDate": "2025-06-21",
                            "peName": "52A",
                            "length": 253.2,
                            "width": 129.6,
                            "height": 79.3,
                            "blockNoList": [
                                "521",
                                "522"
                            ]
                        },
                        {
                            "carryDate": "2025-06-22",
                            "peName": "52B",
                            "length": 271.6,
                            "width": 204.86,
                            "height": 79.3,
                            "blockNoList": [
                                "523",
                                "524",
                                "623",
                                "624"
                            ]
                        },
                        {
                            "carryDate": "2025-06-24",
                            "peName": "52C",
                            "length": 291.8,
                            "width": 204.86,
                            "height": 79.3,
                            "blockNoList": [
                                "525",
                                "526",
                                "625",
                                "626"
                            ]
                        },
                        {
                            "carryDate": "2025-07-03",
                            "peName": "52D",
                            "length": 291.8,
                            "width": 204.86,
                            "height": 79.3,
                            "blockNoList": [
                                "527",
                                "528",
                                "627",
                                "628"
                            ]
                        },
                        {
                            "carryDate": "2025-07-05",
                            "peName": "52E",
                            "length": 284,
                            "width": 204.86,
                            "height": 79.3,
                            "blockNoList": [
                                "529",
                                "541",
                                "629",
                                "641"
                            ]
                        },
                        {
                            "carryDate": "2025-06-21",
                            "peName": "53A",
                            "length": 253.2,
                            "width": 129.6,
                            "height": 79.3,
                            "blockNoList": [
                                "531",
                                "532"
                            ]
                        },
                        {
                            "carryDate": "2025-06-22",
                            "peName": "53B",
                            "length": 271.6,
                            "width": 204.86,
                            "height": 79.3,
                            "blockNoList": [
                                "533",
                                "534",
                                "633",
                                "634"
                            ]
                        },
                        {
                            "carryDate": "2025-06-24",
                            "peName": "53C",
                            "length": 291.8,
                            "width": 204.86,
                            "height": 79.3,
                            "blockNoList": [
                                "535",
                                "536",
                                "635",
                                "636"
                            ]
                        },
                        {
                            "carryDate": "2025-07-03",
                            "peName": "53D",
                            "length": 291.8,
                            "width": 204.86,
                            "height": 79.3,
                            "blockNoList": [
                                "537",
                                "538",
                                "637",
                                "638"
                            ]
                        },
                        {
                            "carryDate": "2025-07-05",
                            "peName": "53E",
                            "length": 284,
                            "width": 204.86,
                            "height": 79.3,
                            "blockNoList": [
                                "539",
                                "551",
                                "639",
                                "651"
                            ]
                        },
                        {
                            "carryDate": "2025-07-07",
                            "peName": "542",
                            "length": 14.2,
                            "width": 7.93,
                            "height": 9.495,
                            "blockNoList": [
                                "542"
                            ]
                        },
                        {
                            "carryDate": "2025-07-07",
                            "peName": "552",
                            "length": 14.2,
                            "width": 7.93,
                            "height": 9.495,
                            "blockNoList": [
                                "552"
                            ]
                        },
                        {
                            "carryDate": "2025-07-06",
                            "peName": "80A",
                            "length": 148.46,
                            "width": 246.25,
                            "height": 84,
                            "blockNoList": [
                                "801",
                                "802",
                                "803"
                            ]
                        },
                        {
                            "carryDate": "2025-07-08",
                            "peName": "80B",
                            "length": 187.4,
                            "width": 294.41,
                            "height": 146,
                            "blockNoList": [
                                "804",
                                "805",
                                "806",
                                "807",
                                "827",
                                "837"
                            ]
                        },
                        {
                            "carryDate": "2025-07-15",
                            "peName": "90A",
                            "length": 201.6,
                            "width": 323.6,
                            "height": 255.18,
                            "blockNoList": [
                                "901",
                                "902",
                                "903",
                                "904",
                                "905",
                                "927",
                                "928",
                                "929",
                                "931",
                                "937",
                                "938",
                                "939"
                            ]
                        },
                        {
                            "carryDate": "2025-07-16",
                            "peName": "906",
                            "length": 6.1,
                            "width": 12,
                            "height": 13.8,
                            "blockNoList": [
                                "906"
                            ]
                        },
                        {
                            "carryDate": "2025-07-02",
                            "peName": "5舱舱口围",
                            "length": 1,
                            "width": 1,
                            "height": 1,
                            "blockNoList": [
                                "701",
                                "702",
                                "721",
                                "722",
                                "731",
                                "732"
                            ]
                        },
                        {
                            "carryDate": "2025-07-04",
                            "peName": "4舱舱口围",
                            "length": 1,
                            "width": 1,
                            "height": 1,
                            "blockNoList": [
                                "703",
                                "704",
                                "723",
                                "724",
                                "733",
                                "734"
                            ]
                        },
                        {
                            "carryDate": "2025-07-06",
                            "peName": "3舱舱口围",
                            "length": 1,
                            "width": 1,
                            "height": 1,
                            "blockNoList": [
                                "705",
                                "706",
                                "725",
                                "726",
                                "735",
                                "736"
                            ]
                        },
                        {
                            "carryDate": "2025-07-08",
                            "peName": "2舱舱口围",
                            "length": 1,
                            "width": 1,
                            "height": 1,
                            "blockNoList": [
                                "707",
                                "708",
                                "727",
                                "728",
                                "737",
                                "738"
                            ]
                        },
                        {
                            "carryDate": "2025-07-09",
                            "peName": "1舱舱口围",
                            "length": 1,
                            "width": 1,
                            "height": 1,
                            "blockNoList": [
                                "709",
                                "711",
                                "729",
                                "739",
                                "741",
                                "751"
                            ]
                        }
                    ]
                    $.each(data.data.carryDetail, function (i, val) {
                        carryData.push({ peName: val.peName, carryDate: val.carryDate })
                        $('#nameOption').append(
                            `<option value="${val.peName}">${val.peName}</option>`
                        )
                    })
                    // initModel(carryData,modelScalar,modelURL)
                    carryList();
                })




                const raycaster = new THREE.Raycaster()
                const mouse = new THREE.Vector2()

                const onClick = (event) => {

                    // 如果正在搭载模拟，则禁止点击
                    if (isSimulating) {
                        return;
                    }

                    // 获取canvas元素的位置和尺寸信息
                    const rect = canvas.getBoundingClientRect();

                    // 修正后的坐标转换：
                    mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
                    mouse.y = 1 - ((event.clientY - rect.top) / rect.height) * 2;
                    raycaster.params.Mesh.threshold = 2; // 调整检测阈值，单位是世界坐标系中的距离
                    raycaster.setFromCamera(mouse, camera);


                    const intersects = raycaster.intersectObjects(scene.children, true)

                    if (intersects.length > 0) {
                        const obj = intersects[0].object;
                        obj.raycastEnabled = true;
                        if (!window.event.ctrlKey) {
                            del(obj.name)
                        } else {
                            let selectNames = []
                            for (let i = 0; i < chooseModels.length; i++) {
                                selectNames.push(chooseModels[i].name)
                            }
                            if (chooseModels.length > 0) {
                                if (selectNames.indexOf(obj.name) > -1) {
                                    obj.material.color = new THREE.Color(0x34afd2);
                                    chooseModels = chooseModels.filter(item => item.name != obj.name);
                                } else {
                                    chooseModels.push(obj)
                                    obj.material.color = new THREE.Color(0xffd9b5);
                                }
                            } else {
                                chooseModels.push(obj)
                                obj.material.color = new THREE.Color(0xffd9b5);
                            }
                            $('#modelsChange').click()


                            // selectModels(chooseModels)
                        }

                    } else {
                        if (oldintersects.length > 0) {
                            if (oldintersects[oldintersects.length - 1].material.color.b == 1) {
                                oldintersects[oldintersects.length - 1].material.color = new THREE.Color(0x34afd2);
                            }
                        }
                    }

                }


                renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
                renderer.setPixelRatio(window.devicePixelRatio);
                renderer.setSize(window.innerWidth * 0.8, window.innerHeight * 0.75);

                renderer.domElement.addEventListener('click', onClick)

                renderer.shadowMap.enabled = true;
                container.appendChild(renderer.domElement);
                controls = new OrbitControls(camera, renderer.domElement);
                controls.target.set(0, 0, 0);

                controls.autoRotate = false;
                controls.autoRotateSpeed = 1
                // controls.update();
                const texLoader = new THREE.TextureLoader();
                // const tex = texLoader.load('images/label-bg.png')





                labelRenderer = new CSS2DRenderer();
                labelRenderer.setSize(window.innerWidth, window.innerHeight);
                labelRenderer.domElement.style.position = 'absolute';
                labelRenderer.domElement.style.top = '0px';
                labelRenderer.domElement.style.left = '0px';

                //设置.pointerEvents=none，以免模型标签HTML元素遮挡鼠标选择场景模型
                labelRenderer.domElement.style.pointerEvents = 'none';
                container.appendChild(labelRenderer.domElement)



                window.addEventListener('resize', onWindowResize, false);

                animate();
            }


            function initModel(carryData, modelScalar, modelURL) {
                resetModel()
                itemList = [];
                let carryPeName = carryData.map(item => item.peName);
                var loader = new FBXLoader();
                loader.load(modelURL, function (object) {
                    $('li').click(function () {
                        const name = $(this).attr('data-name')
                        del(name)
                    })
                    object.position.set(0, 0, 0)

                    object.scale.multiplyScalar(modelScalar);
                    // console.log(carryAll)
                    object.traverse((item) => {
                        if (item.isMesh && !item.name.includes("Hatch_cover")) {
                            const carryDate = carryAll.find(x => x.peName == item.name)?.carryDate || '';
                            itemList.push({ _e: item, name: item.name, carryDate: carryDate, itemX: item.position.x, itemY: item.position.y, itemZ: item.position.z })
                            if (carryPeName.length == 0 || carryPeName.includes(item.name)) {
                                const edges = new THREE.EdgesGeometry(item.geometry)
                                const em = new THREE.LineBasicMaterial({
                                    opacity: 0.6,
                                    color: 0xf0fbff,
                                    visible: true,
                                    linewidth: 0.2
                                })
                                const line = new THREE.LineSegments(edges, em)
                                item.add(line);
                                let newMaterial = new THREE.MeshLambertMaterial({
                                    color: new THREE.Color(0x34afd2), //可修改报警时的闪烁颜色
                                    transparent: true,
                                    opacity: 0.6, //可修改报警闪烁是的透明度
                                    wireframe: false,
                                    depthWrite: true,
                                    side: THREE.DoubleSide,
                                });
                                item.material = newMaterial
                                // 根据百分比动态染色（例如：染色50%）
                                // setColorByPercent(item, 50); // 这里传入百分比
                            } else {
                                const edges = new THREE.EdgesGeometry(item.geometry)
                                const em = new THREE.LineBasicMaterial({
                                    opacity: 0.1,
                                    color: 0xc8d7e6,
                                    visible: true,
                                    linewidth: 0.2
                                })
                                const line = new THREE.LineSegments(edges, em)
                                item.add(line);
                                let newMaterial = new THREE.MeshLambertMaterial({
                                    color: new THREE.Color(0x8ba4bb), //可修改报警时的闪烁颜色
                                    transparent: true,
                                    opacity: 0.2, //可修改报警闪烁是的透明度
                                    wireframe: false,
                                    depthWrite: true,
                                    side: THREE.DoubleSide,
                                });
                                item.material = newMaterial
                            }
                        }
                    });
                    scene.add(object);
                    // object.rotation.y += 1.58
                    modul = object;
                    // 使用Tween.js创建动画
                    // initAssembly(); // 在场景初始化阶段调用
                });

            }




            // 改进的resetModel函数
            function resetModel() {
                // 清空 itemList 并解除引用
                itemList.forEach(item => {
                    if (item._e) {
                        item._e = null; // 显式解除引用
                    }
                });
                itemList.length = 0; // 快速清空数组

                // 移除场景中的旧模型
                if (modul) {
                    scene.remove(modul);
                    modul.traverse(child => {
                        if (child.isMesh) {
                            child.geometry?.dispose();
                            child.material?.dispose();
                            child.children?.forEach(c => {
                                if (c.isLineSegments) {
                                    c.geometry.dispose();
                                    c.material.dispose();
                                }
                            });
                        }
                    });
                    modul = null;
                }

            }


            function InitTextLabel(text, labelPoint, clickEvent) {
                //创建div容器
                this.el = document.createElement('div');
                this.el.className = 'label';
                this.el.textContent = text
                this.el.style.pointerEvents = 'auto'
                this.el.addEventListener('pointerdown', function (e) {
                    if (clickEvent)
                        clickEvent(e)
                })
                this.pointLabel = new CSS2DObject(this.el);
                this.pointLabel.position.copy(labelPoint);

            }

            function onWindowResize() {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            }
            //
            function animate() {
                requestAnimationFrame(animate);
                TWEEN.update()
                //stats.update();
                controls.update();
                renderer.render(scene, camera);
                labelRenderer.render(scene, camera)
            }
            //计算传入模型包围盒的中心点
            function getCenterPoint(obj) {
                //包围盒
                const box = new THREE.Box3().setFromObject(obj);
                var center = new THREE.Vector3();
                //获取包围盒的中心点
                box.getCenter(center);
                return center;
            }
            //爆炸拆解传入模型 type 1:平移，2：爆炸
            function explodeModel(obj, val, type) {
                isSimulating = false;
                if (!obj) return;
                const center = getCenterPoint(obj);
                let y = obj.position.y;
                obj.traverse((child) => {
                    if (child.isMesh) {
                        // 保存原始位置
                        if (!child.userData.originalPosition) {
                            child.userData.originalPosition = child.position.clone();
                        }
                        // 计算相对中心的位置
                        const originalPosition = child.userData.originalPosition;
                        const direction = originalPosition.clone().sub(center).normalize();
                        const distance = originalPosition.distanceTo(center); // 新增距离计算
                        let targetPosition;
                        if (type == 1) {
                            //等比例拆解
                            targetPosition = child.position.clone().multiplyScalar(1.5);
                        } else {
                            // // 计算目标位置，基于原始位置加上方向向量乘以拆解系数，中心点爆炸拆解
                            const explosionFactor = 1; // 爆炸系数
                            targetPosition = originalPosition
                                .clone()
                                .add(direction.multiplyScalar((val - 1) * explosionFactor));

                        }
                        // 创建动画
                        new TWEEN.Tween(child.position)
                            .to(
                                {
                                    x: targetPosition.x,
                                    y: targetPosition.y,
                                    z: targetPosition.z,
                                },
                                2000
                            )
                            .easing(TWEEN.Easing.Quadratic.InOut)
                            .onUpdate(() => { })
                            .start();
                    }
                });
            }

            function initGlobalCenter() {
                const globalBox = new THREE.Box3();
                itemList.forEach(item => globalBox.expandByObject(item._e));
                globalBox.getCenter(globalCenter);
            }

            //合并传入模型
            function mergrModel(obj) {
                // $('#recoverBtn').trigger('click');
                isSimulating = false;
                if (!obj) return;
                obj.traverse((child) => {
                    if (child.isMesh) {
                        // child.material.color = new THREE.Color(0x34afd2);
                        new TWEEN.Tween(child.position)
                            .to(child.userData.originalPosition, 2000)
                            .easing(TWEEN.Easing.Quadratic.InOut)
                            .start();
                    }
                });
            }


            function dzmn(obj) {
                // 子组件位置还原
                obj.traverse((child) => {
                    if (child.isMesh) {
                        // child.material.color = new THREE.Color(0x34afd2);
                        new TWEEN.Tween(child.position)
                            .to(child.userData.originalPosition, 2000)
                            .easing(TWEEN.Easing.Quadratic.InOut)
                            .start();
                    }
                });
                explodeModel(modul, 2000, 1)
                // 获取 stepContent div
                const stepContent = document.getElementById('stepContent');
                stepContent.scrollTo({ left: 0 }); // 强制回到初始位置
                isSimulating = true; // 开始搭载模拟，禁止点击
                let currentTween = null;
                const peNames = carryData.map(item => item.peName);
                const orderMap = peNames.reduce((acc, name, index) => {
                    acc[name] = index;
                    return acc;
                }, {});
                $("#blockDetail").hide().empty();
                // itemList.sort((a, b) => orderMap[a.name] - orderMap[b.name]);
                itemList.sort((a, b) => {
                    if (a.carryDate === "" && b.carryDate === "") return 0;
                    if (a.carryDate === "") return 1;  // 空字符串的 a 排后面
                    if (b.carryDate === "") return -1; // 空字符串的 b 排后面
                    return a.carryDate.localeCompare(b.carryDate);
                });
                // ========== 视角控制参数 ==========
                const VIEW_CONFIG = {
                    BASE_DISTANCE_MULTIPLIER: 10,      // 基础距离系数
                    VERTICAL_OFFSET_RATIO: 0.3,         // 垂直偏移比例
                    HORIZONTAL_ANGLE_RANGE: 30,         // 水平观察角度（调整为90度侧视）
                    MIN_DISTANCE: 18.0,                 // 最小观察距离
                    MAX_DISTANCE: 50.0,                 // 最大观察距离
                    TARGET_OFFSET_RATIO: 1,           // 目标点横向偏移比例
                    VERTICAL_ANGLE: 70,                 // 垂直俯视角度
                    SIDE_OFFSET_RATIO: 0.8              // 新增侧向偏移系数
                };

                // ========== 初始化全局中心 ==========
                const globalBox = new THREE.Box3();
                const globalCenter = new THREE.Vector3();
                itemList.forEach(item => globalBox.expandByObject(item._e));
                globalBox.getCenter(globalCenter);
                //初始进度条
                let begin = 0;
                itemList.forEach((item, i) => {
                    // 保持模型位置固定
                    const targetPos = new THREE.Vector3(
                        item.itemX,
                        item.itemY,
                        item.itemZ
                    );

                    // ========== 包围盒计算 ==========
                    const localBox = new THREE.Box3().setFromObject(item._e);
                    const localCenter = new THREE.Vector3();
                    localBox.getCenter(localCenter);
                    const size = new THREE.Vector3();
                    localBox.getSize(size);

                    // ========== 动态视角方向计算 ==========
                    const positionDelta = localCenter.clone().sub(globalCenter);
                    const horizontalAngle = positionDelta.x > 0 ?
                        -VIEW_CONFIG.HORIZONTAL_ANGLE_RANGE :
                        VIEW_CONFIG.HORIZONTAL_ANGLE_RANGE;

                    // ========== 观察距离计算 ==========
                    const modelDiagonal = size.length();
                    let optimalDistance = modelDiagonal * VIEW_CONFIG.BASE_DISTANCE_MULTIPLIER;
                    optimalDistance = THREE.MathUtils.clamp(
                        optimalDistance,
                        VIEW_CONFIG.MIN_DISTANCE,
                        VIEW_CONFIG.MAX_DISTANCE
                    );

                    // ========== 创建动画序列 ==========
                    const tween = new TWEEN.Tween(item._e.position)
                        .to(targetPos, 2000)
                        .easing(TWEEN.Easing.Quadratic.InOut);

                    if (currentTween) {
                        currentTween.chain(tween);
                    } else {
                        tween.start().delay(1000);
                        $('#tipsBox').show();
                    }
                    currentTween = tween;

                    tween.onStart(() => {
                        $('.carryDate[data-carrydate="' + item.carryDate + '"]').parent().find('.click').show();
                        //同级隐藏
                        $('.carryDate[data-carrydate="' + item.carryDate + '"]').parent().find('.click').siblings('img').not(this).hide()
                        //其他的非点击的显示
                        $('.carryDate[data-carrydate="' + item.carryDate + '"]').parent().siblings('div').not(this).find(".click").hide();
                        $('.carryDate[data-carrydate="' + item.carryDate + '"]').parent().siblings('div').not(this).find(".noClick").show();

                        // 计算需要滚动的距离
                        if (stepData.indexOf(item.carryDate) != -1 && begin != stepData.indexOf(item.carryDate) && stepData.indexOf(item.carryDate) > 0) {
                            //平滑滚动到目标位置
                            const scrollDistance = 214
                            stepContent.scrollTo({
                                left: stepContent.scrollLeft + scrollDistance,
                                behavior: 'smooth' // 启用平滑滚动
                            });
                            begin = stepData.indexOf(item.carryDate);
                        }


                        // ========== 模型高亮 ==========
                        animateColorChange(item._e, new THREE.Color(0xFF722B));

                        // ========== 目标观察点计算 ==========
                        const sideOffset = size.x * VIEW_CONFIG.SIDE_OFFSET_RATIO *
                            (horizontalAngle > 0 ? 1 : -1);
                        const targetLookAt = localCenter.clone()
                            .add(new THREE.Vector3(
                                sideOffset,
                                0,
                                size.z * VIEW_CONFIG.TARGET_OFFSET_RATIO
                            ));

                        // ========== 相机位置计算 ==========
                        const horizontalRadian = THREE.MathUtils.degToRad(horizontalAngle);
                        const verticalRadian = THREE.MathUtils.degToRad(VIEW_CONFIG.VERTICAL_ANGLE);

                        // 球坐标计算基础位置
                        const cameraPosition = new THREE.Vector3()
                            .setFromSphericalCoords(
                                optimalDistance,
                                verticalRadian,
                                horizontalRadian
                            )
                            .add(targetLookAt);

                        // 横向位置补偿
                        cameraPosition.x += size.x * VIEW_CONFIG.TARGET_OFFSET_RATIO *
                            (horizontalAngle > 0 ? 1 : -1);
                        // 垂直偏移
                        cameraPosition.y += size.y * VIEW_CONFIG.VERTICAL_OFFSET_RATIO;

                        // ========== 相机动画 ==========
                        // new TWEEN.Tween(camera.position)
                        //     .to(cameraPosition, 1800)
                        //     .easing(TWEEN.Easing.Quadratic.InOut)
                        //     .start();

                        // ========== 信息提示 ==========
                        requestAnimationFrame(() => {
                            const $typed = $(`<span id='typed${i}' class="typed"></span>`)
                                .css({ opacity: 0 })
                                .animate({ opacity: 1 }, 500);
                            $('#tipsBox').empty().append($typed);

                            new Typed(`#typed${i}`, {
                                strings: [
                                    `<span class="carryInfo">总段名称:</span><span class="carryInfo">${item.name}</span><br>
                                         <span class="carryInfo">搭载日期:</span><span class="carryInfo">${item.carryDate}</span>`
                                ],
                                typeSpeed: 10,
                                startDelay: 100,
                                showCursor: false,
                                loop: false
                            });
                        });
                    });

                    tween.onComplete(() => {
                        // ========== 恢复模型颜色 ==========
                        item._e.material.color = new THREE.Color(0x34afd2);
                        item._e.material.opacity = 0.6;
                        // ========== 清理提示信息 ==========
                        $(`#typed${i}`).fadeOut(500, function () {
                            $(this).remove();
                        });

                        // 如果是最后一个模块，恢复点击功能
                        if (i === itemList.length - 1) {
                            isSimulating = false; // 模拟结束，恢复点击
                            $('#recoverBtn').trigger('click');
                            $('.nodeItem').find(".click").hide();
                            $('.nodeItem').find(".noClick").show();
                        }
                    });
                });
            }


            // 辅助函数：恢复全局状态
            // function restoreGlobalState() {
            //     isSimulating = false;
            //     $('#recoverBtn').trigger('click'); // 恢复按钮功能
            //     // 清理所有 TWEEN 动画
            //     TWEEN.removeAll();
            //     // 其他全局清理...
            // }

            function hidemodel(obj) {
                obj.children.forEach(function (item) {
                    new TWEEN.Tween(item.position)
                        .to({ x: 1000, y: 1000, z: 1000 }, 2000)
                        .easing(TWEEN.Easing.Quadratic.InOut)
                        .start();
                })

            }

            var highlightedModel = null;
            // 全局变量存储当前线条和看板引用
            var currentLine = null;
            var currentInfoPanel = null;
            function del(name) {
                if (name == '') {
                    return;
                }
                // ========== 清理旧线条和看板 ==========

                let newCarryDetail = carryAll.filter(item => item.peName == name);
                let peName = '';
                let carryDate = '';
                let blockNos = '';
                let l = '', h = '', w = '';
                if (newCarryDetail.length > 0) {
                    carryDate = newCarryDetail[0].carryDate || '';
                    peName = newCarryDetail[0].peName;
                    blockNos = newCarryDetail[0].blockNoList.length > 0 ? newCarryDetail[0].blockNoList.join(",") : "";
                    l = newCarryDetail[0].length || '';
                    h = newCarryDetail[0].height || '';
                    w = newCarryDetail[0].width || '';
                }

                if (currentLine) {
                    scene.remove(currentLine);
                    currentLine.geometry.dispose();
                    currentLine.material.dispose();
                    currentLine = null;
                }
                if (currentInfoPanel) {
                    // currentInfoPanel.classList.remove('visible');
                    // setTimeout(() => currentInfoPanel.remove(), 300);
                    currentInfoPanel.remove(); // 直接移除DOM元素
                    currentInfoPanel = null;
                }
                // ========== 生成新连接线 ==========
                const lineGeometry = new THREE.BufferGeometry();
                const points = [];
                const nameNodeLine = scene.getObjectByName(name);
                // 获取模型中心点（起点）
                const modelCenter = getCenterPoint(nameNodeLine);

                // 计算看板位置（终点：模型右侧偏移）
                const panelPosition = modelCenter.clone().add(new THREE.Vector3(5, 2, 0));
                points.push(modelCenter);
                points.push(panelPosition);
                lineGeometry.setFromPoints(points);

                const lineMaterial = new THREE.LineBasicMaterial({
                    color: 0xFFFFFF,
                    linewidth: 2
                });
                currentLine = new THREE.Line(lineGeometry, lineMaterial);
                scene.add(currentLine);

                // ========== 生成信息看板 ==========
                const infoPanel = document.createElement('div');
                infoPanel.className = 'info-panel';
                infoPanel.innerHTML = `
<!--                <div class="monitor-frame"> &lt;!&ndash; 外框 &ndash;&gt;-->
    <div class="screen-content"> <!-- 屏幕内容区域 -->
      <div class="header-line"> <!-- 标题行 -->
        <div class="close-btn" onclick="this.closest('.info-panel').remove()">×</div>
        <div class="title-line">搭载日：${carryDate}</div>
      </div>
      <div class="info-grid"> <!-- 信息表格布局 -->
        <div class="sub-line"><span class="lableName">总段：</span><span class="text-content">${peName}</span></div>
        <div class="size-line"><span class="lableName">分段：</span><span class="text-content">${blockNos}</span></div>
        <div class="size-line"><span class="lableName">长宽高：</span><span class="text-content" style="letter-spacing: -3px">${l} × ${w} × ${h}</span></div>
      </div>
    </div>
<!--  </div>-->
                     `;
                document.body.appendChild(infoPanel);
                // 显示动画
                setTimeout(() => infoPanel.classList.add('visible'), 10);
                currentInfoPanel = infoPanel;
                // ========== 看板位置同步更新（随模型移动） ==========
                const updatePanelPosition = () => {
                    if (!currentInfoPanel) return;

                    // 将 3D 坐标转换为屏幕坐标
                    const vector = panelPosition.clone().project(camera);
                    const x = (vector.x * 0.5 + 0.5) * window.innerWidth;
                    const y = (vector.y * -0.5 + 0.5) * window.innerHeight;

                    currentInfoPanel.style.left = `${x + 20}px`; // 偏移 20px 避免遮挡
                    currentInfoPanel.style.top = `${y}px`;
                };

                // 在动画循环中更新位置
                const animate = () => {
                    if (currentInfoPanel) {
                        updatePanelPosition();
                        requestAnimationFrame(animate);
                    }
                };
                animate();

                // ========== 关闭按钮事件 ==========
                infoPanel.querySelector('.close-btn').addEventListener('click', () => {
                    scene.remove(currentLine);
                    currentLine.geometry.dispose();
                    currentLine.material.dispose();
                    currentLine = null;
                    infoPanel.remove();
                    currentInfoPanel = null;
                });




                // 重置旧高亮模型的颜色
                if (highlightedModel) {
                    highlightedModel.material.color = new THREE.Color(0xffffff); // 默认颜色
                }
                const nameNode = scene.getObjectByName(name);
                nameNode.material.color = new THREE.Color(0xFF722B);
                highlightedModel = nameNode;
                if (oldintersects.length > 30) {
                    oldintersects = [];
                }
                let targetPositionOld;
                oldintersects.push(nameNode);
                if (oldintersects.length > 1) {
                    if (oldintersects[oldintersects.length - 2].material.color.b == 1) {
                        oldintersects[oldintersects.length - 2].material.color = new THREE.Color(0x34afd2);
                    }
                    targetPositionOld = getCenterPoint(oldintersects[oldintersects.length - 2]);
                }

                const obj = nameNode;

                $("#selectColor").change(function (e) {
                    const color = $("#selectColor").val();
                    nameNode.material.color = new THREE.Color(color);
                });
                $("#changeShow").change(function (e) {
                    const type = $("#changeShow").val();
                    if (type == 0) {
                        nameNode.visible = true;
                    } else {
                        nameNode.visible = false;
                    }
                });

                var box = new THREE.Box3();
                box.expandByObject(nameNode);
                const center = getCenterPoint(obj);

                // 计算目标相机位置
                const size = box.getSize(new THREE.Vector3());
                const maxDimension = Math.max(size.x, size.y, size.z);
                const fov = camera.fov * (Math.PI / 180);
                const distance = (maxDimension / 2) / Math.tan(fov / 2) * 3; // 增加3倍安全边距

                // 获取当前相机方向
                var direction = new THREE.Vector3();
                camera.getWorldDirection(direction);
                direction.negate();

                // 计算目标相机位置
                const targetCameraPosition = center.clone().add(direction.multiplyScalar(distance));

                // 计算目标控制器目标点
                const targetControlsTarget = center.clone();

                // 创建相机位置动画
                new TWEEN.Tween(camera.position)
                    .to(targetCameraPosition, 1000)
                    .easing(TWEEN.Easing.Quadratic.InOut)
                    .start();

                // 创建控制器目标点动画
                new TWEEN.Tween(controls.target)
                    .to(targetControlsTarget, 1000)
                    .easing(TWEEN.Easing.Quadratic.InOut)
                    .onUpdate(() => {
                        controls.update();
                    })
                    .start();

                // ========== 信息提示 ==========


            }

            init()
            $("#search").on('click', function (e) {
                const name = $("#nameOption").val()
                del(name)
            })



            // ========== 1. 全局状态管理 ==========
            let isTransitioning = false; // 切换锁定状态
            let currentColorTween = null; // 颜色动画控制

            // ========== 2. 修改颜色切换逻辑 ==========
            function animateColorChange(mesh, targetColor) {
                if (currentColorTween) {
                    currentColorTween.stop();
                    TWEEN.remove(currentColorTween);
                }

                currentColorTween = new TWEEN.Tween(mesh.material.color)
                    .to(targetColor, 500) // 500ms颜色渐变
                    .onUpdate(() => {
                        mesh.material.needsUpdate = true; // 强制材质更新
                    })
                    .start();
            }



            $view.find(".nodeItem").on("click", function (e) {

                // 同时执行三个动画,还原
                Promise.all([
                    // 模型整体位置还原（从当前位置到(0,0,-10)）
                    new Promise(resolve => {
                        new TWEEN.Tween(modul.position)
                            .to({ x: 0, y: 0, z: 0 }, 2000)
                            .easing(TWEEN.Easing.Quadratic.InOut)
                            .onComplete(resolve)
                            .start();
                    }),


                    // 相机位置和视角还原
                    new Promise(resolve => {
                        // 相机位置到(0, 10, 50)
                        new TWEEN.Tween(camera.position)
                            .to({ x: 0, y: 10, z: 50 }, 2000)
                            .easing(TWEEN.Easing.Quadratic.InOut)
                            .start();

                        // 控制中心点回到模型中心(0,0,0)
                        new TWEEN.Tween(controls.target)
                            .to({ x: 0, y: 0, z: 0 }, 2000)
                            .easing(TWEEN.Easing.Quadratic.InOut)
                            .onUpdate(() => controls.update())
                            .onComplete(() => {
                                // controls.reset(); // 重置轨道控制器
                                resolve();
                            })
                            .start();
                    })
                ]).then(() => {
                    controls.enabled = true; // 恢复控制器
                    controls.minDistance = 0;
                    controls.update();
                });
                //取消动画
                TWEEN.removeAll();
                //清空数据
                $('#tipsBox').hide().empty();
                $("#blockDetail").hide().empty();
                // $('#recoverBtn').trigger('click');
                let clickDate = $(this).find(".carryDate").attr("data-carrydate");
                $(this).find('.click').show();
                //同级隐藏
                $(this).find('.click').siblings('img').not(this).hide()
                //其他的非点击的显示
                $(this).siblings('div').not(this).find(".click").hide();
                $(this).siblings('div').not(this).find(".noClick").show();
                let newCarryDat = [];
                febs.getSyn(ctx + "dockship/carryDetail", { shipId: shipId, date: clickDate }, function (data) {
                    $.each(data.data.carryDetail, function (i, val) {
                        newCarryDat.push({ peName: val.peName, carryDate: val.carryDate })
                    })
                    initModel(newCarryDat, modelScalar, modelURL)
                })
            })




            $("#splitpy").on('click', function (e) {
                explodeModel(modul, 2000, 1)

            })
            $("#splitbz").on('click', function (e) {
                explodeModel(modul, 2000, 2)
            })
            $("#merge").on('click', function (e) {
                mergrModel(modul)
            })
            $("#dzmn").on('click', function (e) {
                layer.closeAll();
                dzmn(modul)
            })
            $("#hidemodel").on('click', function (e) {
                console.log(1)
                hidemodel(modul)
            })

            $('#showUp').click(function () {
                $(this).text() == '旋转开启' ? $(this).text('旋转关闭') : $(this).text('旋转开启')

                controls.autoRotate = !controls.autoRotate
                controls.update();
            })

            // 还原初始状态
            $('#recoverBtn').click(function () {
                $("#showUp").text('旋转关闭');
                $('.close-btn').click()
                controls.autoRotate = false;

                isSimulating = false;
                controls.enabled = false; // 禁用控制器
                TWEEN.removeAll();

                // 同时执行三个动画
                Promise.all([
                    // 模型整体位置还原（从当前位置到(0,0,-10)）
                    new Promise(resolve => {
                        new TWEEN.Tween(modul.position)
                            .to({ x: 0, y: 0, z: 0 }, 2000)
                            .easing(TWEEN.Easing.Quadratic.InOut)
                            .onComplete(resolve)
                            .start();
                    }),

                    // 子组件位置还原
                    new Promise(resolve => {
                        mergrModel(modul);
                        setTimeout(resolve, 2000); // 保持与动画同步
                    }),

                    // 相机位置和视角还原
                    new Promise(resolve => {
                        // 相机位置到(0, 10, 50)
                        new TWEEN.Tween(camera.position)
                            .to({ x: 0, y: 10, z: 50 }, 2000)
                            .easing(TWEEN.Easing.Quadratic.InOut)
                            .start();

                        // 控制中心点回到模型中心(0,0,0)
                        new TWEEN.Tween(controls.target)
                            .to({ x: 0, y: 0, z: 0 }, 2000)
                            .easing(TWEEN.Easing.Quadratic.InOut)
                            .onUpdate(() => controls.update())
                            .onComplete(() => {
                                // controls.reset(); // 重置轨道控制器
                                resolve();
                            })
                            .start();
                    })
                ]).then(() => {
                    controls.enabled = true; // 恢复控制器
                    controls.minDistance = 0;
                    controls.update();
                    initModel(carryData, modelScalar, modelURL);
                });

                $('#tipsBox').hide().empty();
                $("#blockDetail").hide().empty();
            });
            $('#modelsChange').click(function () {
                if (chooseModels.length == 0) {
                    changeBox.style.display = 'none'
                    return
                }
                changeBox.innerHTML = 'nigenshan'
                const info = `
                        <p style="margin-top:12px;margin-bottom:15px">数量:
                            <text>${chooseModels.length}<text>
                        <p>
                        <p>颜色:
                        <select id="selectColors" style="width: 100px;height: 30px;margin-bottom:15px">
                            <option value="0xffffff">未设置</option>
                            <option value="rgb(255, 114, 43)">红色</option>
                            <option value="rgb(85, 255, 255)">蓝色</option>
                            <option value="rgb(255, 255, 0)">黄色</option>
                            <option value="rgb(0, 0, 0)">黑色</option>
                        </select>

                        </p>
                        <p>状态:
                        <select id="changeShows" style="width: 100px;height: 30px;margin-bottom:15px">
                            <option value="">请选择</option>
                            <option value="0">显示</option>
                            <option value="1">隐藏</option>
                        </select>
                        </p>
                        `
                changeBox.innerHTML = info;
                changeBox.style.display = 'block'
                changeBox.style.left = '2%'
                changeBox.style.top = '10%'
                $("#selectColors").change(function (e) {
                    const color = $("#selectColors").val()
                    chooseModels.forEach((item) => {
                        item.material.color = new THREE.Color(color);
                    })
                })
                $("#changeShows").change(function (e) {
                    console.log(2)
                    const type = $("#changeShows").val()
                    if (type == 0) {
                        chooseModels.forEach((item) => {
                            item.visible = true
                        })
                    } else {
                        chooseModels.forEach((item) => {
                            item.visible = false
                        })
                    }
                })
            })

            // ========== 排序功能实现 ==========
            let sortedCarryAll = [...carryAll]; // 创建副本用于排序
            let isDragging = false;

            // 切换排序面板显示/隐藏
            $('#toggleSortBtn').on('click', function () {
                const panel = $('#sortPanel');
                const btn = $(this);

                if (panel.is(':visible')) {
                    // 收起面板
                    panel.hide();
                    btn.removeClass('expanded').text('◀');
                } else {
                    // 展开面板
                    panel.show();
                    btn.addClass('expanded').text('▶');
                    renderSortableList();
                }
            });

            // 渲染可排序列表
            function renderSortableList() {
                const container = $('#sortableList');
                container.empty();

                sortedCarryAll.forEach((item, index) => {
                    const blockNoString = item.blockNoList.join(', ');
                    const sortItem = $(`
                        <div class="sort-item" data-index="${index}" draggable="true">
                            <div class="sort-item-header">
                                <span class="sort-item-name">${item.peName}</span>
                                <span class="sort-item-date">${item.carryDate}</span>
                            </div>
                            <div class="sort-item-details">
                                尺寸: ${item.length} × ${item.width} × ${item.height}<br>
                                分段: ${blockNoString}
                            </div>
                        </div>
                    `);
                    container.append(sortItem);
                });

                // 添加拖拽事件
                setupDragAndDrop();
            }

            // 设置拖拽功能
            function setupDragAndDrop() {
                const sortItems = document.querySelectorAll('.sort-item');

                sortItems.forEach(item => {
                    item.addEventListener('dragstart', handleDragStart);
                    item.addEventListener('dragover', handleDragOver);
                    item.addEventListener('drop', handleDrop);
                    item.addEventListener('dragend', handleDragEnd);
                });
            }

            let draggedElement = null;

            function handleDragStart(e) {
                draggedElement = this;
                this.classList.add('dragging');
                e.dataTransfer.effectAllowed = 'move';
                e.dataTransfer.setData('text/html', this.outerHTML);

                // 获取拖动项目的索引和对应的部件名称
                const draggedIndex = parseInt(this.dataset.index);
                const draggedItem = sortedCarryAll[draggedIndex];

                // 在3D模型中高亮显示对应的部件
                highlightModelPart(draggedItem.peName);
            }

            function handleDragOver(e) {
                if (e.preventDefault) {
                    e.preventDefault();
                }
                e.dataTransfer.dropEffect = 'move';
                return false;
            }

            function handleDrop(e) {
                if (e.stopPropagation) {
                    e.stopPropagation();
                }

                if (draggedElement !== this) {
                    const draggedIndex = parseInt(draggedElement.dataset.index);
                    const targetIndex = parseInt(this.dataset.index);

                    // 重新排列数组
                    const draggedItem = sortedCarryAll.splice(draggedIndex, 1)[0];
                    sortedCarryAll.splice(targetIndex, 0, draggedItem);

                    // 重新渲染列表
                    renderSortableList();
                }
                return false;
            }

            function handleDragEnd(e) {
                this.classList.remove('dragging');
                draggedElement = null;

                // 拖动结束后，恢复所有部件的原始颜色
                resetModelHighlight();
            }

            // 应用排序
            $('#applySortBtn').on('click', function () {
                const sortBy = $('#sortBy').val();
                const sortOrder = $('#sortOrder').val();

                if (sortBy === 'manual') {
                    // 手动排序模式，不做自动排序
                    return;
                }

                sortedCarryAll.sort((a, b) => {
                    let valueA, valueB;

                    switch (sortBy) {
                        case 'carryDate':
                            valueA = a.carryDate || '';
                            valueB = b.carryDate || '';
                            break;
                        case 'peName':
                            valueA = a.peName || '';
                            valueB = b.peName || '';
                            break;
                        case 'length':
                            valueA = parseFloat(a.length) || 0;
                            valueB = parseFloat(b.length) || 0;
                            break;
                        case 'width':
                            valueA = parseFloat(a.width) || 0;
                            valueB = parseFloat(b.width) || 0;
                            break;
                        case 'height':
                            valueA = parseFloat(a.height) || 0;
                            valueB = parseFloat(b.height) || 0;
                            break;
                        default:
                            return 0;
                    }
                    if (typeof valueA === 'string') {
                        const comparison = valueA.localeCompare(valueB);
                        return sortOrder === 'asc' ? comparison : -comparison;
                    } else {
                        const comparison = valueA - valueB;
                        return sortOrder === 'asc' ? comparison : -comparison;
                    }
                });

                renderSortableList();
            });

            // 重置排序
            $('#resetSortBtn').on('click', function () {
                sortedCarryAll = [...carryAll]; // 重置为原始顺序
                renderSortableList();
            });

            // 保存新数组
            $('#saveSortBtn').on('click', function () {
                // 创建新的排序后的数组
                const newSortedArray = [...sortedCarryAll];

                // 在控制台输出新数组
                console.log('新的排序数组:', newSortedArray);

                // 显示保存成功的提示
                layer.msg('新数组已保存到控制台，请查看浏览器开发者工具的Console面板', {
                    icon: 1,
                    time: 3000
                });

                // 可以在这里添加保存到服务器的逻辑
                // 例如：
                // febs.post(ctx + "dockship/saveSortedArray", {
                //     shipId: shipId,
                //     sortedArray: newSortedArray
                // }, function(data) {
                //     layer.msg('保存成功', { icon: 1 });
                // });

            });

            // 监听排序方式变化
            $('#sortBy').on('change', function () {
                const sortBy = $(this).val();
                if (sortBy === 'manual') {
                    $('#sortOrder').prop('disabled', true);
                    $('#applySortBtn').text('手动排序');
                } else {
                    $('#sortOrder').prop('disabled', false);
                    $('#applySortBtn').text('应用排序');
                }
            });

            // ========== 3D模型高亮功能 ==========
            let originalColors = new Map(); // 存储原始颜色

            // 高亮显示指定部件
            function highlightModelPart(peName) {
                if (!modul) return;

                // 先恢复所有部件的原始颜色
                resetModelHighlight();

                // 遍历模型中的所有网格对象
                modul.traverse((child) => {
                    if (child.isMesh && child.name === peName) {
                        // 保存原始颜色
                        if (!originalColors.has(child.name)) {
                            originalColors.set(child.name, child.material.color.clone());
                        }

                        // 设置高亮颜色（亮黄色）
                        child.material.color.setHex(0xFFFF00);

                        // 增加透明度以突出显示
                        child.material.opacity = 0.9;

                        console.log(`高亮显示部件: ${peName}`);
                    }
                });
            }

            // 恢复所有部件的原始颜色
            function resetModelHighlight() {
                if (!modul) return;

                modul.traverse((child) => {
                    if (child.isMesh && originalColors.has(child.name)) {
                        // 恢复原始颜色
                        const originalColor = originalColors.get(child.name);
                        child.material.color.copy(originalColor);

                        // 恢复原始透明度
                        child.material.opacity = 0.6;
                    }
                });
            }

            // 添加鼠标悬停效果到排序项目
            function addHoverEffectToSortItems() {
                $(document).on('mouseenter', '.sort-item', function () {
                    if (!$(this).hasClass('dragging')) {
                        const index = parseInt($(this).data('index'));
                        const item = sortedCarryAll[index];
                        if (item) {
                            highlightModelPart(item.peName);
                        }
                    }
                });

                $(document).on('mouseleave', '.sort-item', function () {
                    if (!$(this).hasClass('dragging')) {
                        resetModelHighlight();
                    }
                });
            }

            // 初始化悬停效果
            addHoverEffectToSortItems();

        })
    </script>

</body>

</html>