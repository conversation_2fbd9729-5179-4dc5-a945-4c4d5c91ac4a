<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns="http://www.w3.org/1999/html">

<head>
    <th:block th:include="include::header('托盘清册管理')" />
    <style type="text/css">
        /*.layui-table-cell .layui-form-checkbox[lay-skin=primary] {*/
        /*    top: -8px;*/
        /*    padding: 5px;*/
        /*}*/

        /*.layui-table-cell {*/
        /*    height: 40px !important;*/
        /*}*/

        #trayPlanReportView .edited {
            background-color: #74b9ff !important;
        }

        .layui-table-checked.layui-table-hover {
            background-color: #74b9ff !important;
        }

        .layui-table tbody tr:hover,
        .layui-table-hover {
            background-color: #F8F8F8;
        }

        .username {
            background-color: rgb(93, 177, 255);
            position: relative;
            border-radius: 3px;
            color: #FFF;
            text-align: center;
            padding-left: 10px;
            padding-right: 10px;
            margin-right: 2px;
            margin-left: 2px;
            display: inline-table;
        }

        .layui-badge-rim {
            height: 21px;
            line-height: 19px;
        }

        #searchForm .layui-form-label {
            width: 80px !important;
        }

        #searchForm .layui-btn {
            margin: 0 -3px
        }

        #searchForm .layui-inline {
            margin: 5px;
        }

        /* 为隔行变色定义CSS */
        .layui-table tbody tr:nth-child(odd) {
            background-color: #ffffff;
            /* 奇数行背景色 */
        }

        .layui-table tbody tr:nth-child(even) {
            background-color: #f2f2f2;
            /* 偶数行背景色 */
        }

        .layui-table-header .layui-table {
            margin-top: 5px;
        }

        .layui-table-header {
            height: 36px;
        }

        .shipNoSelectText {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 180px;
            position: absolute;
            right: 10px;
        }

        .topTab {
            height: 38px;
            width: 100%;
            background-color: white;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 0 20px;
            /*border-bottom: 1px solid #e6e6e6;*/
        }

        .mainTabContainer {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 0 0 auto;
        }

        .categoryTab {
            height: 38px;
            width: 100%;
            background-color: white;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding: 0 20px;
            border-bottom: 1px solid #e6e6e6;
            /*box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);*/
        }

        .categoryContainer {
            display: flex;
            background: white;
            /*border-radius: 6px;*/
            padding: 2px;
            /*box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);*/
            gap: 6px;
            justify-content: flex-start;
        }

        .categoryGroup {
            display: flex;
            align-items: center;
            gap: 3px;
        }

        /*.categoryGroup::after {*/
        /*    content: '';*/
        /*    width: 1px;*/
        /*    height: 20px;*/
        /*    background: #e6e6e6;*/
        /*    margin: 0 6px;*/
        /*}*/

        .categoryGroup:last-child::after {
            display: none;
        }

        .categoryOption {
            padding: 4px 12px;
            margin: 0 1px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            color: #666;
            background: transparent;
            transition: all 0.3s ease;
            position: relative;
            white-space: nowrap;
            opacity: 0.6;
            height: 22px;
            display: flex;
            align-items: center;
        }

        .categoryOption:hover {
            background: #f0f7ff;
            color: #1890ff;
            opacity: 1;
        }

        .categoryOption.categorySelect {
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            color: white;
            box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
            opacity: 1;
        }

        .categoryOption.categorySelect::after {
            content: '';
            position: absolute;
            bottom: -6px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid #1890ff;
        }

        .categoryOption.active-group {
            opacity: 1;
        }

        .categoryOption.inactive-group {
            opacity: 0.3;
            pointer-events: none;
            display: none;
        }

        .mainContent {
            width: 100%;
            height: calc(100% - 168px);
            background-color: #fff;
            display: flex;
            flex-wrap: nowrap;
        }

        .topOption {
            padding: 4px 12px;
            font-size: 14px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            background: #f0f2f5;
            border-radius: 4px;
            margin: 0 2px;
            transition: all 0.3s ease;
            white-space: nowrap;
            height: 22px;
        }

        .topOption:hover {
            background: #e6f7ff;
            color: #1890ff;
        }

        .topSelect {
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            color: white;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }

        .tableContainer {
            width: 100%;
            height: calc(100% - 40px);
            display: flex;
            align-content: center;
            justify-content: center;
            padding-top: 20px;
        }

        .echartsContainer {
            width: 100%;
            height: 50%;
            display: flex;
            align-content: center;
            justify-content: center;
            padding-top: 20px;
        }

        .totalPage {
            display: none;
        }
    </style>
    <link rel="stylesheet" th:href="@{/febs/views/css/commonZs.css}" media="all">
    <style>
        #searchForm .layui-form-label {
            padding: 6px 2px !important;
        }

        #shipNoSelect xm-select>.xm-body {
            width: 350px;
        }

        #searchForm .layui-form-item .layui-input-inline {
            width: 240px !important
        }

        #searchForm .layui-input {
            width: 242px !important
        }

        .layui-table-page {
            bottom: 0px;
        }


        .layui-table-cell {
            height: 30px;
            line-height: 30px;
            padding: 0px;
        }


        .layui-table tbody tr {
            height: 30px;
        }

        .layui-table td {
            padding: 0px 0px;
        }

        .layui-table-view {
            padding-left: 15px;
        }
    </style>
</head>

<body>
    <div class="layui-fluid layui-anim febs-anim page-body" id="trayPlanReportView" lay-title="托盘计划完成率报表">
        <form class="layui-form search-form" id="searchForm">
            <div class="jhg-body-search">
                <div class="layui-form-item">

                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm">船号:</label>
                        <div class="layui-input-inline" style="width: 300px;">
                            <div id="shipNoSelect"></div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm">BOM种类:</label>
                        <div class="layui-input-inline" style="width: 300px;">
                            <div id="BOMType"></div>
                        </div>
                    </div>
                    <div class="layui-inline dataSelect">
                        <label style="width: 80px;padding: 5px 0;" class="layui-form-label">作业日期：</label>
                        <div class="layui-input-inline" id="timeRange" style="display: flex;width: 300px">
                            <input type="text" id="sTime" name="sTime" autocomplete="off" class="layui-input"
                                style="text-align: center;width: 106px !important;" placeholder="开始日期">
                            <div style="font-size: 20px;line-height: 30px;margin: 0 10px">~</div>
                            <input type="text" id="eTime" name="eTime" autocomplete="off" class="layui-input"
                                style="text-align: center;width: 106px !important;" placeholder="结束日期">
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm">设计专业:</label>
                        <div class="layui-input-inline">
                            <div id="majorSelect"></div>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm">作业人员:</label>
                        <div class="layui-input-inline">
                            <div id="ssWorkUserSelect"></div>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label layui-form-label-sm">船型区域:</label>
                        <div class="layui-input-inline">
                            <div id="shipArea"></div>
                        </div>
                    </div>

                    <div class="layui-inline rawMaterialNo">
                        <label class="layui-form-label layui-form-label-sm">物资编码:</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" id="rawMaterialNo"></input>
                        </div>
                    </div>


                    <div class="layui-inline" style="margin-left: 15px">
                        <div id="query" class="layui-btn searchBlue layui-btn-sm">
                            <em class="layui-icon">&#xe615;</em>检索
                        </div>
                        <div id="queryOther" class="layui-hide">检索(假)</div>
                    </div>
                    <div class="layui-inline">
                        <div id="exportsAll" class="layui-btn layui-btn-small blueBtm">
                            <em class="layui-icon layui-icon-export"></em> 导出
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <!-- 第一行：主导航 -->
        <div class="topTab">
            <div class="mainTabContainer">
                <div class="topOption topSelect" data-name="plan">计划完成率报表</div>
                <div class="topOption" data-name="total">物量汇总统计</div>
            </div>
        </div>

        <!-- 第二行：类目导航 -->
        <div class="categoryTab">
            <div class="categoryContainer">
                <!-- 计划完成率报表类目组 -->
                <div class="categoryGroup" data-main-type="plan">
                    <div class="categoryOption categorySelect showNum1 active-group" data-type="0" data-main="plan">
                        托盘未登记物资</div>
                    <div class="categoryOption showNum2 active-group" data-type="1" data-main="plan">托盘未确认</div>
                    <div class="categoryOption showNum3 active-group" data-type="2" data-main="plan">托盘未关联</div>
                </div>

                <!-- 物量汇总统计类目组 -->
                <div class="categoryGroup" data-main-type="total">
                    <div class="categoryOption showNum4 inactive-group" data-type="0" data-main="total">未使用物量</div>
                    <div class="categoryOption showNum5 inactive-group" data-type="1" data-main="total">未关联物量</div>
                </div>
            </div>
        </div>

        <div class="mainContent planPage">
            <div class="tableContainer">
                <table id="mainTable" lay-filter="mainTable"></table>
            </div>
        </div>

        <div class="mainContent totalPage" style="display: none;">
            <div class="tableContainer">
                <table id="mainTable1" lay-filter="mainTable1"></table>
            </div>
        </div>

    </div>
    <th:block th:include="include::foot" />
    <script data-th-inline="none" type="text/javascript">
        layui.config({
            base: ctx + 'febs/'
        }).extend({
            febs: 'lay/modules/febs',
            validate: 'lay/modules/validate',
            formSelects: 'lay/extends/formSelects-v4.min',
            jqueryui: 'lay/extends/jquery-ui.min',
            echarts: 'lay/extends/echarts.min',
            commonJS: 'lay/extends/common'
        }).use(['tree', 'jquery', 'validate', 'table', 'laypage', 'form', 'laydate', 'febs', 'layer', 'commonJS', 'dropdown', 'rpcJs'], function () {
            var $ = layui.jquery,
                $view = $('#trayPlanReportView'),
                febs = layui.febs,
                form = layui.form,
                table = layui.table,
                laydate = layui.laydate,
                rpcJs = layui.rpcJs,
                shipNoSelect,
                majorSelect,
                ssWorkUserSelect,
                areaSelect,
                bomSelect,
                cacheFlag = 0,
                typeName = '计划完成率报表',
                dataType = 0,
                allShips = [];

            laydate.render({
                elem: '#timeRange',
                range: ['#sTime', '#eTime'],
                type: 'date',
                isInitValue: true,
                value: []
            });
            form.render();
            $(".rawMaterialNo").hide();
            setShipNo();
            initDict();
            setSsWorkUser(null)
            // initTable(0);
            let editFlg = currentUser.permissionSet.indexOf('drawPmlManger:edit') == -1 ? false : true


            // 二级类目导航点击事件
            $('.categoryOption').click(function () {
                // 检查是否是当前激活组的选项
                if ($(this).hasClass('inactive-group')) {
                    return false;
                }

                // 只在当前激活的类目组中切换选中状态
                var mainType = $(this).data('main');
                $('.categoryOption[data-main="' + mainType + '"]').removeClass('categorySelect');
                $(this).addClass('categorySelect');

                dataType = $(this).data('type')
                if (mainType == 'plan') {
                    if (shipNoSelect.getValue('value').length === 0) {
                        febs.alert.warn('请先选择船号');
                        return false
                    }
                    if (bomSelect.getValue('value').length === 0) {
                        febs.alert.warn('请先选择bom种类');
                        return false
                    }
                    initTable(dataType, shipNoSelect.getValue('value'), bomSelect.getValue('value'), majorSelect.getValue('value'), ssWorkUserSelect.getValue('value'), areaSelect.getValue('value'), $('#sTime').val(), $('#eTime').val())
                } else {
                    if (shipNoSelect.getValue('value').length === 0) {
                        febs.alert.warn('请先选择船号');
                        return false
                    }
                    if (bomSelect.getValue('value').length === 0) {
                        febs.alert.warn('请先选择bom种类');
                        return false
                    }
                    initTable1(dataType, shipNoSelect.getValue('value'), bomSelect.getValue('value'), majorSelect.getValue('value'), ssWorkUserSelect.getValue('value'), areaSelect.getValue('value'), $('#sTime').val(), $('#eTime').val(), $("#rawMaterialNo").val())
                }
            });


            $('.topOption').click(function () {
                $('.topOption').removeClass('topSelect')
                $(this).addClass('topSelect');
                var txt = $(this).text()
                var dataName = $(this).data('name')
                typeName = txt
                $(".rawMaterialNo").val("");

                // 重置所有类目选中状态
                $('.categoryOption').removeClass('categorySelect')

                if (dataName == 'plan') {
                    $('.planPage').show()
                    $('.totalPage').hide()

                    // 激活计划完成率报表类目组，禁用物量汇总类目组
                    $('.categoryOption[data-main="plan"]').removeClass('inactive-group').addClass('active-group')
                    $('.categoryOption[data-main="total"]').removeClass('active-group').addClass('inactive-group')

                    // 设置默认选中第一个计划类目
                    $('.showNum1').addClass('categorySelect')
                    dataType = 0

                    $(".rawMaterialNo").hide();
                    if (shipNoSelect.getValue('value').length === 0) {
                        febs.alert.warn('请先选择船号');
                        return false
                    }
                    if (bomSelect.getValue('value').length === 0) {
                        febs.alert.warn('请先选择bom种类');
                        return false
                    }
                    initTable(0, shipNoSelect.getValue('value'), bomSelect.getValue('value'), majorSelect.getValue('value'), ssWorkUserSelect.getValue('value'), areaSelect.getValue('value'), $('#sTime').val(), $('#eTime').val())
                    getLeftNum(1, shipNoSelect.getValue('value'), bomSelect.getValue('value'), majorSelect.getValue('value'), ssWorkUserSelect.getValue('value'), areaSelect.getValue('value'), $('#sTime').val(), $('#eTime').val())

                } else {
                    $('.planPage').hide()
                    $('.totalPage').show()

                    // 激活物量汇总类目组，禁用计划完成率报表类目组
                    $('.categoryOption[data-main="total"]').removeClass('inactive-group').addClass('active-group')
                    $('.categoryOption[data-main="plan"]').removeClass('active-group').addClass('inactive-group')

                    // 设置默认选中第一个物量类目
                    $('.showNum4').addClass('categorySelect')
                    dataType = 0

                    //物资
                    $(".rawMaterialNo").show();
                    if (shipNoSelect.getValue('value').length === 0) {
                        febs.alert.warn('请先选择船号');
                        return false
                    }
                    if (bomSelect.getValue('value').length === 0) {
                        febs.alert.warn('请先选择bom种类');
                        return false
                    }
                    initTable1(0, shipNoSelect.getValue('value'), bomSelect.getValue('value'), majorSelect.getValue('value'), ssWorkUserSelect.getValue('value'), areaSelect.getValue('value'), $('#sTime').val(), $('#eTime').val(), $("#rawMaterialNo").val())
                    getLeftNum(2, shipNoSelect.getValue('value'), bomSelect.getValue('value'), majorSelect.getValue('value'), ssWorkUserSelect.getValue('value'), areaSelect.getValue('value'), $('#sTime').val(), $('#eTime').val(), $("#rawMaterialNo").val())
                }
            });


            function getLeftNum(planFlg, shipVal, bomVal, majorVal, workerVal, areaVal, sTime, eTime, rawMaterialNo) {
                let param = {
                    shipIds: shipVal,
                    planFlg: planFlg,
                    pdtCodeNames: bomVal,
                    workUserIdList: workerVal,
                    pptdIds: majorVal,
                    strIds: areaVal,
                    planStartDate: sTime,
                    planFinishDate: eTime,
                    rawMaterialNo: rawMaterialNo
                }
                febs.get(ctx + 'bom/bomTrayBasicInfo/tabAmount', param, function (e) {
                    if (e.code == 200) {
                        if (planFlg == 1) {
                            $('.showNum1').html('托盘未登记物资(' + e.data['未登记'] + ')')
                            $('.showNum2').html('托盘未确认(' + e.data['未确认'] + ')')
                            $('.showNum3').html('托盘未关联(' + e.data['未关联'] + ')')
                        } else {
                            $('.showNum4').html('未使用物量(' + e.data['未使用'] + ')')
                            $('.showNum5').html('未关联物量(' + e.data['未关联'] + ')')
                        }
                    }
                })
            }

            function initTable(type, shipVal, bomVal, majorVal, workerVal, areaVal, sTime, eTime) {
                var cols = [[
                    { field: 'designDrawNo', title: '图纸编号', minWidth: 110, align: 'center' }
                    , { field: 'designDrawDesc', title: '图纸描述', minWidth: 280, align: 'center' }
                    , { field: 'trueName', title: '作业人员', minWidth: 110, align: 'center' }
                    , { field: 'produceDate', title: '生产需求日', minWidth: 110, align: 'center' }
                    , { field: 'trayNo', title: '托盘编号', minWidth: 140, align: 'center' }
                    , { field: 'trayName', title: '托盘名称', minWidth: 110, align: 'center' }
                    , { field: 'trayTypeStr', title: '托盘类型', minWidth: 110, align: 'center' }
                    , { field: 'registerStatusStr', title: '物资登记', minWidth: 90, align: 'center' }
                    , { field: 'planFinishDate', title: '计划完成日', minWidth: 110, align: 'center' }
                    , { field: 'pmlConfirmDate', title: '托盘确认日', minWidth: 110, align: 'center' }
                    , { field: 'pdwCodeName', title: '作业区分', minWidth: 100, align: 'center' }
                ]];
                table.render({
                    elem: '#mainTable',
                    url: ctx + "bom/bomTrayBasicInfo/report",
                    method: 'get',
                    where: {
                        shipIds: shipVal,
                        reportFlg: type,
                        pdtCodeNames: bomVal,
                        workUserIdList: workerVal,
                        pptdIds: majorVal,
                        strIds: areaVal,
                        planStartDate: sTime,
                        planFinishDate: eTime,
                    },
                    skin: 'row',
                    cols: cols,
                    page: true, // 开启分页
                    page: { curr: 1 },
                    limit: 50,  // 默认每页显示50条
                    limits: [10, 20, 50, 100, 200, 500],
                    // height: commonJS.calcTabelHeight(),
                    height: 500,
                    request: {
                        pageName: 'pageNum',
                        limitName: 'pageSize'
                    },
                    response: {
                        statusCode: 200,
                    },
                    parseData: function (res) {
                        var layuiTableExpectedCode = res.code;
                        var totalCount = res.data.total;
                        return {
                            "code": layuiTableExpectedCode,
                            "count": totalCount,
                            "data": res.data.rows
                        };
                    },

                });
            }


            function initTable1(type, shipVal, bomVal, majorVal, workerVal, areaVal, sTime, eTime, rawMaterialNo) {
                table.render({
                    elem: '#mainTable1',
                    url: ctx + "bom/bomTrayTotal/report",
                    method: 'get',
                    where: {
                        shipIds: shipVal,
                        reportFlg: type,
                        pdtCodeNames: bomVal,
                        workUserIdList: workerVal,
                        pptdIds: majorVal,
                        strIds: areaVal,
                        planStartDate: sTime,
                        planFinishDate: eTime,
                        rawMaterialNo: rawMaterialNo
                    },
                    cols: [
                        [
                            { fixed: 'left', type: 'numbers', title: '序号', width: 50 },
                            {
                                fixed: 'left',
                                field: 'rawMaterialId',
                                title: '物资Id',
                                align: 'center',
                                minWidth: 50,
                                hide: true
                            },
                            {
                                fixed: 'left',
                                field: 'rawMaterialNo',
                                title: '物资编码',
                                align: 'center',
                                minWidth: 150,
                                templet(d) {
                                    return '<div style="text-align: left">' + (d.rawMaterialNo == null ? '' : d.rawMaterialNo) + '</div>'
                                }
                            },
                            {
                                fixed: 'left',
                                field: 'rawDesc',
                                title: '外购件物资描述',
                                minWidth: 200,
                                align: 'center',
                                templet(d) {
                                    return '<div style="text-align: left">' + (d.rawDesc == null ? '' : d.rawDesc) + '</div>'
                                }
                            },
                            { field: 'dataTypeStr', title: '物料类型', minWidth: 90, align: 'center', },
                            { field: 'rawUnit', title: '外购件单位', minWidth: 100, align: 'center' },
                            { field: 'rawUnitWeight', title: '外购件单位重量', minWidth: 120, align: 'center' },

                            { field: 'rawAmount', title: '外购件数量', minWidth: 100, align: 'center' },
                            {
                                field: 'rawLeftAmount',
                                title: '未关联数量',
                                minWidth: 100,
                                align: 'center',
                            },
                            { field: 'weight', title: '总重量', minWidth: 90, align: 'center' },

                        ]
                    ],
                    page: true, // 开启分页
                    page: { curr: 1 },
                    limit: 50,  // 默认每页显示50条
                    limits: [10, 20, 50, 100, 200, 500],
                    // height: commonJS.calcTabelHeight(),
                    height: 500,
                    request: {
                        pageName: 'pageNum',
                        limitName: 'pageSize'
                    },
                    response: {
                        statusCode: 200,
                    },
                    parseData: function (res) {
                        var layuiTableExpectedCode = res.code;
                        var totalCount = res.data.total;
                        return {
                            "code": layuiTableExpectedCode,
                            "count": totalCount,
                            "data": res.data.rows
                        };
                    },
                });
            }

            //获取材料使用类型
            febs.get(ctx + 'por/porShipBomMaterialDict/all', {}, function (e) {
                if (e.code == 200) {
                    useType = e.data
                }
            })

            setBomType()

            function setShipArea(typeIds) {
                febs.get(ctx + 'basic/shipTypeArea/shipTypeRegionDictByTypeIds', { typeIds: typeIds }, function (e) {
                    if (String(e.code) === '200') {
                        let arr = []
                        $.each(e.data, function (i, v) {
                            arr.push({
                                name: v.strCodeName,
                                value: v.strId,
                                showName: v.strCode
                            })
                        });
                        areaSelect = xmSelect.render({
                            el: '#shipArea',
                            data: arr,
                            filterable: true,
                            toolbar: {
                                show: true,
                                list: ['ALL', 'CLEAR']

                            },
                            tips: arr.length === 0 ? "请先选择船号" : "请选择",
                            template({ item }) {
                                return `${item.name}<span class="shipNoText" title="${item.showName ? item.showName : ''}">${item.showName ? item.showName : ''}</span>`;
                            },
                        })
                    }
                })
            }

            xmSelect.render({
                el: '#shipArea',
                radio: false,
                clickClose: false,
                data: []
            });

            function setBomType() {
                bomSelect = xmSelect.render({
                    el: '#BOMType',
                    radio: false,
                    clickClose: false,
                    data: [
                        { name: '制作图', value: '制作图' },
                        { name: '安装图', value: '安装图' }
                    ]
                });
            }

            //船号选择
            function setShipNo() {
                let arr = [];
                let resp = rpcJs.getShipDataList();
                if (resp.code == 200) {
                    allShips = resp.data;
                    $.each(resp.data, function (i, v) {
                        arr.push({
                            name: v.shipNo,
                            value: v.shipId,
                            showname: v.showName,
                            typeId: v.typeId
                        })
                    })
                }
                shipNoSelect = xmSelect.render({
                    el: '#shipNoSelect',
                    data: arr,
                    filterable: true,
                    toolbar: {
                        show: true
                    },
                    template({ item }) {
                        return item.name + '<span class="shipNoSelectText" title="' + item.showname + '">' + item.showname + '</span>'
                    },
                    model: {
                        label: {
                            block: {
                                template(item, sels) {
                                    return item.name + '(' + item.showname + ')'
                                }
                            }
                        }
                    },
                    radio: false,
                    clickClose: false,
                    on: function (data) {
                        let typeIds = '';
                        if (data && data.length !== 0 && data.arr && data.arr.length !== 0) {
                            typeIds = data.arr.map(item => item.typeId).join(',');
                        }
                        setShipArea(typeIds)
                        let isAdd = data.isAdd;
                        let selectedItem = data.arr[0];
                        if (isAdd) {
                            cacheFlag = 0;
                        } else {
                            cacheFlag = 0;
                        }
                    }
                })
            }

            //初始化字典数据
            function initDict() {
                febs.get(ctx + 'plan/produceProfessionTypeDict/tree', {}, function (e) {
                    if (e.code == 200) {
                        majorSelect = xmSelect.render({
                            el: '#majorSelect',
                            data: e.data,
                            radio: false,
                            filterable: true,
                            toolbar: {
                                show: true
                            },
                            clickClose: false,
                            tree: {
                                show: true,
                                strict: true,
                            },
                            on: function (data) {
                                if (data.isAdd) {
                                    setSsWorkUser(data.arr[0].value)
                                } else {
                                    setSsWorkUser(null)
                                }
                            }
                        })
                    }
                })
            }

            //设置生设作业人员
            function setSsWorkUser(pptdId) {
                let arr = []
                febs.postArraySync(ctx + 'plan/produceProfessionTypeDict/detailWork', { pptdId: pptdId }, function (data) {
                    if (data.code == 200) {
                        $.each(data.data, function (i, v) {
                            arr.push({
                                name: v.truename,
                                value: v.userId,
                            })
                        })
                    }
                })
                ssWorkUserSelect = xmSelect.render({
                    el: '#ssWorkUserSelect',
                    data: arr,
                    toolbar: {
                        show: true,
                        list: ['ALL', 'CLEAR']
                    },
                    filterable: true,
                    tips: "请选择",
                    radio: false,
                    clickClose: false,
                })
            }

            //检索
            $('#query').on('click', function () {

                if (shipNoSelect.getValue('value').length === 0) {
                    febs.alert.warn('请先选择船号');
                    return false
                }
                if (bomSelect.getValue('value').length === 0) {
                    febs.alert.warn('请先选择bom种类');
                    return false
                }

                var shipVal = shipNoSelect.getValue('value')
                var bomVal = bomSelect.getValue('value')
                var majorVal = majorSelect.getValue('value')
                var workerVal = ssWorkUserSelect.getValue('value')
                var areaVal = areaSelect.getValue('value')
                var sTime = $('#sTime').val()
                var eTime = $('#eTime').val()
                var rawMaterialNo = $('#rawMaterialNo').val();
                if (typeName == '计划完成率报表') {
                    initTable(dataType, shipVal, bomVal, majorVal, workerVal, areaVal, sTime, eTime)
                    getLeftNum(1, shipVal, bomVal, majorVal, workerVal, areaVal, sTime, eTime)
                } else {
                    initTable1(dataType, shipVal, bomVal, majorVal, workerVal, areaVal, sTime, eTime, rawMaterialNo)
                    getLeftNum(2, shipVal, bomVal, majorVal, workerVal, areaVal, sTime, eTime, rawMaterialNo)
                }
            })


            $('#exportsAll').on('click', function () {
                if (shipNoSelect.getValue('value').length === 0) {
                    febs.alert.warn('请先选择船号');
                    return false
                }
                if (bomSelect.getValue('value').length === 0) {
                    febs.alert.warn('请先选择bom种类');
                    return false
                }

                // reportFlg 计划完成率报表 0未登记，1未确认，2未关联; 物量汇总统计 0未使用物量 1未关联物量
                if (typeName == '计划完成率报表') {
                    var shipVal = shipNoSelect.getValue('value')
                    var bomVal = bomSelect.getValue('value')
                    var majorVal = majorSelect.getValue('value')
                    var workerVal = ssWorkUserSelect.getValue('value')
                    var areaVal = areaSelect.getValue('value')
                    var sTime = $('#sTime').val()
                    var eTime = $('#eTime').val()
                    let param = {
                        shipIds: shipVal,
                        reportFlg: dataType,
                        pdtCodeNames: bomVal,
                        workUserIdList: workerVal,
                        pptdIds: majorVal,
                        strIds: areaVal,
                        planStartDate: sTime,
                        planFinishDate: eTime,
                    }
                    febs.downloadByPost(ctx + 'bom/bomTrayBasicInfo/exportTemplate',
                        param,
                        '计划完成率报表导出' + new Date().getTime() + '.xlsx');
                } else {
                    var shipVal = shipNoSelect.getValue('value')
                    var bomVal = bomSelect.getValue('value')
                    var majorVal = majorSelect.getValue('value')
                    var workerVal = ssWorkUserSelect.getValue('value')
                    var areaVal = areaSelect.getValue('value')
                    var sTime = $('#sTime').val()
                    var eTime = $('#eTime').val()
                    var rawMaterialNo = $('#rawMaterialNo').val();
                    let param = {
                        shipIds: shipVal,
                        reportFlg: dataType,
                        pdtCodeNames: bomVal,
                        workUserIdList: workerVal,
                        pptdIds: majorVal,
                        strIds: areaVal,
                        planStartDate: sTime,
                        planFinishDate: eTime,
                        rawMaterialNo: rawMaterialNo
                    }
                    febs.downloadByPost(ctx + 'bom/exportRawMaterial/exportTemplate',
                        param,
                        '物资汇总统计' + new Date().getTime() + '.xlsx');
                }

            });
        });
    </script>
</body>

</html>v