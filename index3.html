<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.w3.org/1999/xhtml" lang="en">

<head>
    <meta charset="UTF-8">
    <title>设备协议清单管理</title>
    <th:block th:include="include::header('材料采购清单管理')" />
    <style>
        #febs-proMaterialManger .showBgColor {
            background-color: #9df50a !important;
        }

        #febs-proMaterialManger .layui-input-inline {
            width: 120px;
        }

        #febs-proMaterialManger .yfs-list {
            background-color: #f7f7f7;
            height: 100%;
        }

        #febs-proMaterialManger .ztree li span.button.add {
            margin-left: 2px;
            margin-right: -1px;
            background-position: -144px 0;
            vertical-align: top;
        }

        #febs-proMaterialManger .ztree li a {
            height: 25px;
        }

        #febs-proMaterialManger .ztree * {
            padding: 0;
            margin: 0;
            font-size: 16px;
            font-family: Verdana, Arial, Helvetica, AppleGothic, sans-serif;
        }

        #febs-proMaterialManger .button .ico_open {
            width: 20px;
            height: 20px;
        }

        #febs-proMaterialManger .tree-selected {
            color: #2F9688;
            font-weight: bold;
        }

        .layui-form select {
            display: none;
            width: 180px;
        }

        .layui-table tbody tr:hover,
        .layui-table-hover {
            background-color: #F8F8F8;
        }

        .layui-table-checked.layui-table-hover {
            background-color: #74b9ff !important;
        }

        #detail .layui-form-item {
            margin-bottom: 0;
            padding-top: 10px;
            padding-bottom: 5px;
        }

        #febs-proMaterialManger .search-form {
            padding-bottom: 0px;
            box-sizing: border-box;
        }

        .jhg-body-search {
            background-color: #F8F8F8;
        }

        #febs-proMaterialManger .edited {
            background-color: #74b9ff !important;
        }

        .weight-unit {
            width: 60%;
        }

        xm-select .xm-select-default {
            width: 20px !important;
        }

        #mainTable .layui-table-tool-temp {
            padding-right: 0px;
        }

        #febs-proMaterialManger .updateColorClass {
            background-color: #efc680
        }

        .layui-anim .layui-anim-upbit {
            z-index: 9999;
        }

        .layui-table-header .layui-table {
            margin-top: 5px;
        }

        .layui-table-header {
            height: 36px;
        }

        .shipNoText {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 180px;
            position: absolute;
            right: 10px;
        }

        .jhg-body-search .layui-form-item .layui-input-inline .layui-input,
        .jhg-body-search .layui-form-item .layui-input-inline .layui-select {
            width: 182px;
        }

        /* 为隔行变色定义CSS */
        .layui-table tbody tr:nth-child(odd) {
            background-color: #ffffff;
            /* 奇数行背景色 */
        }

        .layui-table tbody tr:nth-child(even) {
            background-color: #f2f2f2;
            /* 偶数行背景色 */
        }

        .jhg-body-search .layui-form-item .layui-form-label {
            padding: 5px 10px;
            width: auto;
        }

        .icon-img {
            width: 16px;
            height: 15px;
            margin-left: 2px;
            margin-bottom: 6px;
        }

        .layui-btn-container .layui-btn {
            margin-right: 5px;
        }

        .layui-form-item .layui-inline {
            margin-right: 0 !important;
        }

        .layui-table-tool {
            position: relative;
            width: 100%;
            min-height: 45px;
            line-height: 30px;
            padding: 5px 15px;
            border-width: 0;
            border-bottom-width: 1px;
        }

        .jhg-body-search .layui-form-item {
            padding-bottom: 0
        }

        #shipNos xm-select>.xm-body {
            width: 350px;
        }

        .btnRemark {
            cursor: pointer;
            position: fixed;
            right: 154px;
            top: 58px;
            width: 35px;
            height: 35px;
            background: #1b83f0;
            border-radius: 35px;
            line-height: 34px;
            text-align: center;
            display: inline-block;
            color: #ffff;
            z-index: 100000;
        }

        .btnRemarkContentClass {
            position: fixed;
            z-index: 100000;
            top: 50px;
            right: 50px;
            width: 190px;
            padding: 20px;
            cursor: move;
            background: #9dd5ebcc;
            animation: fadenumstyle .5s;
            display: none;
        }

        .transferMaterialTipsClass {
            cursor: pointer;
            position: fixed;
            right: 218px;
            top: 58px;
            width: 100px;
            height: 35px;
            background: #1b83f0;
            border-radius: 35px;
            line-height: 34px;
            text-align: center;
            display: inline-block;
            color: #ffff;
            z-index: 100000;
        }

        .transferMaterialTipsContentClass {
            position: fixed;
            z-index: 100000;
            top: 50px;
            right: 120px;
            width: 1100px;
            height: 600px;
            padding: 20px;
            /*cursor: move;*/
            background: #9dd5ebcc;
            animation: fadenumstyle .5s;
            display: none;
        }

        #porShipNoSelect xm-select>.xm-body {
            width: 350px;
        }

        #dstShipNoSelect xm-select>.xm-body {
            width: 350px;
        }

        #febs-proMaterialManger .jhg-body-search .layui-form-item .layui-input-inline .layui-input,
        #febs-proMaterialManger .jhg-body-search .layui-form-item .layui-input-inline .layui-select {
            width: 172px;
        }

        #febs-proMaterialManger #searchForm .layui-form-item .layui-input-inline {
            width: 172px !important;
        }

        /* 隐藏滚动条但保持滚动功能 */
        .layui-row {
            scrollbar-width: none;
            /* Firefox */
            -ms-overflow-style: none;
            /* IE 和 Edge */
        }

        .layui-row::-webkit-scrollbar {
            display: none;
            /* Chrome, Safari 和 Opera */
        }

        /* jQuery UI resizable 样式 */
        .ui-resizable-handle {
            position: absolute;
            font-size: 0.1px;
            display: block;
            -ms-touch-action: none;
            touch-action: none;
        }

        .ui-resizable-s {
            cursor: s-resize;
            height: 7px;
            width: 100%;
            bottom: -5px;
            left: 0;
            background: linear-gradient(to bottom, transparent, #ddd);
            transition: background 0.2s ease;
        }

        .ui-resizable-s:hover {
            background: linear-gradient(to bottom, transparent, #1890ff);
        }

        .ui-resizable-s::after {
            content: '';
            position: absolute;
            bottom: 2px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 2px;
            background: #999;
            border-radius: 1px;
        }

        .ui-resizable-s:hover::after {
            background: #1890ff;
        }

        /* 表格滚动容器样式 */
        #mainTableScrollWrapper,
        #detailTableScrollWrapper,
        #previousTableScrollWrapper {
            /* 自定义滚动条样式 */
            scrollbar-width: thin;
            scrollbar-color: #c1c1c1 #f1f1f1;
        }

        #mainTableScrollWrapper::-webkit-scrollbar,
        #detailTableScrollWrapper::-webkit-scrollbar,
        #previousTableScrollWrapper::-webkit-scrollbar {
            width: 8px;
        }

        #mainTableScrollWrapper::-webkit-scrollbar-track,
        #detailTableScrollWrapper::-webkit-scrollbar-track,
        #previousTableScrollWrapper::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        #mainTableScrollWrapper::-webkit-scrollbar-thumb,
        #detailTableScrollWrapper::-webkit-scrollbar-thumb,
        #previousTableScrollWrapper::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        #mainTableScrollWrapper::-webkit-scrollbar-thumb:hover,
        #detailTableScrollWrapper::-webkit-scrollbar-thumb:hover,
        #previousTableScrollWrapper::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }

        /* 让第二个表格使用layui原生的横向滚动和固定列功能 */
        #detailTableContainer .layui-table-view {
            width: 100% !important;
        }

        /* 确保第二个表格的外层容器不干扰layui的滚动机制 */
        #detailTableScrollWrapper {
            overflow: hidden !important;
        }

        /* 让layui表格自己处理横向滚动 */
        #detailTableContainer .layui-table-body.layui-table-main {
            overflow-x: auto !important;
        }

        /* 确保第二个表格有合适的最小宽度以触发横向滚动 */
        #detailTableContainer .layui-table {
            min-width: 100% !important;
        }

        /* 确保第二个表格的固定列正常显示 */
        #detailTableContainer .layui-table-fixed {
            z-index: 999 !important;
        }

        #detailTableContainer .layui-table-fixed-l {
            box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1) !important;
        }

        /* 确保第二个表格容器高度固定 */
        #detailTableContainer {
            height: 150px !important;
        }

        #detailTableScrollWrapper {
            height: 100% !important;
        }

        /* 固定第一个表格的工具栏 */
        #mainTableContainer .layui-table-tool {
            position: sticky !important;
            top: 0 !important;
            z-index: 1000 !important;
            background-color: #fff !important;
            border-bottom: 1px solid #e6e6e6 !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
        }

        /* 确保第一个表格容器支持sticky定位和拖拽功能 */
        #mainTableContainer {
            position: relative !important;
        }

        #mainTableScrollWrapper {
            position: relative !important;
        }
    </style>
    <link rel="stylesheet" th:href="@{/febs/views/css/commonZs.css}" media="all">
</head>

<body>
    <div class="layui-fluid layui-anim febs-anim page-body" id="febs-proMaterialManger" lay-title="材料采购清单管理">
        <div class="layui-row" style="display: flex;flex-direction: column; height:100%">
            <form class="layui-form search-form"
                style="background-color: #ffffff !important;width: 100%; flex-shrink: 0;" id="searchForm">
                <div class="jhg-body-search" style="background-color: #ffffff !important;">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm">船号:</label>
                            <div class="layui-input-inline" style="width: 220px">
                                <div id="shipNos"></div>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm">专业划分:</label>
                            <div class="layui-input-inline">
                                <div id="proTypeList"></div>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm">设计专业:</label>
                            <div class="layui-input-inline">
                                <div id="professions"></div>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm">物资编码:</label>
                            <div class="layui-input-inline">
                                <input type="text" name="basicMaterialNo" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm">物资描述:</label>
                            <div class="layui-input-inline" style="width: 120px">
                                <input type="text" name="basicMaterialDesc" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline" shiro:hasPermission="porMaterialManageView:view">
                            <div id="query" class="layui-btn searchBlue layui-btn-sm">
                                <em class="layui-icon">&#xe615;</em> 检索
                            </div>
                        </div>
                        <div class="layui-inline">
                            <div id="moreQuery" class="layui-btn searchBlue layui-btn-sm">
                                更多查询
                            </div>
                        </div>
                        <div class="layui-inline">
                            <div class="btnRemark" id="btnRemark">备注</div>
                            <div id="btnRemarkContent" class="btnRemarkContentClass">
                                <div style="margin-bottom: 2px">备注:</div>
                                <div style="display: flex;margin-bottom: 2px">
                                    <div style="height: 19px;width: 30px;background-color: #9df50a;margin-right: 20px">
                                    </div>
                                    <div>: 行点击</div>
                                </div>
                                <div style="display: flex;margin-bottom: 2px">
                                    <div style="height: 19px;width: 30px;background-color: #74b9ff;margin-right: 20px">
                                    </div>
                                    <div>: 行选中</div>
                                </div>
                                <div style="display: flex;margin-bottom: 2px">
                                    <div style="height: 19px;width: 30px;background-color: #efc680;margin-right: 20px;">
                                    </div>
                                    <div>: 等待二次上传SAP</div>
                                </div>

                            </div>
                        </div>
                        <div class="layui-inline">
                            <div class="transferMaterialTipsClass" id="transferMaterialTips"
                                shiro:hasPermission="porMaterialManageView:transferMaterial">
                                <i class="layui-icon layui-icon-login"></i>
                                物资移库
                            </div>
                            <div id="transferMaterialTipsContent" class="transferMaterialTipsContentClass">
                                <div id="searchDiv"
                                    style="background-color: white;margin-bottom: 10px;padding-top: 10px;">
                                    <div class="layui-inline">
                                        <label class="layui-form-label layui-form-label-sm">船号:</label>
                                        <div class="layui-input-inline" style="width: 220px">
                                            <div id="porShipNoSelect"></div>
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label layui-form-label-sm">目标船:</label>
                                        <div class="layui-input-inline" style="width: 220px">
                                            <div id="dstShipNoSelect"></div>
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label layui-form-label-sm"
                                            style="padding-left: 40px;">专业划分:</label>
                                        <div class="layui-input-inline">
                                            <div id="proTypeListTransfer"></div>
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label layui-form-label-sm">设计专业:</label>
                                        <div class="layui-input-inline">
                                            <div id="professionsTransfer"></div>
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label layui-form-label-sm">POR:</label>
                                        <div class="layui-input-inline">
                                            <input type="text" id="porHeadNoTransfer" autocomplete="off"
                                                class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label layui-form-label-sm"
                                            style="width: 100px;">物资编码/描述:</label>
                                        <div class="layui-input-inline">
                                            <input type="text" id="basicMaterialNoOrDesc" autocomplete="off"
                                                class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-inline" shiro:hasPermission="porDeviceManageView:view">
                                        <div id="transferQuery" class="layui-btn searchBlue layui-btn-sm">
                                            <em class="layui-icon">&#xe615;</em> 检索
                                        </div>
                                        <div id="queryOther1" class="layui-hide">检索(假)</div>
                                    </div>


                                    <div class="layui-inline" shiro:hasPermission="porDeviceManageView:edit">
                                        <div class="layui-btn layui-btn-sm layui-bg-red" id="deleteStorge">
                                            <i class="layui-icon layui-icon-delete"></i> 删除
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div id="applyStorge"
                                        style="width:100%;background-color: white;height: 475px;overflow: hidden;">
                                        <div class="jhg-body-table">
                                            <table class="layui-hide" id="applyStorgeList" lay-filter="applyStorgeList">
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="layui-inline" style="left: 45%;margin-top: 10px;">
                                        <div id="transferSubmit" class="layui-btn searchBlue layui-btn-sm">
                                            <em class="layui-icon"></em> 提交
                                        </div>
                                        <div id="transferCancle" class="layui-btn layui-btn-sm">
                                            <em class="layui-icon"></em> 取消
                                        </div>
                                    </div>
                                </div>
                            </div>


                        </div>
                        <input id="editFlagWithLine" hidden="" />
                        <input id="editFlagWithPre" hidden="" />
                    </div>
                    <div class="layui-form-item layui-hide" id="moreQueryDiv">
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm">负责人:</label>
                            <div class="layui-input-inline">
                                <input type="text" name="directorName" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm">SAP状态:</label>
                            <div class="layui-input-inline" style="width: 120px">
                                <select id="sapStatus" lay-filter="codeType">
                                    <option value="">所有</option>
                                    <option value="0">未处理</option>
                                    <option value="1">部分处理</option>
                                    <option value="2">全部处理</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm">
                                船型区域:</label>
                            <div class="layui-input-inline" style="width: 220px">
                                <div id="regions"></div>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm">POR编号:</label>
                            <div class="layui-input-inline">
                                <input type="text" name="porHeadNo" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm">POR状态：</label>
                            <div class="layui-input-inline">
                                <select id="codeType" lay-filter="codeType">
                                    <option value="">所有</option>
                                    <option value="0">未上传</option>
                                    <option value="1">部分上传</option>
                                    <option value="2">全部上传</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
            <div class="layui-form col-md-3" id="mainTableContainer" style="display: flex; flex: 1; min-height: 150px;">
                <div id="mainTableScrollWrapper"
                    style="width: 100%;background-color: white; margin-bottom: 3px; overflow-y: auto; overflow-x: hidden; height: 100%;">
                    <div id="mainTable" style="width: 100%;background-color: white;">
                        <div class="jhg-body-table">
                            <table class="layui-hide" lay-filter="porHeadMaterialTable"
                                lay-data="{id: 'porHeadMaterialTable'}" id="protocolList">
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <form class="layui-form search-form" style="flex-shrink: 0;">
                <div class="jhg-body-search">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm" style="width: 70px">物资编码:</label>
                            <div class="layui-input-inline" style="width:180px;">
                                <input type="text" name="basicMaterialNoLine" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm" style="width: 70px">物资描述:</label>
                            <div class="layui-input-inline" style="width:180px;">
                                <input type="text" name="basicMaterialDescLine" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline" shiro:hasPermission="porMaterialManageView:view"
                            style="margin-left: -5px">
                            <div id="query2" class="layui-btn searchBlue layui-btn-sm">
                                <em class="layui-icon">&#xe615;</em> 检索
                            </div>
                            <div id="queryOther2" class="layui-hide">检索(假)</div>
                        </div>

                        <div class="layui-inline" shiro:hasPermission="porMaterialManageView:edit">
                            <div class="layui-btn layui-btn-sm blueBtm" id="addLine" lay-event="addLine">
                                <i class="layui-icon layui-icon-add-1"></i> 新增
                            </div>
                        </div>

                        <div class="layui-inline" shiro:hasPermission="porMaterialManageView:edit">
                            <div class="layui-btn layui-btn-sm blueBtm" id="importLine" lay-event="importLine">
                                <i class="layui-icon">&#xe7aa;</i> 导入
                            </div>
                        </div>

                        <!--                    <div class="layui-inline" shiro:hasPermission="porMaterialManageView:edit">-->
                        <!--                        <div class="layui-btn blueBtm layui-btn-sm" id="saveBatchLine" lay-event="saveBatchLine">-->
                        <!--                            <i class="layui-icon">&#xe7a9;</i> 保存-->
                        <!--                        </div>-->
                        <!--                    </div>-->
                        <div class="layui-inline" shiro:hasPermission="porMaterialManageView:upload">
                            <div class="layui-btn layui-btn-sm blueBtm" id="upBatch" lay-event="upBatch">
                                <i class="layui-icon">&#xe828;</i> 材料单上传
                            </div>
                        </div>
                        <div class="layui-inline" shiro:hasPermission="porMaterialManageView:edit">
                            <div class="layui-btn layui-btn-sm layui-bg-red" id="deleteLine" lay-event="deleteLine">
                                <i class="layui-icon layui-icon-delete"></i> 删除
                            </div>
                        </div>
                        <div class="layui-inline" style="float: right;">
                            <div id="lineTotalCount"
                                style="width: 140px !important;text-align: right;padding-top: 6px;padding-right: 15px;">

                            </div>
                        </div>
                        <div class="layui-inline">
                            <div class="layui-btn blueBtm layui-btn-sm febs-hide" id="saveBatchLineExit"
                                lay-event="saveBatchLineExit">
                                <i class="layui-icon">&#xe7a9;</i> 保存
                            </div>
                        </div>

                        <div class="layui-inline" shiro:hasPermission="porMaterialManageView:transferMaterial">
                            <div class="layui-btn layui-btn-sm blueBtm" id="transferMaterialForApply"
                                lay-event="transferMaterialForApply">
                                <i class="layui-icon">&#xe60a;</i> 物资移库
                            </div>
                        </div>
                    </div>
                </div>
            </form>
            <div class="layui-form col-md-1" id="detailTableContainer"
                style="display: flex; height: 150px; flex-shrink: 0;">
                <div id="detailTableScrollWrapper"
                    style="width:100%;background-color: white;height: 100%;overflow: hidden;">
                    <div id="detail" style="width:100%;background-color: white;">
                        <div class="jhg-body-table">
                            <table class="layui-hide" lay-filter="porLineMaterialTable"
                                lay-data="{id: 'porLineMaterialTable'}" id="protocolList2">
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <form class="layui-form search-form" style="flex-shrink: 0;">
                <div class="jhg-body-search">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm" style="width: 70px"
                                for="basicMaterialNoPrevious">物资编码:</label>
                            <div class="layui-input-inline" style="width:180px;">
                                <input type="text" name="basicMaterialNoPrevious" id="basicMaterialNoPrevious"
                                    autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label layui-form-label-sm" style="width: 70px"
                                for="basicMaterialDescPrevious">物资描述:</label>
                            <div class="layui-input-inline" style="width:180px;">
                                <input type="text" name="basicMaterialDescPrevious" id="basicMaterialDescPrevious"
                                    autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline" style="margin-left: -5px">
                            <div id="previousQuery" class="layui-btn searchBlue layui-btn-sm">
                                <em class="layui-icon">&#xe615;</em> 检索
                            </div>
                        </div>
                        <div class="layui-inline" style="float: right;">
                            <div id="previousTotalCount"
                                style="width: 140px !important;text-align: right;padding-top: 6px;padding-right: 15px;">
                            </div>
                        </div>
                    </div>
                </div>
            </form>
            <div class="layui-form col-md-1" id="previousTableContainer"
                style="display: flex; flex: 1; min-height: 150px; margin-top: 10px;">
                <div id="previousTableScrollWrapper"
                    style="width:100%;background-color: white;height: 100%;overflow-y: auto; overflow-x: hidden;">
                    <div id="previousShipMaterialDiv" style="width:100%;background-color: white;">
                        <div class="jhg-body-table">
                            <table class="layui-hide" id="previousShipMaterialTable"
                                lay-filter="previousShipMaterialTable">
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 工具栏模板 -->
    <script type="text/html" id="deviceProtocolListMangerToolbar">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm blueBtm" id="add" lay-event="add"
                shiro:hasPermission="porMaterialManageView:edit">
            <i class="layui-icon layui-icon-add-1"></i>新增
        </button>

        <button type="button" class="layui-btn layui-btn-sm blueBtm" id="import" lay-event="import"
                shiro:hasPermission="porMaterialManageView:edit">
            <i class="layui-icon">&#xe7aa;</i>导入
        </button>
        <button type="button" class="layui-btn layui-btn-sm blueBtm" id="export" lay-event="export"
                shiro:hasPermission="porMaterialManageView:view">
            <i class="layui-icon layui-icon-export"></i>导出
        </button>
        <button type="button" class="layui-btn layui-btn-sm blueBtm" id="copyMaterial" lay-event="copyMaterial"
                shiro:hasPermission="porMaterialManageView:copy">
            <i class="layui-icon">&#xe7ef;</i>项目复制
        </button>
        <button type="button" class="layui-btn layui-btn-sm blueBtm" id="confirmSap" lay-event="confirmSap"
                shiro:hasPermission="porMaterialManageView:confirmSap">
            <i class="layui-icon"><img data-th-src="@{/img/icon/comfirm.png}" class="icon-img"></i>确认
        </button>
        <button class="layui-btn layui-btn-sm blueBtm" id="selectLeftInfos" lay-event="selectLeftInfos"
                shiro:hasPermission="porMaterialManageView:leftInfos">
            <i class="layui-icon"><img data-th-src="@{/img/icon/total.png}" class="icon-img"></i>统计
        </button>
        <button class="layui-btn layui-btn-sm layui-bg-red" id="delete" lay-event="delete"
                shiro:hasPermission="porMaterialManageView:edit">
            <i class="layui-icon layui-icon-delete"></i>删除
        </button>
        <button type="button" class="layui-btn layui-btn-sm layui-bg-red" id="confirmSapBack" lay-event="confirmSapBack"
                shiro:hasPermission="porMaterialManageView:confirmSapBack">
            <i class="layui-icon"><img data-th-src="@{/img/icon/revoke.png}" class="icon-img"></i>撤销确认
        </button>
        <button type="button" class="layui-btn layui-btn-sm blueBtm" id="up" lay-event="up"
                shiro:hasPermission="porMaterialManageView:up">
            <i class="layui-icon">&#xe828;</i>上传SAP
        </button>
        <button type="button" class="layui-btn layui-btn-sm blueBtm" id="updatePurchase" lay-event="updatePurchase"
                shiro:hasPermission="porMaterialManageView:updatePurchase">
            <i class="layui-icon">&#xe828;</i>同步采购状态
        </button>
        <div class="layui-inline" style="float: right; width: 125px !important;">
            <div id="headTotalCount" style="text-align: right;padding-top: 10px;word-spacing: 0px;">

            </div>
        </div>
        <!--        <button type="button" class="layui-btn layui-btn-sm blueBtm" id="saveBatch" lay-event="saveBatch">-->
        <!--            <i class="layui-icon">&#xe7a9;</i>保存-->
        <!--        </button>-->

    </div>
</script>
    <script type="text/html" id="materialProtocolListMangerToolbar">
    <div class="layui-btn-container">
        <!--        <button type="button" class="layui-btn layui-btn-sm blueBtm" id="exportLine" lay-event="exportLine">-->
        <!--            <i class="layui-icon">&#xe601;</i>导出-->
        <!--        </button>-->

    </div>
</script>
    <th:block th:include="include::foot" />
    <script data-th-inline="none" type="text/javascript">

        layui.config({
            base: ctx + 'febs/'
        }).extend({
            febs: 'lay/modules/febs',
            validate: 'lay/modules/validate',
            formSelects: 'lay/extends/formSelects-v4.min',
            jqueryui: 'lay/extends/jquery-ui.min',
            echarts: 'lay/extends/echarts.min',
            commonJS: 'lay/extends/common',
        }).use(['jquery', 'validate', 'table', 'form', 'febs', 'commonJS', 'rpcJs', 'jqueryui'], function () {
            var $ = layui.jquery,
                $view = $('#febs-proMaterialManger'),
                $mainTable = $('#mainTable'),
                febs = layui.febs,
                form = layui.form,
                commonJS = layui.commonJS,
                rpcJs = layui.rpcJs,
                shipNos,
                shipArr = [],
                shipIds,
                shipId,
                $query = $view.find('#query'),
                $queryTwo = $view.find('#query2'),
                $searchForm = $view.find('form'),
                tableInsTwo,
                phmpId,
                headUploadStatus,
                professions,
                porShipNoSelect,
                dstShipNoSelect,
                professionsTransfer,
                proTypeListTransfer,
                regions,
                strids,
                proIds,
                units,
                unitSelect,
                shipTypeId,
                proTypeList,
                proData,
                detailArr = [],
                detailProMap = new Map(),
                proMap = new Map(),
                regionsMap = new Map(),
                proNoToIdMap = new Map(),
                proNoToTypeMap = new Map(),
                xsorssMap = new Map(),
                ssMap = new Map(),
                xsMap = new Map(),
                table = layui.table;
            var tableIns, headStatus, previousTableIns, onRowHaveEditShiro, linePhDesc, porId, porType, transferTableIns;
            form.render();
            var xsProfessionUserList = parent.xsProfessionUserList;
            var ssProfessionUserList = parent.ssProfessionUserList;

            var editFlg = currentUser.permissionSet.indexOf('porMaterialManageView:edit') == -1 ? false : true
            let permiRestFlag = currentUser.permissionSet.indexOf('porMaterialManageView:restFlg') > -1 ? true : false;
            var uploadFlg = currentUser.permissionSet.indexOf('porMaterialManageView:upload') == -1 ? false : true


            autoHeight();

            $(window).resize(function () {
                // let bodyTableHeight = $view.find("#mainTable").height()-137;
                // $view.find(".layui-table-body.layui-table-main").css("height",bodyTableHeight);
                autoHeight();
            })
            getProfessions();
            setShipNo();
            setProType();
            setProfessions();
            setRegions([]);
            setUnits();
            initTable();

            function getProfessions() {
                febs.getSyn(ctx + 'plan/produceProfessionTypeDict/tree', {}, function (e) {
                    if (e.code == 200) {
                        proData = e.data
                        $.each(e.data, function (i, v) {
                            $.each(v.children, function (index, child) {
                                let name = child.name;
                                let desc = child.desc;
                                let str = name.replaceAll(desc, "").replaceAll(" ", "");
                                proMap.set(parseInt(child.value), str);
                            })
                        })

                    }
                })
                febs.getSyn(ctx + 'plan/detailDesignProfessionTypeDict', null, function (data) {
                    if (data.code == 200) {
                        $.each(data.data, function (i, v) {
                            detailProMap.set(v.ddptId, v.ddptCodeName);
                            detailArr.push({
                                name: v.ddptCodeName,
                                value: v.ddptId,
                                showname: v.ddptCode
                            })
                        })
                    }
                })
            }

            function autoHeight() {
                //选项卡tab高度
                var tabHeight = $(".febs-tabs-wrap").height() == null ? "0" : $(".febs-tabs-wrap").height();
                //标题头的高度
                var appHeight = $("#app-header").height() == null ? "0" : $("#app-header").height();
                var diff = tabHeight == 0 ? 16 : 52;
                var tempheight = $(window).height() - tabHeight - appHeight - diff;
                // 设置主容器高度，留出一些边距避免超出屏幕
                $view.find('.layui-row').height(tempheight - 10);

            }

            function setUnits() {
                febs.getSyn(ctx + 'basic/basicUnitItemDict/all', {}, function (e) {
                    if (e.code == 200) {
                        units = e.data
                    }
                })
            }

            function getRegions(shipTypeId) {
                let arr = []
                febs.getSyn(ctx + 'basic/shipTypeArea/all', { shipTypeId: shipTypeId }, function (e) {
                    if (e.code == 200) {
                        $.each(e.data, function (i, v) {
                            regionsMap.set(v.strId, v.strCodeName);
                            arr.push({
                                name: v.strCodeName,
                                value: v.strId,
                                showname: v.strCode
                            })
                        })
                    }
                })
                return arr
            }

            function setProType() {
                let proArr = [{ name: '详细设计专业', value: '0' }, { name: '生产设计专业', value: '1' }]
                proTypeList = xmSelect.render({
                    el: '#proTypeList',
                    filterable: true,
                    data: proArr,
                    template({ item }) {
                        return item.name
                    },
                    radio: true,
                    clickClose: true,
                    on: function (data) {
                        let isAdd = data.isAdd;
                        if (!isAdd) {
                            professions = xmSelect.render({
                                el: '#professions',
                                data: [],
                                filterable: true,
                                template({ item }) {
                                    return item.name + '<span class="shipNoText" title="' + item.showname + '">' + item.showname + '</span>'
                                },
                                radio: true,
                                clickClose: true,
                            })
                            return false;
                        }
                        if (data.change[0].value == 0) {
                            professions = xmSelect.render({
                                el: '#professions',
                                data: detailArr,
                                filterable: true,
                                template({ item }) {
                                    return item.name + '<span class="shipNoText" title="' + item.showname + '">' + item.showname + '</span>'
                                },
                                radio: true,
                                clickClose: true,
                            })
                        } else {
                            professions = xmSelect.render({
                                el: '#professions',
                                data: proData,
                                radio: true,
                                clickClose: true,
                                height: '150px',
                                tree: {
                                    show: true,
                                    strict: false,

                                }
                            })
                        }
                    }
                })

                proTypeListTransfer = xmSelect.render({
                    el: '#proTypeListTransfer',
                    data: proArr,
                    filterable: true,
                    template({ item }) {
                        return item.name
                    },
                    radio: true,
                    clickClose: true,
                    on: function (data) {
                        let isAdd = data.isAdd;
                        if (!isAdd) {
                            professionsTransfer = xmSelect.render({
                                el: '#professionsTransfer',
                                data: [],
                                filterable: true,
                                template({ item }) {
                                    return item.name + '<span class="shipNoText" title="' + item.showname + '">' + item.showname + '</span>'
                                },
                                radio: true,
                                clickClose: true,
                            })
                            return false;
                        }
                        if (data.change[0].value == 0) {
                            professionsTransfer = xmSelect.render({
                                el: '#professionsTransfer',
                                data: detailArr,
                                filterable: true,
                                template({ item }) {
                                    return item.name + '<span class="shipNoText" title="' + item.showname + '">' + item.showname + '</span>'
                                },
                                radio: true,
                                clickClose: true,
                            })
                        } else {
                            professionsTransfer = xmSelect.render({
                                el: '#professionsTransfer',
                                data: proData,
                                radio: true,
                                clickClose: true,
                                filterable: true,
                                height: '150px',
                                tree: {
                                    show: true,
                                    strict: false,

                                },
                                template({ item }) {
                                    return item.name + '<span style="position: absolute;right: 10px;color: #90a4ae">' + item.desc + '</span>'
                                },
                                // iconfont : {
                                //     parent : 'hidden' //隐藏父节点图标
                                // },
                            })
                        }
                    }
                })
            }

            function setRegions(arr) {

                regions = xmSelect.render({
                    el: '#regions',
                    data: arr,
                    filterable: true,
                    tips: arr.length === 0 ? "请先选择船号" : "请选择",
                    template({ item }) {
                        return item.name + '<span class="shipNoText" title="' + item.showname + '">' + item.showname + '</span>'
                    },
                    radio: true,
                    clickClose: true,
                })
                if (localStorage.getItem("shipMaterialPredictAndPorShipNo", strids) != null) {
                    strids = localStorage.getItem("shipMaterialPredictAndPorShipNo", strids)
                    regions.setValue(strids.split(','))
                }

            }

            function setProfessions() {
                professions = xmSelect.render({
                    el: '#professions',
                    data: [],
                    filterable: true,
                    tips: "请先选择专业类型",
                    template({ item }) {
                        return item.name + '<span class="shipNoText" title="' + item.showname + '">' + item.showname + '</span>'
                    },
                    radio: true,
                    clickClose: true,
                })
                if (localStorage.getItem("shipMaterialPredictAndPorShipNo", proIds) != null) {
                    proIds = localStorage.getItem("shipMaterialPredictAndPorShipNo", proIds)
                    professions.setValue(proIds.split(','))
                }

            }


            function initTable() {
                localStorage.removeItem('porMaterialInfos')
                let arr = []
                createTable(arr)
                createTable2(arr)
                // 初始化移库表格
                createTable3();
            }


            function setShipNo() {
                let arr = [];
                let resp = rpcJs.getShipDataList();
                if (resp.code == 200) {
                    allShips = resp.data
                    $.each(resp.data, function (i, v) {
                        shipArr.push({
                            name: v.shipNo,
                            value: v.shipId,
                            showname: v.showName,
                            typeId: v.typeId
                        })
                    })
                }
                shipNos = xmSelect.render({
                    el: '#shipNos',
                    data: shipArr,
                    filterable: true,
                    template({ item }) {
                        return item.name + '<span class="shipNoText" title="' + item.showname + '">' + item.showname + '</span>'
                    },
                    radio: true,
                    clickClose: true,
                    model: {
                        label: {
                            block: {
                                template(item, sels) {
                                    return item.name + '(' + item.showname + ')'
                                }
                            }
                        }
                    },
                    on: function (data) {
                        if (data.arr[0] != undefined) {
                            shipTypeId = data.arr[0].typeId

                            // 物资移库有数据时只允许选择单船 提示
                            if (null != localStorage.getItem('porMaterialInfos') && data.arr[0].value != JSON.parse(localStorage.getItem('porMaterialInfos'))[0].porShipId) {
                                febs.modal.confirm("注意", "当前船：" + data.arr[0].name + "还存在物资移库数据未提交，请确认是否切换船号", function () {
                                    let arr = getRegions(shipTypeId)
                                    setRegions(arr)
                                    // 切换船号 删除缓存
                                    localStorage.removeItem('porMaterialInfos')
                                }, function () {
                                    shipNos.setValue([JSON.parse(localStorage.getItem('porMaterialInfos'))[0].porShipId])
                                    let arr = getRegions(shipNos.getValue()[0].typeId)
                                    setRegions(arr)
                                })
                            } else {
                                let arr = getRegions(shipTypeId)
                                setRegions(arr)
                            }
                        }
                    }
                })

                if (localStorage.getItem("shipMaterialPredictAndPorShipNo", shipIds) != null) {
                    shipIds = localStorage.getItem("shipMaterialPredictAndPorShipNo", shipIds)
                    shipNos.setValue(shipIds.split(','))
                }

                porShipNoSelect = xmSelect.render({
                    el: '#porShipNoSelect',
                    data: shipArr,
                    filterable: true,
                    template({ item }) {
                        return item.name + '<span class="shipNoText" title="' + item.showname + '">' + item.showname + '</span>'
                    },
                    radio: true,
                    clickClose: true,
                    model: {
                        label: {
                            block: {
                                template(item, sels) {
                                    return item.name + '(' + item.showname + ')'
                                }
                            }
                        }
                    },
                    on: function (data) {
                    }
                })
                dstShipNoSelect = xmSelect.render({
                    el: '#dstShipNoSelect',
                    data: shipArr,
                    filterable: true,
                    template({ item }) {
                        return item.name + '<span class="shipNoText" title="' + item.showname + '">' + item.showname + '</span>'
                    },
                    radio: true,
                    clickClose: true,
                    model: {
                        label: {
                            block: {
                                template(item, sels) {
                                    return item.name + '(' + item.showname + ')'
                                }
                            }
                        }
                    },
                    on: function (data) {
                    }
                })
            }

            function createTable(data) {
                tableIns = febs.table.init({
                    elem: $view.find('#protocolList'),
                    id: 'protocolList',
                    toolbar: '#deviceProtocolListMangerToolbar',
                    defaultToolbar: [],
                    data: data,
                    autoSort: true,
                    // height: $view.find("#mainTable").height(), // 移除固定高度，让表格自适应容器
                    cols: [
                        [
                            { fixed: 'left', type: 'checkbox' },
                            {
                                fixed: 'left',
                                field: 'porHeadNo',
                                title: 'POR编号',
                                sort: true,
                                align: 'center',
                                width: 92,
                                templet: function (d) {
                                    return '<div style="text-align: left">' + d.porHeadNo + '</div>'
                                }
                            },
                            {
                                fixed: 'left',
                                field: 'porHeadDesc',
                                title: '<span style="color: #1b83f0;font-weight: bold;">POR描述</span>',
                                width: 120,
                                align: 'center',
                                edit: function (d) {
                                    if (editFlg && d.sapStatus == 0) {
                                        return "text"
                                    }
                                },
                                templet: function (d) {
                                    return '<div style="text-align: left">' + d.porHeadDesc + '</div>'
                                }
                            },
                            { field: 'l', title: 'L', minWidth: 40, align: 'center' },
                            {
                                field: 'proId', title: 'POR专业', minWidth: 88, align: 'center', templet: function (d) {
                                    if (d.proType == "0") {
                                        //详细设计专业
                                        return detailProMap.get(d.proId) == null ? '未知' : '详设-' + detailProMap.get(d.proId);
                                    } else if (d.proType == "1") {
                                        //生产设计专业
                                        return proMap.get(d.proId) == null ? '未知' : '生设-' + proMap.get(d.proId);
                                    } else {
                                        return "未知";
                                    }
                                }
                            },
                            {
                                field: 'strId', title: '船型区域', minWidth: 95, align: 'center', templet: function (d) {
                                    return regionsMap.get(d.strId) == null ? '未知' : regionsMap.get(d.strId);
                                }
                            },
                            { field: 'deptName', title: 'POR部门', width: 95, align: 'center' },
                            { field: 'createdByName', title: 'POR负责人', width: 95, align: 'center' },
                            { field: 'phone', title: '电话', width: 110, align: 'center' },
                            {
                                field: 'produceDate',
                                title: '生产需求日',
                                sort: true,
                                minWidth: 107,
                                align: 'center',
                                templet: function (d) {
                                    if (d.produceDate != null) {
                                        return commonJS.formatDate(new Date(d.produceDate), 'yyyy-MM-dd')
                                    } else {
                                        return ''
                                    }
                                }
                            },
                            {
                                field: 'protocolDate',
                                title: '计划发放日',
                                sort: true,
                                minWidth: 107,
                                align: 'center',
                                templet: function (d) {
                                    if (d.protocolDate != null) {
                                        return commonJS.formatDate(new Date(d.protocolDate), 'yyyy-MM-dd')
                                    } else {
                                        return ''
                                    }
                                }
                            },
                            { field: 'status', title: 'status', hide: true },
                            { field: 'statusTrans', title: 'POR状态', width: 70, align: 'center' },
                            { field: 'sapStatusStr', title: 'SAP状态', minWidth: 75, align: 'center' },
                            {
                                field: 'uploadDate',
                                title: '实际发放日',
                                sort: true,
                                minWidth: 107,
                                align: 'center',
                                templet: function (d) {
                                    if (d.uploadDate != null) {
                                        return commonJS.formatDate(new Date(d.uploadDate), 'yyyy-MM-dd')
                                    } else {
                                        return ''
                                    }
                                }
                            },
                            {
                                field: 'remark',
                                title: '<span style="color: #1b83f0;font-weight: bold">备注</span>',
                                width: 100,
                                align: 'center',
                                edit: function (d) {
                                    if (editFlg && d.sapStatus == 0) {
                                        return "text"
                                    }
                                },
                                templet: function (d) {
                                    return '<div style="text-align: left">' + (d.remark != null ? d.remark : "") + '</div>'
                                }
                            },
                            // {field: 'releaseStatus',title: '操作',  width: 80,align: 'center',fixed:'right',templet:function (d) {
                            //         if (d.sapStatus == 1) {
                            //             return  '    <div class="layui-clear-space">\n' +
                            //                 // '        <a class="layui-btn layui-btn-xs layui-bg-lime" lay-event="show">查看</a>\n' +
                            //                 '    </div>'
                            //         } else {
                            //             return  '    <div class="layui-clear-space">\n' +
                            //                 // '        <a class="layui-btn layui-btn-xs layui-bg-lime" lay-event="show">查看</a>\n' +
                            //                 // '        <a class="layui-btn layui-btn-xs blueBtm" lay-event="update">保存</a>\n' +
                            //                 '    </div>'
                            //         }
                            //     }}
                        ]
                    ],
                    page: false,
                    done: function (res, curr, count) {
                        $view.find('#headTotalCount').text('检索 ' + count + ' 条数据')
                        $.each(res.data, function (i, val) {
                            if (val.proType == '0') {// 详设
                                $.each(xsProfessionUserList, function (i, d) {
                                    if (val.proId == d.professionId) {
                                        xsMap.set(val.proId, 0)
                                    }
                                })
                            } else {// 生设
                                $.each(ssProfessionUserList, function (i, d) {
                                    if (val.proId == d.professionId) {
                                        ssMap.set(val.proId, 0)
                                    }
                                })
                            }
                            proNoToIdMap.set(val.porHeadNo, val.proId);
                            proNoToTypeMap.set(val.porHeadNo, val.proType);

                            if (val.uploadStatus == '1') {
                                $('tr[data-index="' + i + '"]').addClass('updateColorClass');
                            }
                        })
                    }
                    // limits: [10,20,30, 50, 100],
                    // limit: 10,
                })
            };

            table.on('edit(porHeadMaterialTable)', function (d) {
                let data = d.data
                data.produceDate = null; //后台转换报错
                data.protocolDate = null;//后台转换报错
                let haveEditShiro = false;
                if (data.proType == '0') {// 详设
                    haveEditShiro = xsMap.get(data.proId) != 0 ? false : true;
                } else {// 生设
                    haveEditShiro = ssMap.get(data.proId) != 0 ? false : true;
                }
                if (xsMap.size == 0 && ssMap.size == 0) {
                    haveEditShiro = true;
                }
                if (!haveEditShiro) {
                    var field = d.field;
                    var oldValue = d.oldValue;
                    var update = {};
                    update[field] = oldValue;
                    d.update(update, true)
                    layer.tips('当前数据您没有操作权限', this, { tips: 1 });
                } else {
                    febs.post(ctx + 'por/porHeadMaterialPurchase/update', data, function (da) {
                        if (da.code != 200) {
                            febs.alert.fail('修改失败');
                        }
                        $(d.tr[0]).removeClass('edited')
                    })
                }
            })
            table.on('sort(porHeadMaterialTable)', function (d) {
                let param = d.config.where;
                let serverUrl = d.config.url;
                param.order = d.type;
                param.orderField = d.field;
                let shipId = shipNos.getValue('valueStr');
                if (shipId === '') {
                    febs.alert.warn('请选择船号');
                    return false;
                } else {
                    let shipId = shipNos.getValue('value')[0];
                    tableIns.reload(
                        {
                            url: serverUrl,
                            where: param,
                            // page: {curr: 1}
                        });

                }
            })

            function createTable2(data) {
                tableInsTwo = febs.table.init({
                    elem: $view.find('#protocolList2'),
                    id: 'protocolList2',
                    data: data,
                    // toolbar: '#materialProtocolListMangerToolbar',
                    defaultToolbar: [],
                    height: 150, // 设置固定高度，让layui处理滚动
                    cols: [
                        [
                            { fixed: "left", type: 'checkbox' },
                            { fixed: "left", field: 'plmpId', title: "主键id", hide: true },
                            {
                                field: 'porHeadNo',
                                fixed: 'left',
                                title: 'POR编号',
                                align: 'center',
                                width: 120,
                                templet: function (d) {
                                    return '<div style="text-align: left;">' + d.porHeadNo + '</div>'
                                }
                            },
                            { field: 'lineNo', fixed: "left", title: '行项号', align: 'center', width: 80, sort: true },
                            {
                                field: 'baiscMaterialNo',
                                fixed: "left",
                                title: '物资编码',
                                align: 'center',
                                width: 115,
                                templet(d) {
                                    return '<div style="text-align: left;">' + d.baiscMaterialNo + '</div>'
                                }
                            },
                            {
                                field: 'materialDesc',
                                fixed: "left",
                                title: '名称及规格',
                                width: 158,
                                align: 'center',
                                edit: function (d) {
                                    if (editFlg && d.sapStatus != '1' && d.type == 1) {
                                        return 'text'
                                    }
                                },
                                templet(d) {
                                    return '<div style="text-align: left;">' + d.materialDesc + '</div>'
                                }
                            },
                            {
                                field: 'weightUnit', title: '单位', width: 80, align: 'center', templet: function (d) {
                                    if (d.type == 1) {
                                        return '<div id="XM-' + d.plmpId + '" class="por-weight"></div>'
                                    } else {
                                        return d.porUnitItemCode ? d.porUnitItemCode : ""
                                    }
                                }
                            },
                            {
                                field: 'weight', title: '单位重量', width: 93, align: 'center', edit: function (d) {
                                    if (editFlg && d.sapStatus != '1' && d.type == 1) {
                                        return 'text'
                                    }
                                }
                            },
                            {
                                field: 'amount',
                                title: '<span style="color: #1b83f0;font-weight: bold">POR数量</span>',
                                width: 80,
                                align: 'center',
                                edit: function (d) {
                                    if (editFlg && d.sapStatus != '1' && d.appendixFlg != '1') {
                                        return 'text'
                                    }
                                },
                                templet: function (d) {
                                    if (d.appendixFlg == '0') {
                                        if (d.useAmount == 0) {
                                            return '<span lay-event="showAppendix" style="text-decoration: underline;">' + (d.amount) + '</span>'
                                        } else {
                                            return d.amount
                                        }
                                    } else {
                                        return '<span lay-event="showAppendix" style="text-decoration: underline;">' + (d.amount) + '</span>'
                                    }

                                }
                            },
                            {
                                field: 'appendixFlg',
                                title: '附件号标记',
                                align: 'center',
                                width: 80,
                                minWidth: 70,
                                templet: function (d) {
                                    if (d.appendixFlg == 1) {
                                        return `<span class="layui-badge" style="background-color: #4dbeba">是</span>`
                                    } else {
                                        return `<span class="layui-badge" style="background-color: #e7d9cc">否</span>`

                                    }
                                }
                            },
                            {
                                field: 'remark',
                                title: '<span style="color: #1b83f0;font-weight: bold">备注</span>',
                                width: 92,
                                align: 'center',
                                edit: function (d) {
                                    if (editFlg && d.sapStatus != '1') {
                                        return 'text'
                                    }
                                },
                                templet: function (d) {
                                    return '<div style="text-align: left;">' + (d.remark != null ? d.remark : "") + '</div>'
                                }
                            },
                            { field: 'uploadStatusStr', title: '上传状态', minWidth: 80, align: 'center' },
                            { field: 'sapStatusStr', title: 'SAP状态', minWidth: 80, align: 'center' },
                            { field: 'purchaseStatus', title: '订单采购状态', minWidth: 110, align: 'center' },
                            { field: 'purchaseNo', title: '订单采购编号', minWidth: 110, align: 'center' },
                            { field: 'purchaseLineNo', title: '订单采购行项号', minWidth: 130, align: 'center' },
                            { field: 'purchasePerson', title: '订单采购人', minWidth: 110, align: 'center' },
                            { field: 'companyName', title: '中标厂家', minWidth: 100, align: 'center' },
                            { field: 'awardTime', title: '中标时间', minWidth: 100, align: 'center' },
                            { field: 'purchaseAmount', title: '订货数量', width: 80, align: 'center' },
                            { field: 'stockAmount', title: '入库数量', width: 80, align: 'center' },
                            {
                                field: 'useAmount',
                                title: '使用数量(PML)',
                                style: 'color:#1b83f0;cursor:pointer',
                                width: 130,
                                align: 'center',
                                templet: function (d) {
                                    return '<span lay-event="detailTray" style="text-decoration: underline">' + (d.useAmount) + '</span>'
                                }
                            },
                            {
                                title: '使用剩余数量', width: 110, align: 'center', templet: function (d) {
                                    if (null != d.amount && Number(d.amount) > 0) {
                                        //总数 - 使用数量 = 剩余数量
                                        return Number(d.amount) - Number(d.useAmount) - Number(d.restAmount) - Number(d.previousAmount);
                                    } else {
                                        return 0;
                                    }
                                }
                            },
                            {
                                field: 'restAmount',
                                title: '替代物资数量',
                                width: 90,
                                align: 'center',
                                templet: function (d) {
                                    if (d.appendixFlg == '1') {
                                        return '<span lay-event="showAppendixBdNum" style="text-decoration: underline;">' + (d.restAmount) + '</span>'
                                    } else {
                                        return d.restAmount
                                    }
                                }
                            },
                            { field: 'previousAmount', title: '移库数量', width: 90, align: 'center' },
                            {
                                field: 'attachmentFlg',
                                title: '附图标识',
                                width: 80,
                                align: 'center',
                                templet: function (d) {
                                    if (d.attachmentFlg == 1) {
                                        //是
                                        return `<span class="layui-badge" style="background-color: #4dbeba">是</span>`
                                    } else {
                                        //否
                                        return `<span class="layui-badge" style="background-color: #e7d9cc">否</span>`
                                    }


                                }
                            },
                            {
                                field: 'status',
                                title: '操作',
                                width: 90,
                                fixed: 'right',
                                align: 'center',
                                templet: function (d) {
                                    if (uploadFlg) {
                                        let proId = proNoToIdMap.get(d.porHeadNo);
                                        let proType = proNoToTypeMap.get(d.porHeadNo);
                                        if (xsMap.size == 0 && ssMap.size == 0) {
                                            if (d.sapStatus == '1') {
                                                return '    <div class="layui-clear-space">\n' +
                                                    '        <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="uploadView">材料单</a>\n' +
                                                    '    </div>'
                                            } else {
                                                return '    <div class="layui-clear-space">\n' +
                                                    '        <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="upload">材料单</a>\n' +
                                                    '    </div>'
                                            }
                                        } else if (proType == '0' && xsMap.get(proId) != 0) {// xs
                                            return '    <div class="layui-clear-space">\n' +
                                                '        <a class="layui-btn layui-btn-xs " lay-event="uploadView">材料单</a>\n' +
                                                '    </div>'
                                        } else if (proType == '1' && ssMap.get(proId) != 0) {
                                            return '    <div class="layui-clear-space">\n' +
                                                '        <a class="layui-btn layui-btn-xs " lay-event="uploadView">材料单</a>\n' +
                                                '    </div>'
                                        }
                                        if (d.sapStatus == '1') {
                                            return '    <div class="layui-clear-space">\n' +
                                                '        <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="uploadView">材料单</a>\n' +
                                                '    </div>'
                                        } else {
                                            return '    <div class="layui-clear-space">\n' +
                                                '        <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="upload">材料单</a>\n' +
                                                '    </div>'
                                        }
                                    } else {
                                        return '    <div class="layui-clear-space">\n' +
                                            '        <a class="layui-btn layui-btn-xs layui-btn-disabled" >材料单</a>\n' +
                                            '    </div>'
                                    }
                                }
                            }
                        ]
                    ],
                    page: false,
                    // limits: [10,20,30, 50, 100],
                    limit: 10,
                    done: function (res, curr, count) {
                        $view.find('#lineTotalCount').text('检索 ' + count + ' 条数据')
                        let disable;
                        if (headStatus == 1) {
                            disable = true
                        } else {
                            disable = false
                        }
                        var cells = document.querySelectorAll('div[lay-id="protocolList2"] .layui-table-cell');
                        cells.forEach(item => {
                            item.style.height = '35px';
                        })
                        var xmCells = document.querySelectorAll('div[lay-id="protocolList2"] .por-weight');
                        xmCells.forEach(item => {
                            item.parentElement.style.overflow = 'visible';
                        })
                        res.data.forEach(t => {
                            if (t.type == 1) {
                                let arr = [];
                                $.each(units, function (i, v) {
                                    if (t.weightUnit == v.id) {
                                        arr.push({
                                            name: v.unitItemCode + ' ' + v.unitItemName,
                                            value: v.id,
                                            selected: true
                                        })
                                    } else {
                                        arr.push({
                                            name: v.unitItemCode + ' ' + v.unitItemName,
                                            value: v.id,
                                        })
                                    }

                                })
                                unitSelect = xmSelect.render({
                                    el: '#XM-' + t.plmpId,
                                    radio: true,
                                    clickClose: true,
                                    direction: 'auto',
                                    height: '100px',
                                    disabled: disable,
                                    filterable: true,
                                    size: 'mini',
                                    model: {
                                        label: { type: 'text' }
                                    },
                                    data: arr,
                                    template({ item }) {
                                        return item.name
                                    },
                                    on: function (data) {
                                        t.weightUnit = data.change[0].value
                                        let index = $view.find('#XM-' + t.plmpId).closest('tr').data('index');
                                        table.setRowChecked('protocolList2', {
                                            index: index,
                                            checked: true
                                        })
                                    }
                                })
                            }
                        })
                        var options = this;
                        // 获取当前行数据
                        table.getRowData = function (tableId, elem) {
                            var index = $(elem).closest('tr').data('index');
                            return table.cache[tableId][index] || {};
                        };
                        // 原生 select 事件
                        var tableViewElem = this.elem.next();
                        tableViewElem.find('.select-demo-primary').on('change', function () {
                            $(this).closest('tr').addClass('edited')
                            var value = this.value; // 获取选中项 value
                            var data = table.getRowData(options.id, this); // 获取当前行数据(如 id 等字段，以作为数据修改的索引)
                            // 更新数据中对应的字段
                            data.weightUnit = value;
                        });
                    }
                })
            }

            // 初始化移库表格
            function createTable3() {
                previousTableIns = febs.table.init({
                    elem: $view.find('#previousShipMaterialTable'),
                    // height: $view.find("#previousShipMaterialDiv").height(), // 移除固定高度，让表格自适应容器
                    id: 'previousShipMaterialTable',
                    css: [ // 重设当前表格样式
                        '.layui-table-tool-temp{padding-right: 145px;}'
                    ].join(''),
                    cellMinWidth: 80,
                    data: [],
                    cols: [[
                        { type: 'checkbox' },
                        { field: 'pmadiId', hide: true },
                        { field: 'porShipNo', title: '当前船', align: 'center', minWidth: 120 },
                        { field: 'applyNo', title: '移库单', align: 'center', minWidth: 120 },
                        {
                            field: 'uploadStatus',
                            title: '移库状态',
                            align: 'center',
                            minWidth: 120,
                            templet: function (d) {
                                if (d.uploadStatus) {
                                    if (String(d.uploadStatus) === '0') {
                                        return '未移库';
                                    } else if (String(d.uploadStatus) === '1') {
                                        return '已移库';
                                    } else if (String(d.uploadStatus) === '2') {
                                        return '移库失败';
                                    }
                                } else {
                                    return '';
                                }
                            }
                        },
                        { field: 'dstShipNo', title: '目标船', align: 'center', minWidth: 120 },
                        { field: 'porHeadNo', title: 'porNo', align: 'center', minWidth: 120 },
                        { field: 'porHeadDesc', title: 'por描述', align: 'center', minWidth: 120 },
                        { field: 'pptdCodeName', title: 'por专业', align: 'center', minWidth: 120 },
                        { field: 'lineNo', title: 'LineNo', align: 'center', minWidth: 120 },
                        { field: 'basicMaterialNo', title: '物资编码', align: 'center', minWidth: 120 },
                        { field: 'basicMaterialDesc', title: '物资描述', align: 'center', minWidth: 120 },
                        { field: 'amount', title: '转移数量', align: 'center', minWidth: 120 },
                        {
                            field: 'useAmount', title: '使用数量', align: 'center', minWidth: 120, templet: function (d) {
                                if (d.useAmount != null && d.useAmount !== '' && d.useAmount !== undefined && d.useAmount > 0) {
                                    return `<span style="text-decoration: underline;" lay-event="trayUseDetail">${d.useAmount}</span>`;
                                } else {
                                    return '0';
                                }
                            }
                        },
                        { field: 'principalUserName', title: '负责人', align: 'center', minWidth: 120 },
                    ]],
                    page: false,
                    done: function (res, curr, count) {
                        $view.find('#previousTotalCount').text(`检索 ${count} 条数据`);
                    }
                })
            }

            table.on('row(porHeadMaterialTable)', function (d) {
                let tr = d.tr;
                $view.find('.showBgColor').removeClass('showBgColor')
                tr.removeClass('showBgColor')
                tr.addClass('showBgColor')

                headStatus = d.data.sapStatus
                if (d.data.proType == '0') {
                    onRowHaveEditShiro = xsMap.get(d.data.proId) != 0 ? false : true;
                } else {
                    onRowHaveEditShiro = ssMap.get(d.data.proId) != 0 ? false : true;
                }
                if (xsMap.size == 0 && ssMap.size == 0) {
                    onRowHaveEditShiro = true;
                }
                let params = getQueryParams();
                params.phmpId = d.data.phmpId;
                params.porHeadNo = '';
                phmpId = d.data.phmpId;
                headUploadStatus = d.data.uploadStatus;
                linePhDesc = d.data.porHeadDesc;
                porType = d.data.proType
                porId = d.data.porId
                tableInsTwo.reload({
                    where: params,
                    url: ctx + 'por/porLineMaterialPurchase/list',
                    // page:{curr:1}
                })

            })


            //触发排序事件
            table.on('sort(porLineMaterialTable)', function (obj) {
                if (getQueryParamsTwo().phmpId !== undefined && getQueryParamsTwo().phmpId != null) {
                    let param = getQueryParamsTwo()
                    param.field = obj.field;
                    param.order = obj.type;//desc;asc;null
                    // tableInsTwo.
                    tableInsTwo.reload({
                        initSort: obj,
                        where: param,
                        url: ctx + 'por/porLineMaterialPurchase/list',
                        // page: {curr: 1}
                    });
                } else {// 应对物资编码和描述有值的场景
                    var data = table.cache['protocolList2'];
                    data.sort(function (a, b) {
                        if (obj.type == 'asc') {
                            return a[obj.field] - b[obj.field];
                        } else {
                            return b[obj.field] - a[obj.field];
                        }
                    })
                    table.renderData('porLineMaterialTable');
                }
            })
            //switch事件
            form.on('switch(restFlgChange)', function (obj) {

                let plmpId = $(this).attr("plmpId");
                let amount = $(this).attr("amount");
                let restAmount = $(this).attr("restAmount");
                let remark = $(this).attr("remark");
                let restFlg = $(this).attr("restFlg");
                if (restFlg != 0) {
                    restFlg = 0;
                } else {
                    restFlg = 1;
                }
                data = {
                    plmpId, amount, restAmount, remark, restFlg
                }
                porLineRestAmount({ data });
            })


            // 头部工具栏事件
            table.on('toolbar(porHeadMaterialTable)', function (obj) {
                let options = obj.config; // 获取当前表格属性配置项
                let checkStatus = table.checkStatus(options.id); // 获取选中行相关数据
                let data = checkStatus.data;
                // 根据不同的事件名进行相应的操作
                switch (obj.event) { // 对应模板元素中的 lay-event 属性值
                    case 'add':
                        if (!shipNos.getValue('value')[0]) {
                            febs.alert.warn('请先选择船号');
                            return false;
                        }
                        febs.modal.open('新增材料采购清单', 'por/porHeadMaterialManageAdd', {
                            btn: ['提交'],
                            area: ['600px', '530px'],
                            data: {
                                shipId: shipNos.getValue('value')[0], shipTypeId: shipNos.getValue()[0].typeId,
                                xsProfessionUserList: xsProfessionUserList,
                                ssProfessionUserList: ssProfessionUserList
                            },
                            yes: function (e) {
                                $('#porHeadMaterialDict-edit').find('#submit').trigger('click');
                            }
                        })
                        break;
                    case 'delete':
                        let deleteInfo;
                        let flg = true;
                        if (data.length < 1) {
                            febs.alert.warn('请选择需要删除的数据');
                            return false;
                        }
                        let phmpIds = [];
                        layui.each(data, function (key, item) {
                            if (item.sapStatus != '0') {
                                febs.alert.warn(item.porHeadNo + '中存在SAP已处理的物资，不能删除');
                                flg = false;
                                return false;
                            }
                            let haveEditShiro = false;
                            if (item.proType == '0') {// 详设
                                haveEditShiro = xsMap.get(item.proId) != 0 ? false : true;
                            } else {// 生设
                                haveEditShiro = ssMap.get(item.proId) != 0 ? false : true;
                            }
                            if (xsMap.size == 0 && ssMap.size == 0) {
                                haveEditShiro = true;
                            }
                            if (!haveEditShiro) {
                                febs.alert.warn(item.porHeadNo + '数据您没有操作权限');
                                flg = false;
                                return false;
                            }
                            phmpIds.push(item.phmpId)
                        });
                        if (flg) {// 允许同时删除head下line
                            febs.getSyn(ctx + 'por/porLineMaterialPurchase/count', { phmpIds: phmpIds.join(',') }, function (data) {
                                deleteInfo = data.data
                            })
                            if (deleteInfo.length > 0) {
                                febs.modal.confirm('删除', '是否同时删除选中的采购清单及其附属物资', function () {
                                    febs.get(ctx + 'por/porHeadMaterialPurchase/delete', { pmphIds: phmpIds.join(',') }, function (e) {
                                        if (e.code == '200') {
                                            layer.closeAll();
                                            febs.alert.success('删除完成');
                                            $view.find('#queryOther1').trigger('click');
                                            createTable2([]);
                                        }
                                    })
                                })
                                flg = false;
                                return false
                            }
                        }
                        if (flg) {
                            febs.modal.confirm('删除', '是否确认删除选中的采购清单', function () {
                                febs.get(ctx + 'por/porHeadMaterialPurchase/delete', { pmphIds: phmpIds.join(',') }, function (e) {
                                    if (e.code == '200') {
                                        layer.closeAll();
                                        febs.alert.success('删除完成');
                                        $view.find('#queryOther1').trigger('click');
                                    }
                                })
                            })
                        }

                        break;
                    case 'import':
                        if (shipNos.getValue('valueStr') == '') {
                            febs.alert.warn('请选择船号后再导入');
                            return false
                        } else {
                            febs.modal.open('导入', 'por/importPorHeadMaterial', {
                                btn: ['开始上传'],
                                area: ['700px', '480px'],
                                data: { shipId: shipNos.getValue('valueStr'), shipNo: shipNos.getValue('nameStr') },
                                yes: function (e) {
                                    $('#porHeadMaterialImport').find('#test9').trigger('click');
                                }
                            });
                        }
                        break;
                    case 'export':
                        if (shipNos.getValue('value').length === 0) {
                            febs.alert.warn('请选择船号后再导出');
                            return false
                        }
                        if (data.length > 0) {
                            //勾选导出
                            let phmpIds = "";
                            layui.each(data, function (key, item) {
                                phmpIds = phmpIds + "," + item.phmpId
                            });
                            febs.download(ctx + 'por/porHeadMaterialPurchase/excel', { phmpStr: phmpIds }, 'POR材料采购导出' + new Date().getTime() + '.xlsx');
                        } else {
                            //当前页导出
                            let param = getQueryParams();
                            param.pageSize = $mainTable.find('.layui-laypage-limits').find("option:selected").val()
                            param.pageNum = $mainTable.find('.layui-laypage-em').next().html()
                            febs.download(ctx + 'por/porHeadMaterialPurchase/excel', param, 'POR材料采购导出' + new Date().getTime() + '.xlsx');
                        }
                        break;
                    case 'copyMaterial':
                        febs.modal.open('材料采购复制', 'por/porHeadMaterialManageCopy', {
                            btn: '提交',
                            area: ['700px', '550px'],
                            data: {
                                source: 'material',
                                ssProfessionUserList: ssProfessionUserList,
                                xsProfessionUserList: xsProfessionUserList
                            },
                            yes: function (e) {
                                $('#copyCarryNo').find('#submit').trigger('click');
                            }
                        })
                        break;
                    case 'up':
                        if (shipNos.getValue('value').length === 0) {
                            febs.alert.warn('请选择船号后再上传SAP');
                            return false
                        }
                        if (data.length == 0) {
                            febs.alert.warn('请选择数据后再上传SAP');
                            return false
                        }
                        let sapFlg = true;
                        let sapParam = {};
                        let headIds = [];

                        $.each(data, function (i, v) {
                            if (v.produceDate == null) {
                                sapFlg = false;
                                febs.alert.warn('选中的POR编号里有生产需求日为空不能上传SAP');
                                return false
                            } else if (v.uploadStatusStr == "全部上传" || v.uploadStatus == "2") {
                                sapFlg = false;
                                febs.alert.warn('该POR下物资SAP已全部上传，无需重新上传SAP');
                                return false
                            }

                            let haveEditShiro = false;
                            if (v.proType == '0') {// 详设
                                haveEditShiro = xsMap.get(v.proId) != 0 ? false : true;
                            } else {// 生设
                                haveEditShiro = ssMap.get(v.proId) != 0 ? false : true;
                            }
                            if (xsMap.size == 0 && ssMap.size == 0) {
                                haveEditShiro = true;
                            }
                            if (!haveEditShiro) {
                                febs.alert.warn(v.porHeadNo + '数据您没有操作权限');
                                sapFlg = false;
                                return false;
                            }
                            headIds.push(v.phmpId)
                        })
                        if (sapFlg) {
                            sapParam = {
                                porMainIdList: headIds,
                                dataType: "2"
                            }
                            febs.post(ctx + 'sap/porSapValid', sapParam, function (res) {
                                let index = layer.msg('SAP已在上传中，请耐心等待', { icon: 16, shade: 0.01, time: 10800000 })
                                if (res.code == 200) {
                                    let validRes = res.data;
                                    if (validRes.errorList.length > 0 && validRes.errorList != null) {
                                        layer.close(index);
                                        let html = '<div style="display: flex;flex-direction: column;">';
                                        validRes.errorList.forEach(item => {
                                            html += '<label>POR编号:' + item.porNo + ',错误原因：' + item.errMessage + '</label>'
                                        })
                                        html = html + '</div>';
                                        layer.open({
                                            title: 'SAP上传验证',
                                            content: html
                                        });
                                    } else {
                                        febs.post(ctx + 'sap/sapRecordSync', sapParam, function (data) {
                                            if (data.code == 200) {
                                                if (data.data.resultFlg) {
                                                    layer.close(index)
                                                    // febs.alert.success(data.message);
                                                    layer.alert(data.data.msg)
                                                    $query.trigger("click")
                                                } else {
                                                    layer.close(index)
                                                    // febs.alert.error(data.message);
                                                    layer.open({
                                                        title: '上传SAP失败信息',
                                                        type: 1,
                                                        area: ['20%', '40%'],
                                                        content: '<div style="padding: 16px;">' + data.data.msg + '</div>',
                                                        success: function () {
                                                        },
                                                    });
                                                }
                                            }
                                        });
                                    }
                                } else {
                                    layer.close(index);
                                    febs.alert.error(res.message);
                                }
                            });
                        }
                        break;
                    case 'updatePurchase':
                        updatePurchase(obj)
                        break;
                    case 'saveBatch':
                        let infos = [];
                        let saveData;
                        let msg = '是否确定批量保存已修改的数据';
                        let thisCache = table.cache['protocolList'] || {}
                        let checkStatus = table.checkStatus('protocolList');
                        if (checkStatus.data.length === 0) {
                            saveData = thisCache;
                            msg = '未勾选要保存的数据，将全部保存';
                        } else {
                            saveData = checkStatus.data;
                        }
                        if (saveData === null || saveData.length === 0) {
                            febs.alert.warn('当前页面无数据，无需保存');
                            return false;
                        }
                        $.each(saveData, function (i, v) {
                            v.protocolDate = null;
                            v.produceDate = null;
                            infos.push(v)
                        })
                        febs.modal.confirm('批量保存', msg, function () {
                            febs.postArray(ctx + 'por/porHeadMaterialPurchase/updateBatch', infos, function () {
                                febs.alert.success('修改成功');
                                // $(obj.tr[0]).removeClass('edited')
                                $query.trigger("click")
                            })
                        })
                        break;
                    case 'confirmSap':
                        if (shipNos.getValue('value').length === 0) {
                            febs.alert.warn('请选择船号后再确认SAP');
                            return false
                        }
                        if (data.length == 0) {
                            febs.alert.warn('请选择数据后再确认SAP');
                            return false
                        }
                        let flgSap = true;
                        let infosSap = [];

                        $.each(data, function (i, v) {
                            if (v.statusTrans != "已上传") {
                                flgSap = false;
                                febs.alert.warn('未上传的POR编号不能确认SAP');
                                return false
                            }

                            let haveEditShiro = false;
                            if (v.proType == '0') {// 详设
                                haveEditShiro = xsMap.get(v.proId) != 0 ? false : true;
                            } else {// 生设
                                haveEditShiro = ssMap.get(v.proId) != 0 ? false : true;
                            }
                            if (xsMap.size == 0 && ssMap.size == 0) {
                                haveEditShiro = true;
                            }
                            if (!haveEditShiro) {
                                febs.alert.warn(v.porHeadNo + '数据您没有操作权限');
                                flg = false;
                                return false;
                            }
                            let fv = {};
                            fv.phmpId = v.phmpId;
                            fv.sapStatus = "1";
                            infosSap.push(fv)
                        })
                        if (flgSap) {
                            febs.modal.confirm('确认', '请确认是否更改选中数据的SAP状态', function () {
                                febs.postArray(ctx + 'por/porHeadMaterialPurchase/updateBatch', infosSap, function () {
                                    febs.alert.success('修改成功');
                                    // $(obj.tr[0]).removeClass('edited')
                                    $view.find('#queryOther1').trigger('click');
                                })
                            })
                        }
                        break;
                    case 'confirmSapBack':
                        if (shipNos.getValue('value').length === 0) {
                            febs.alert.warn('请选择船号后再撤销确认SAP');
                            return false
                        }
                        if (data.length == 0) {
                            febs.alert.warn('请选择数据后再撤销确认SAP');
                            return false
                        }
                        let flgSapBack = true;
                        let infosSapBack = [];

                        $.each(data, function (i, v) {
                            if (v.statusTrans != "已上传") {
                                flgSapBack = false;
                                febs.alert.warn('未上传的POR编号不能撤销确认SAP');
                                return false
                            }

                            let haveEditShiro = false;
                            if (v.proType == '0') {// 详设
                                haveEditShiro = xsMap.get(v.proId) != 0 ? false : true;
                            } else {// 生设
                                haveEditShiro = ssMap.get(v.proId) != 0 ? false : true;
                            }
                            if (xsMap.size == 0 && ssMap.size == 0) {
                                haveEditShiro = true;
                            }
                            if (!haveEditShiro) {
                                febs.alert.warn(v.porHeadNo + '数据您没有操作权限');
                                flg = false;
                                return false;
                            }
                            let fv = {};
                            fv.phmpId = v.phmpId;
                            fv.sapStatus = "0";
                            infosSapBack.push(fv)
                        })
                        if (flgSapBack) {
                            febs.modal.confirm('确认', '请确认是否更改选中数据的SAP状态', function () {
                                febs.postArray(ctx + 'por/porHeadMaterialPurchase/updateBatch', infosSapBack, function () {
                                    febs.alert.success('修改成功');
                                    // $(obj.tr[0]).removeClass('edited')
                                    $view.find('#queryOther1').trigger('click');
                                })
                            })
                        }
                        break;
                    case 'selectLeftInfos':
                        if (!shipNos.getValue('value')[0]) {
                            febs.alert.warn('请先选择船号');
                            return false;
                        }
                        // 查询所有托盘未匹配篇POR
                        febs.modal.open('未匹配POR的托盘统计', 'por/porMaterialManagerView/leftByProInfos', {
                            btn: ['生成POR'],
                            area: ['70%', '80%'],
                            data: {
                                shipId: shipNos.getValue('value')[0],
                                shipArr,
                                ssProfessionUserList: ssProfessionUserList,
                                xsProfessionUserList: xsProfessionUserList,
                                type: 'material'
                            },
                            yes: function (e) {
                                $('#leftByProInfos').find('#leftByPorSubmit').trigger('click');
                            }
                        })
                        break;
                }
            });

            function updatePurchase(obj) {
                if (shipNos.getValue('value').length === 0) {
                    febs.alert.warn('请选择船号后再同步采购状态');
                    return false
                }
                let checkStatus = table.checkStatus('protocolList');
                if (checkStatus.data.length == 0) {
                    febs.alert.warn('请选择数据同步采购状态');
                    return false
                }
                let flg = true;
                let sapParam = {};
                let headIds = [];
                let errList = [];
                $.each(checkStatus.data, function (i, v) {

                    let haveEditShiro = false;
                    if (v.proType == '0') {// 详设
                        haveEditShiro = xsMap.get(v.proId) != 0 ? false : true;
                    } else {// 生设
                        haveEditShiro = ssMap.get(v.proId) != 0 ? false : true;
                    }
                    if (xsMap.size == 0 && ssMap.size == 0) {
                        haveEditShiro = true;
                    }
                    if (!haveEditShiro) {
                        febs.alert.warn(v.porHeadNo + "数据您没有操作权限");
                        flg = false;
                        return false;
                    }
                    if (v.status == null || v.status == '0') {
                        errList.push(v.phmpId)
                    } else {
                        headIds.push(v.phmpId)
                    }
                })
                if (flg) {
                    if (errList.length > 0) {
                        febs.modal.confirm("是否继续同步", '勾选数据中存在未上传的POR，是否继续同步?', function () {
                            updatePurchaseSap(headIds)
                        }, function () {
                            layer.closeAll();
                        });
                    } else {
                        updatePurchaseSap(headIds)
                    }
                }
            }

            function updatePurchaseSap(headIds) {
                let sapParam = {
                    porMainIdList: headIds,
                    dataType: "9"
                }
                febs.post(ctx + 'sap/porSapValid', sapParam, function (res) {
                    let index = layer.msg('同步采购状态中，请耐心等待', { icon: 16, shade: 0.01, time: 10800000 })
                    if (res.code == 200) {
                        febs.post(ctx + 'sap/sapRecordSync', sapParam, function (data) {
                            if (data.code == 200) {
                                if (data.data.resultFlg) {
                                    layer.close(index)
                                    // febs.alert.success(data.message);
                                    layer.alert(data.data.msg)
                                    $view.find('#queryOther1').trigger('click');
                                    $view.find('#queryOther2').trigger('click');
                                } else {
                                    layer.close(index)
                                    // febs.alert.error(data.message);
                                    layer.open({
                                        title: '上传SAP失败信息',
                                        type: 1,
                                        area: ['20%', '40%'],
                                        content: '<div style="padding: 16px;">' + data.data.msg + '</div>',
                                        success: function () {
                                        },
                                    });
                                }
                            }
                        });
                    } else {
                        layer.close(index);
                        febs.alert.error(res.message);
                    }
                });
            }

            table.on('toolbar(porLineMaterialTable)', function (obj) {
                let options = obj.config; // 获取当前表格属性配置项
                let checkStatus = table.checkStatus(options.id); // 获取选中行相关数据
                let data = checkStatus.data;
                if (!onRowHaveEditShiro) {
                    febs.alert.warn('当前数据您没有操作权限');
                    return;
                }
                // 根据不同的事件名进行相应的操作
                switch (obj.event) { // 对应模板元素中的 lay-event 属性值
                    case 'addLine':
                        // if (headStatus == 1){
                        //     febs.alert.warn('已确认数据不能再新增');
                        //     return;
                        // }
                        if (!phmpId) {
                            febs.alert.warn('请先选择por_head');
                            return false;
                        }
                        febs.modal.open('新增采购清单POR_LINE', 'por/porLineMaterialManageAdd', {
                            btn: ['提交'],
                            area: ['500px', '470px'],
                            data: { phmpId: phmpId, shipId: shipNos.getValue('value')[0] },
                            yes: function (e) {
                                $('#porLineMaterialDict-edit').find('#add').trigger('click');
                                tableIns.reloadData();
                            }
                        })
                        break;
                    case 'deleteLine':
                        if (headStatus == 1) {
                            febs.alert.warn('已确认数据不能删除');
                            return
                        }
                        if (data.length < 1) {
                            febs.alert.warn('请选择需要删除的数据');
                            return false;
                        }
                        let plmpIds = [];
                        let phmpIds = [];
                        layui.each(data, function (key, item) {
                            plmpIds.push(item.plmpId)
                            phmpIds.push(item.phmpId)
                        });
                        febs.modal.confirm('删除', '是否确认删除选中的采购清单', function () {
                            febs.get(ctx + 'por/porLineMaterialPurchase/delete', {
                                plmpIds: plmpIds.join(','),
                                phmpIds: phmpIds.join(',')
                            }, function (e) {
                                if (e.code == '200') {
                                    layer.closeAll();
                                    febs.alert.success('删除完成');
                                    // tableIns.reloadData();
                                    $view.find('#queryOther2').trigger('click');
                                }
                            })
                        })
                        break;
                    case 'importLine':
                        // if (headStatus == 1){
                        //     febs.alert.warn('已确认数据不能导入');
                        //     return
                        // }
                        febs.modal.open('导入', 'por/importPorLineMaterial', {
                            btn: ['开始上传'],
                            area: ['700px', '480px'],
                            data: { tableInsTwo: tableInsTwo },
                            yes: function (e) {
                                $('#porLineMaterialImport').find('#test9').trigger('click');
                            }
                        });
                        break;
                    case 'exportLine':
                        febs.download(ctx + 'shipBlock/exportExcel', getQueryParam(), '组立分段' + new Date().getTime() + '.xlsx');
                        break;
                    case 'saveBatchLine':
                        let infos = [];
                        let saveData;
                        let msg = '是否确定批量保存已修改的数据';
                        let thisCache = table.cache['protocolList2'] || {}
                        let checkStatus = table.checkStatus('protocolList2');
                        if (checkStatus.data.length === 0) {
                            saveData = thisCache;
                            msg = '未勾选要保存的数据，将全部保存';
                        } else {
                            saveData = checkStatus.data;
                        }
                        if (saveData === null || saveData.length === 0) {
                            febs.alert.warn('当前页面无数据，无需保存');
                            return false;
                        }
                        $.each(saveData, function (i, v) {
                            let vs = {
                                basicMaterialNo: v.baiscMaterialNo,
                                materialDesc: v.materialDesc,
                                weight: v.weight,
                                weightUnit: v.weightUnit
                            }
                            infos.push(vs)
                        })
                        febs.modal.confirm('批量保存', msg, function () {
                            febs.postArray(ctx + 'por/porLineMaterialPurchase/updateBatch', saveData, function () {
                                febs.postArray(ctx + 'por/porLineDeviceProtocol/updateNoStandardBatch', infos, function () {
                                    removeWindowLinstener();
                                    $view.find('#editFlagWithLine').val("false");
                                    febs.alert.success('保存成功');
                                    $view.find('#queryOther2').trigger('click');
                                })
                            })
                        })
                        break;
                    case 'saveBatchLineExit':
                        if (!onRowHaveEditShiro) {
                            febs.alert.warn('当前数据您没有操作权限');
                            return;
                        }

                        let infosExit = [];
                        let saveDataExit;
                        let msgExit = '是否确定批量保存已修改的数据';
                        let thisCacheExit = table.cache['protocolList2'] || {}
                        let checkStatusExit = table.checkStatus('protocolList2');
                        if (checkStatusExit.data.length === 0) {
                            saveDataExit = thisCacheExit;
                            msg = '未勾选要保存的数据，将全部保存';
                        } else {
                            saveDataExit = checkStatusExit.data;
                        }
                        if (saveDataExit === null || saveDataExit.length === 0) {
                            febs.alert.warn('当前页面无数据，无需保存');
                            return false;
                        }
                        $.each(saveDataExit, function (i, v) {
                            let vs = {
                                basicMaterialNo: v.baiscMaterialNo,
                                materialDesc: v.materialDesc,
                                weight: v.weight,
                                weightUnit: v.weightUnit
                            }
                            infosExit.push(vs)
                        })
                        // febs.modal.confirm('批量保存', msg, function () {
                        febs.postArray(ctx + 'por/porLineMaterialPurchase/updateBatch', saveDataExit, function () {
                            febs.postArray(ctx + 'por/porLineDeviceProtocol/updateNoStandardBatch', infosExit, function () {
                                // febs.alert.success('保存成功');
                                // $queryTwo.trigger('click');
                            })
                        })
                        // })
                        break;
                    case 'upBatch':
                        let plmps = table.checkStatus('protocolList2')
                        if (plmps.data.length == 0) {
                            febs.alert.warn('请至少选择一条数据');
                            return false;
                        }
                        // if (headStatus == 1){
                        //     febs.alert.warn('已确认的数据无法再次上传');
                        //     return false;
                        // }
                        let param = [];
                        let errList = [];
                        plmps.data.forEach(item => {
                            if (item.sapStatus == '1') {
                                errList.push(item.baiscMaterialNo)
                            }
                            param.push(item.plmpId)
                        })
                        if (errList.length > 0) {
                            let msg = '以下物资编码SAP已处理：' + errList.join('、') + ',无法上传材料单！';
                            layer.open({
                                title: '上传失败',
                                type: 1,
                                area: 'auto',
                                content: '<div style="padding: 16px;">' + msg + '</div>',
                                success: function () {
                                },
                            });
                            return false;
                        }
                        febs.modal.open('材料单上传', 'por/porLineMaterialManageUpload', {
                            btn: ['开始上传'],
                            area: ['750px', '500px'],
                            data: { plmpIds: param, tableInsTwo: tableInsTwo, phmpId },
                            yes: function (e) {
                                $('#febs-porUploadBacth').find('#uploadFiles').trigger('click');
                            }
                        })
                        break;
                }
            });

            $('#addLine').on('click', function () {
                if (!phmpId) {
                    febs.alert.warn('请先选择por_head');
                    return false;
                }
                if (!onRowHaveEditShiro) {
                    febs.alert.warn('当前数据您没有操作权限');
                    return;
                }
                if (headUploadStatus != '0') {
                    let headIds = [phmpId]
                    let sapParam = {
                        porMainIdList: headIds,
                        dataType: "9"
                    }
                    febs.post(ctx + 'sap/sapRecordSync', sapParam, function (d) {
                    });
                }
                febs.modal.open('新增采购清单POR_LINE', 'por/porLineMaterialManageAdd', {
                    btn: ['提交'],
                    area: ['500px', '470px'],
                    data: { phmpId: phmpId, shipId: shipNos.getValue('value')[0] },
                    yes: function (e) {
                        $('#porLineMaterialDict-edit').find('#add').trigger('click');
                        tableIns.reloadData();
                    }
                })
            })

            $('#deleteLine').on('click', function () {
                if (!phmpId) {
                    febs.alert.warn('请先选择por_head');
                    return false;
                }
                if (!onRowHaveEditShiro) {
                    febs.alert.warn('当前数据您没有操作权限');
                    return;
                }
                let checkStatus = table.checkStatus('protocolList2');
                let data = checkStatus.data
                if (data.length < 1) {
                    febs.alert.warn('请选择需要删除的数据');
                    return false;
                }
                let plmpIds = [];
                let phmpIds = [];
                let errMaterialNos = new Set();
                layui.each(data, function (key, item) {
                    errMaterialNos.add(item.baiscMaterialNo)
                    plmpIds.push(item.plmpId)
                    phmpIds.push(item.phmpId)
                });
                if (headUploadStatus != '0') {
                    let headIds = [phmpId]
                    let sapParam = {
                        porMainIdList: headIds,
                        dataType: "9"
                    }
                    febs.post(ctx + 'sap/sapRecordSync', sapParam, function (d) {
                        if (d.code == 200) {
                            if (d.data.resultFlg) {
                                if (d.data.materialNos.length > 0) {
                                    let res = d.data.materialNos.filter(item => errMaterialNos.has(item));
                                    if (res.length > 0) {
                                        let msg = '以下物资编码SAP已处理：' + res.join('、') + ',无法删除！';
                                        layer.open({
                                            title: '删除失败',
                                            type: 1,
                                            area: 'auto',
                                            content: '<div style="padding: 16px;">' + msg + '</div>',
                                            success: function () {
                                            },
                                        });
                                    } else {
                                        delLine(plmpIds, phmpIds)
                                    }
                                } else {
                                    delLine(plmpIds, phmpIds)
                                }
                            } else {
                                delLine(plmpIds, phmpIds)
                            }
                        }
                    });
                } else {
                    delLine(plmpIds, phmpIds)
                }

            })

            function delLine(plmpIds, phmpIds) {
                febs.modal.confirm('删除', '是否确认删除选中的采购清单', function () {
                    febs.get(ctx + 'por/porLineMaterialPurchase/delete', {
                        plmpIds: plmpIds.join(','),
                        phmpIds: phmpIds.join(',')
                    }, function (e) {
                        if (e.code == '200') {
                            layer.closeAll();
                            febs.alert.success('删除完成');
                            $view.find('#queryOther2').trigger('click');
                        }
                    })
                })
            }

            $('#importLine').on('click', function () {
                // if (headStatus == 1){
                //     febs.alert.warn('已确认数据不能导入');
                //     return
                // }
                febs.modal.open('导入', 'por/importPorLineMaterial', {
                    btn: ['开始上传'],
                    area: ['700px', '480px'],
                    data: { tableInsTwo: tableInsTwo },
                    yes: function (e) {
                        $('#porLineMaterialImport').find('#test9').trigger('click');
                    }
                });
            })

            $('#exportLine').on('click', function () {
                febs.download(ctx + 'shipBlock/exportExcel', getQueryParam(), '组立分段' + new Date().getTime() + '.xlsx');
            })

            $('#saveBatchLine').on('click', function () {
                let infos = [];
                let saveData;
                let msg = '是否确定批量保存已修改的数据';
                let thisCache = table.cache['protocolList2'] || {}
                let checkStatus = table.checkStatus('protocolList2');
                if (checkStatus.data.length === 0) {
                    saveData = thisCache;
                    msg = '未勾选要保存的数据，将全部保存';
                } else {
                    saveData = checkStatus.data;
                }
                if (saveData === null || saveData.length === 0) {
                    febs.alert.warn('当前页面无数据，无需保存');
                    return false;
                }
                $.each(saveData, function (i, v) {
                    let vs = {
                        basicMaterialNo: v.baiscMaterialNo,
                        materialDesc: v.materialDesc,
                        weight: v.weight,
                        weightUnit: v.weightUnit
                    }
                    infos.push(vs)
                })
                febs.modal.confirm('批量保存', msg, function () {
                    febs.postArray(ctx + 'por/porLineMaterialPurchase/updateBatch', saveData, function () {
                        febs.postArray(ctx + 'por/porLineDeviceProtocol/updateNoStandardBatch', infos, function () {
                            removeWindowLinstener();
                            $view.find('#editFlagWithLine').val("false");
                            febs.alert.success('保存成功');
                            $view.find('#queryOther2').trigger('click');
                        })
                    })
                })
            })

            $('#saveBatchLineExit').on('click', function () {
                let infosExit = [];
                let saveDataExit;
                let msgExit = '是否确定批量保存已修改的数据';
                let thisCacheExit = table.cache['protocolList2'] || {}
                let checkStatusExit = table.checkStatus('protocolList2');
                if (checkStatusExit.data.length === 0) {
                    saveDataExit = thisCacheExit;
                    // msg ='未勾选要保存的数据，将全部保存';
                } else {
                    saveDataExit = checkStatusExit.data;
                }
                if (saveDataExit === null || saveDataExit.length === 0) {
                    febs.alert.warn('当前页面无数据，无需保存');
                    return false;
                }
                $.each(saveDataExit, function (i, v) {
                    let vs = {
                        basicMaterialNo: v.baiscMaterialNo,
                        materialDesc: v.materialDesc,
                        weight: v.weight,
                        weightUnit: v.weightUnit
                    }
                    infosExit.push(vs)
                })
                // febs.modal.confirm('批量保存', msg, function () {
                febs.postArray(ctx + 'por/porLineMaterialPurchase/updateBatch', saveDataExit, function () {
                    febs.postArray(ctx + 'por/porLineDeviceProtocol/updateNoStandardBatch', infosExit, function () {
                        // febs.alert.success('保存成功');
                        // $queryTwo.trigger('click');
                    })
                })
                // })
            })

            $('#upBatch').on('click', function () {
                let plmps = table.checkStatus('protocolList2')
                if (!phmpId) {
                    febs.alert.warn('请先选择por_head');
                    return false;
                }
                if (!onRowHaveEditShiro) {
                    febs.alert.warn('当前数据您没有操作权限');
                    return;
                }
                if (plmps.data.length == 0) {
                    febs.alert.warn('请至少选择一条数据');
                    return false;
                }
                if (headStatus == 1) {
                    febs.alert.warn('已确认的数据无法再次上传');
                    return false;
                }
                let param = [];
                plmps.data.forEach(item => {
                    param.push(item.plmpId)
                })
                febs.modal.open('材料单上传', 'por/porLineMaterialManageUpload', {
                    btn: ['开始上传'],
                    area: ['750px', '500px'],
                    data: { plmpIds: param, tableInsTwo: tableInsTwo, phmpId },
                    yes: function (e) {
                        $('#febs-porUploadBacth').find('#uploadFiles').trigger('click');
                    }
                })
            })

            function addOrRemoveLins(event) {
                event.preventDefault();
            }

            window.addOrRemoveLins2 = addOrRemoveLins// 无奈 0.0

            function addWindowLinstener() {
                window.addEventListener('beforeunload', addOrRemoveLins);
            }

            function removeWindowLinstener() {
                window.removeEventListener('beforeunload', addOrRemoveLins);
            }

            table.on('row(porList)', function (obj) {
                // 标注当前点击行的选中状态
                obj.setRowChecked({
                    type: 'radio' // radio 单选模式；checkbox 复选模式
                });
                $view.find('input[name="id"]').val(obj.data.id)
                $view.find('input[name="goodsCode"]').val(obj.data.name)
                $view.find('input[name="goodsDesc"]').val(obj.data.desc)
            });

            // 更多查询点击事件
            $view.find('#moreQuery').on('click', function () {
                if ($view.find("#moreQueryDiv").hasClass("layui-hide")) {
                    $view.find("#moreQueryDiv").removeClass("layui-hide");
                } else {
                    $view.find("#moreQueryDiv").addClass("layui-hide");
                }
            });

            // 查询
            $query.on('click', function () {
                phmpId = null;
                linePhDesc = null;
                porId = null;
                porType = null;
                var params = getQueryParams()
                if (!params.shipId) {
                    febs.alert.warn("请选择对应船号")
                } else {
                    tableIns.reload({ url: ctx + 'por/porHeadMaterialPurchase/list', where: params });
                    if (params.basicMaterialDesc != "" || params.basicMaterialNo != "") {
                        tableInsTwo.reload({ url: ctx + 'por/porLineMaterialPurchase/list', where: params });
                    } else {
                        createTable2([]);
                    }
                    febs.get(ctx + 'por/previousMaterialApplyDetailInfo/getPreviousMaterialApplyDetailInfoList', getPreviousQueryParams(), function (e) {
                        const newData = e.data ? e.data : [];
                        previousTableIns.reload({ data: newData });
                    });
                }
            });

            $queryTwo.on('click', function () {
                var params = getQueryParamsTwo()
                if (!params.phmpId) {
                    febs.alert.warn("请选择对应POR_HEAD")
                } else {
                    tableInsTwo.reload({ url: ctx + 'por/porLineMaterialPurchase/list', where: params });
                }
            });

            // 移库查询按钮点击事件
            $view.find('#previousQuery').on('click', function () {
                febs.get(ctx + 'por/previousMaterialApplyDetailInfo/getPreviousMaterialApplyDetailInfoList', getPreviousQueryParams2(), function (e) {
                    const newData = e.data ? e.data : [];
                    previousTableIns.reload({ data: newData });
                });
            });

            //操作栏事件
            table.on('tool(porHeadMaterialTable)', function (obj) {
                switch (obj.event) {
                    case 'update':
                        updatePorHeadMaterial(obj);
                        break;
                    case 'show':
                        showPorLineMaterial(obj);
                        break;
                }
            })
            table.on('tool(porLineMaterialTable)', function (obj) {
                let data = obj.data;
                switch (obj.event) {
                    case 'update':
                        updatePorLineMaterial(obj);
                        break;
                    case 'detailTray':
                        openDetailTray(data);
                        break;
                    case 'upload':
                        uploadPorMaterial(obj);
                        break;
                    case 'uploadView':
                        uploadPorMaterialView(obj);
                        break;
                    case 'showAppendix':
                        showAppendixList(data);
                        break;
                    case 'showAppendixBdNum':
                        showAppendixBdNum(data);
                        break;
                }

            })

            function showAppendixList(obj) {
                if (obj.appendixFlg == '0' && obj.sapStatus == '1') {
                    return false
                }
                febs.modal.open('新增附件号', 'por/porLineMaterialManageAdd/addAppendix', {
                    btn: ['确定'],
                    area: ['530px', '680px'],
                    data: {
                        shipId: obj.shipId,
                        porId: obj.phmpId,
                        plmpId: obj.plmpId,
                        headStatus: obj.sapStatus,
                        amount: obj.amount,
                    },
                    yes: function (index, layero) {
                        $('#febs-materialAppendixAdd').find('#appendixSubmit').trigger('click');
                        layer.close(index)
                        tableInsTwo.reload()
                    }
                });
            }

            function showAppendixBdNum(data) {
                if (data.restFlg == 0 || headStatus == 0) {
                    return;
                }
                febs.modal.open('修改附件号标定', 'por/porLineMaterialManageAdd/updateAppendix', {
                    btn: ['确定'],
                    area: ['700px', '680px'],
                    data: {
                        shipId: data.shipId,
                        porId: data.phmpId,
                        plmpId: data.plmpId,
                    },
                    yes: function (index, layero) {
                        $('#febs-materialAppendixUpdate').find('#appendixSubmit').trigger('click');
                        layer.close(index)
                        tableInsTwo.reload()
                    }
                });
            }

            //查看物资被哪些托盘使用了
            function openDetailTray(data) {
                febs.modal.open('物资管理详情', 'por/porMaterialManageView/openDetailTray', {
                    area: ['100%', '100%'],
                    data: {
                        row: data
                    },
                });
            }

            function uploadPorMaterialView(obj) {
                febs.modal.open('材料单', 'por/porHeadMaterialManageUploadView', {
                    area: ['75%', '600px'],
                    data: { plmpId: obj.data.plmpId, phmpId: obj.data.phmpId, tableInsTwo: tableInsTwo },
                    end: function (e) {
                    },
                    beforeEnd: function (e) {
                    },
                    cancel: function (index, layero, that) {
                        $('#febs-porUpload2').find('#cancelSubmit').trigger('click');

                        // layer.confirm('关闭后未保存的内容将会丢失，确认关闭吗？',{
                        //     btn:['确定','取消']
                        // },function() {
                        //     layer.closeAll();
                        // })
                        //阻止默认关闭弹窗行为
                        return false;
                    }
                })
            }

            function uploadPorMaterial(obj) {
                febs.modal.open('材料单上传', 'por/porHeadMaterialManageUpload', {
                    area: ['75%', '600px'],
                    data: { plmpId: obj.data.plmpId, phmpId: obj.data.phmpId, tableInsTwo: tableInsTwo },
                    end: function (e) {
                    },
                    beforeEnd: function (e) {
                    },
                    cancel: function (index, layero, that) {
                        $('#febs-porUpload2').find('#cancelSubmit').trigger('click');

                        // layer.confirm('关闭后未保存的内容将会丢失，确认关闭吗？',{
                        //     btn:['确定','取消']
                        // },function() {
                        //     layer.closeAll();
                        // })
                        //阻止默认关闭弹窗行为
                        return false;
                    }
                })
            }

            function porLineRestAmount(obj) {
                let data = obj.data;
                febs.postArray(ctx + 'por/porLineMaterialPurchase/update', data, function () {
                    febs.alert.success('修改成功');
                    // $queryTwo.trigger('click');
                    $view.find('#queryOther2').trigger('click');
                })
            }

            function updatePorHeadMaterial(obj) {
                let data = obj.data
                data.produceDate = null; //后台转换报错
                data.protocolDate = null;//后台转换报错
                febs.modal.confirm('保存', '是否确定保存已修改的POR描述', function () {
                    febs.post(ctx + 'por/porHeadMaterialPurchase/update', data, function () {
                        febs.alert.success('保存成功');
                        $(obj.tr[0]).removeClass('edited')
                    })
                })

            }

            function updatePorLineMaterial(obj) {
                let data = obj.data; // 获得当前行数据
                data.basicMaterialNo = data.baiscMaterialNo
                febs.modal.confirm('保存', '是否确定保存已修改的数据', function () {
                    febs.postArray(ctx + 'por/porLineMaterialPurchase/update', data, function () {
                        febs.postArray(ctx + 'por/porLineDeviceProtocol/updateNoStandard', data, function () {
                            febs.alert.success('保存成功');
                            $(obj.tr[0]).removeClass('edited')
                            $view.find('#queryOther2').trigger('click');
                        })
                    })
                })
            }

            //Head 单元格编辑事件
            // table.on('edit(porHeadMaterialTable)', function (obj) {
            //     $(obj.tr[0]).addClass('edited')
            //     let index2 = $(this).closest('tr').data('index');
            //     table.setRowChecked('protocolList',{
            //         index: index2,
            //         checked:true
            //     })
            //     var field = obj.field; // 得到字段
            //     var value = obj.value; // 得到修改后的值
            //     var data = obj.data; // 得到所在行所有键值
            //     // 编辑后续操作，如提交更新请求，以完成真实的数据更新
            //     // 其他更新操作
            //     var update = {};
            //     update[field] = value;
            //     obj.update(update);
            // });
            //Line 单元格编辑事件
            table.on('edit(porLineMaterialTable)', function (obj) {
                let haveEditShiro = false;
                let proHeadNo = obj.data.porHeadNo;
                let proId = proNoToIdMap.get(proHeadNo);
                let proType = proNoToTypeMap.get(proHeadNo);
                if (proType == '0') {// 详设
                    haveEditShiro = xsMap.get(proId) != 0 ? false : true;
                } else {// 生设
                    haveEditShiro = ssMap.get(proId) != 0 ? false : true;
                }
                if (xsMap.size == 0 && ssMap.size == 0) {
                    haveEditShiro = true;
                }
                if (!haveEditShiro) {
                    var field = obj.field;
                    var oldValue = obj.oldValue;
                    var update = {};
                    update[field] = oldValue;
                    obj.update(update, true)
                    layer.tips('当前数据您没有操作权限', this, { tips: 1 });
                } else {
                    var field = obj.field; // 得到字段
                    var value = obj.value; // 得到修改后的值
                    var data = obj.data; // 得到所在行所有键值
                    if (field === 'weight' || field === 'amount' || field === 'restAmount') {
                        if (!/^(0|[1-9]\d*)$/.test(obj.value)) {
                            layer.tips('输入的数量不正确，请重新编辑', this, { tips: 1 });
                            return obj.reedit(); // 重新编辑 -- v2.8.0 新增
                        }
                    }
                    if (field === 'restAmount') {
                        if (Number(value) > (Number(obj.data.amount) - Number(obj.data.useAmount))) {
                            layer.tips("超过剩余可使用物资数量，请重新填写", this, { tips: 1 })
                            return obj.reedit();
                        }
                    }
                    let headIds = [data.phmpId]
                    let sapParam = {
                        porMainIdList: headIds,
                        dataType: "9"
                    }
                    if (data.uploadStatus == '1') {
                        let sapStatus = '0'
                        let tip = this
                        febs.post(ctx + 'sap/sapRecordSync', sapParam, function (d) {
                            if (d.code == 200) {
                                if (d.data.resultFlg) {
                                    febs.getSyn(ctx + 'por/porLineMaterialPurchase/getSapStatusById', { lineId: data.plmpId }, function (e) {
                                        if (e.code == '200') {
                                            sapStatus = e.data
                                        }
                                    })
                                    if (sapStatus == '1') {
                                        layer.tips("该物资SAP已处理，无法修改", tip, { tips: 1 })
                                        return obj.reedit();
                                    }
                                    saveRow(data);
                                } else {
                                    layer.open({
                                        title: '同步采购失败信息',
                                        type: 1,
                                        area: ['20%', '40%'],
                                        content: '<div style="padding: 16px;">' + d.data.msg + '</div>',
                                        success: function () {
                                        },
                                    });
                                    return obj.reedit();
                                }
                            }
                        });
                    } else {
                        saveRow(data);
                    }
                }
            });

            function saveRow(data) {
                let arr1 = [{
                    phmpId: data.phmpId,
                    plmpId: data.plmpId,
                    amount: data.amount,
                    remark: data.remark,
                    restAmount: data.restAmount,
                    restFlg: data.restFlg
                }];
                let arr2 = [{
                    basicMaterialNo: data.baiscMaterialNo,
                    materialDesc: data.materialDesc,
                    weight: data.weight,
                    weightUnit: data.weightUnit
                }];
                febs.postArray(ctx + 'por/porLineMaterialPurchase/updateBatch', arr1, function () {
                    febs.postArray(ctx + 'por/porLineDeviceProtocol/updateNoStandardBatch', arr2, function () {
                        febs.alert.success('保存成功');
                        $view.find('#queryOther2').trigger('click');
                    })
                })
            }

            function showPorLineMaterial(obj) {
                $view.find('.showBgColor').removeClass('showBgColor')
                $view.find('.layui-table-click').addClass('showBgColor')
                let basicMaterialDesc = $view.find('input[name= basicMaterialDesc]').val();
                let basicMaterialNo = $view.find('input[name= basicMaterialNo]').val();
                phmpId = obj.data.phmpId
                headStatus = obj.data.sapStatus
                let params = getQueryParams()
                params.phmpId = obj.data.phmpId
                if (basicMaterialDesc != "" || basicMaterialNo != "") {
                    tableInsTwo.reload({ url: ctx + 'por/porLineMaterialPurchase/list', where: params });
                } else {
                    tableInsTwo.reload({ url: ctx + 'por/porLineMaterialPurchase/list', where: { phmpId: obj.data.phmpId } });
                }
            }

            function getQueryParams() {
                // let ssIds = [];
                // let xsIds = [];
                // $.each(ssProfessionUserList,function (i,v) {
                //     ssIds.push(v.professionId);
                // })
                // $.each(xsProfessionUserList,function (i,v) {
                //     xsIds.push(v.professionId);
                // })
                if ($view.find('#codeType').val() != '') {
                    // if ($view.find('input[name= basicMaterialDesc]').val() !== '' || $view.find('input[name= basicMaterialNo]').val() !== ''){
                    //     return {
                    //         shipId: shipNos.getValue('value').length === 0 ? null:shipNos.getValue('value')[0],
                    //         basicMaterialDesc: $view.find('input[name= basicMaterialDesc]').val(),
                    //         basicMaterialNo: $view.find('input[name= basicMaterialNo]').val(),
                    //         // ssProfessionUserList: ssIds,
                    //         // xsProfessionUserList: xsIds
                    //     }
                    // }else {
                    //     return {
                    //         shipId: shipNos.getValue('value').length === 0 ? null:shipNos.getValue('value')[0],
                    //         porHeadNo : $view.find('input[name = porHeadNo]').val(),
                    //         directorName : $view.find('input[name = directorName]').val(),
                    //         status: $view.find('#codeType').val(),
                    //         sapStatus: $view.find('#sapStatus').val(),
                    //         basicMaterialDesc: '',
                    //         basicMaterialNo: '',
                    //         strId: regions.getValue('value').length === 0 ? null:regions.getValue('value')[0],
                    //         proId: professions.getValue('value').length === 0 ? null:professions.getValue('value')[0],
                    //         proType: proTypeList.getValue('value').length === 0 ? null:proTypeList.getValue('value')[0],
                    //         // ssProfessionUserList: ssIds,
                    //         // xsProfessionUserList: xsIds
                    //     };
                    // }
                    return {
                        shipId: shipNos.getValue('value').length === 0 ? null : shipNos.getValue('value')[0],
                        porHeadNo: $view.find('input[name = porHeadNo]').val(),
                        directorName: $view.find('input[name = directorName]').val(),
                        status: $view.find('#codeType').val(),
                        sapStatus: $view.find('#sapStatus').val(),
                        basicMaterialDesc: $view.find('input[name= basicMaterialDesc]').val(),
                        basicMaterialNo: $view.find('input[name= basicMaterialNo]').val(),
                        strId: regions.getValue('value').length === 0 ? null : regions.getValue('value')[0],
                        proId: professions.getValue('value').length === 0 ? null : professions.getValue('value')[0],
                        proType: proTypeList.getValue('value').length === 0 ? null : proTypeList.getValue('value')[0],
                        // ssProfessionUserList: ssIds,
                        // xsProfessionUserList: xsIds
                    };
                } else {
                    // if ($view.find('input[name= basicMaterialDesc]').val() !== '' || $view.find('input[name= basicMaterialNo]').val() !== ''){
                    //     return {
                    //         shipId: shipNos.getValue('value').length === 0 ? null:shipNos.getValue('value')[0],
                    //         basicMaterialDesc: $view.find('input[name= basicMaterialDesc]').val(),
                    //         basicMaterialNo: $view.find('input[name= basicMaterialNo]').val(),
                    //         // ssProfessionUserList: ssIds,
                    //         // xsProfessionUserList: xsIds
                    //     }
                    // }else {
                    //     return {
                    //         shipId: shipNos.getValue('value').length === 0 ? null:shipNos.getValue('value')[0],
                    //         porHeadNo : $view.find('input[name = porHeadNo]').val(),
                    //         directorName : $view.find('input[name = directorName]').val(),
                    //         sapStatus: $view.find('#sapStatus').val(),
                    //         basicMaterialDesc: '',
                    //         basicMaterialNo: '',
                    //         strId: regions.getValue('value').length === 0 ? null:regions.getValue('value')[0],
                    //         proId: professions.getValue('value').length === 0 ? null:professions.getValue('value')[0],
                    //         proType: proTypeList.getValue('value').length === 0 ? null:proTypeList.getValue('value')[0],
                    //         // ssProfessionUserList: ssIds,
                    //         // xsProfessionUserList: xsIds
                    //     };
                    // }
                    return {
                        shipId: shipNos.getValue('value').length === 0 ? null : shipNos.getValue('value')[0],
                        porHeadNo: $view.find('input[name = porHeadNo]').val(),
                        directorName: $view.find('input[name = directorName]').val(),
                        sapStatus: $view.find('#sapStatus').val(),
                        basicMaterialDesc: $view.find('input[name= basicMaterialDesc]').val(),
                        basicMaterialNo: $view.find('input[name= basicMaterialNo]').val(),
                        strId: regions.getValue('value').length === 0 ? null : regions.getValue('value')[0],
                        proId: professions.getValue('value').length === 0 ? null : professions.getValue('value')[0],
                        proType: proTypeList.getValue('value').length === 0 ? null : proTypeList.getValue('value')[0],
                        // ssProfessionUserList: ssIds,
                        // xsProfessionUserList: xsIds
                    };
                }
            }

            // 移库查询条件
            function getPreviousQueryParams() {
                return {
                    dstShipId: shipNos.getValue('value').length === 0 ? null : shipNos.getValue('value')[0],
                    proType: proTypeList.getValue('value').length === 0 ? null : proTypeList.getValue('value')[0],
                    principalUserName: $view.find('input[name = directorName]').val(),
                    strIds: regions.getValue('value').length === 0 ? null : regions.getValue('value')[0],
                    porHeadNo: $view.find('input[name = porHeadNo]').val(),
                    porUploadStatus: $view.find('#codeType').val(),
                    basicMaterialNo: $view.find('input[name= basicMaterialNo]').val(),
                    basicMaterialDesc: $view.find('input[name= basicMaterialDesc]').val(),
                    professionId: professions.getValue('value').length === 0 ? null : professions.getValue('value')[0],
                };
            }

            // 下方移库查询条件
            function getPreviousQueryParams2() {
                return {
                    dstShipId: shipNos.getValue('value').length === 0 ? null : shipNos.getValue('value')[0],
                    proType: proTypeList.getValue('value').length === 0 ? null : proTypeList.getValue('value')[0],
                    principalUserName: $view.find('input[name = directorName]').val(),
                    strIds: regions.getValue('value').length === 0 ? null : regions.getValue('value')[0],
                    porHeadNo: $view.find('input[name = porHeadNo]').val(),
                    porUploadStatus: $view.find('#codeType').val(),
                    basicMaterialNo: $view.find('#basicMaterialNoPrevious').val(),
                    basicMaterialDesc: $view.find('#basicMaterialDescPrevious').val(),
                    professionId: professions.getValue('value').length === 0 ? null : professions.getValue('value')[0],
                };
            }


            function getQueryParamsTwo() {
                return {
                    phmpId: phmpId,
                    basicMaterialNo: $searchForm.find('input[name="basicMaterialNoLine"]').val(),
                    basicMaterialDesc: $searchForm.find('input[name = basicMaterialDescLine]').val(),
                }
            }

            let isDrawflg = false;


            $(document).ready(function () {
                $('#btnRemark').draggable({
                    containment: "#febs-proMaterialManger",
                    grid: [1, 1],
                })
                $('#btnRemark').on('drag', function (e, u) {
                    let newPosition = u.helper.position();
                    $(this).data('position', newPosition)
                    if ($('#btnRemarkContent').offset().left < 0) {
                        $('#btnRemarkContent').css('left', $('#btnRemarkContent').offset().left + 259)
                        $('#btnRemarkContent').css('top', newPosition.top + 30)
                    } else {
                        $('#btnRemarkContent').css('top', newPosition.top + 30)
                        $('#btnRemarkContent').css('left', newPosition.left - 227)
                    }

                })

                $('#btnRemark').mousedown(function () {
                    isDrawflg = false;
                }).mousemove(function () {
                    isDrawflg = true;
                })

                //颜色功能友好提示
                $('#btnRemark').on('click', function () {
                    //false 标识不是拖拽操作 而是点击操作
                    if (!isDrawflg) {
                        let vis = $('#btnRemarkContent').is(":visible");
                        if (vis) {
                            //开了 就关
                            $('#btnRemarkContent').fadeOut();
                        } else {
                            //关了 就开
                            $('#btnRemarkContent').fadeIn();
                            if ($('#btnRemarkContent').offset().left < 0) {
                                $('#btnRemarkContent').css('left', $('#btnRemarkContent').offset().left + 259)
                            }

                        }
                    }
                })
            })
            let pageLeft = Number($('#transferMaterialTips').css('left').split('px')[0]) - 1135;
            // 物资移库窗口
            $('#transferMaterialTips').draggable({
                containment: "#febs-proMaterialManger",
                grid: [1, 1],
                start: function (e, u) {
                    let $movePage = $('#transferMaterialTipsContent')
                    if ($movePage.css('left') != 'auto') {
                        pageLeft = parseInt($movePage.css('left').split('px')[0])
                    }
                },
                drag: function (e, u) {
                    let $movePage = $('#transferMaterialTipsContent')
                    let newPosition = u.helper.position();
                    let oriLeft = u.originalPosition.left;
                    let left = u.position.left;
                    $(this).data('position', newPosition)
                    $movePage.css('top', newPosition.top + 30)
                    $movePage.css('left', pageLeft - (oriLeft - left))
                },
            })

            //颜色功能友好提示
            $('#transferMaterialTips').on('click', function () {
                //false 标识不是拖拽操作 而是点击操作
                let vis = $('#transferMaterialTipsContent').is(":visible");
                if (vis) {
                    //开了 就关
                    $('#transferMaterialTipsContent').fadeOut();
                } else {
                    //关了 就开
                    $('#transferMaterialTipsContent').fadeIn();
                    // if ($('#transferMaterialTipsContent').offset().left < -550) {
                    //     $('#transferMaterialTipsContent').css('left', $('#transferMaterialTipsContent').offset().left + 1100)
                    // }

                    // 打开时检索
                    $('#transferQuery').trigger('click')
                    table.setRowChecked('applyStorgeList', { index: 'all' })// 简单粗暴 重开就全选不记忆
                }
            })

            $('#transferMaterialForApply').on('click', function () {
                let checkStatus = table.checkStatus('protocolList2');
                if (checkStatus.data.length <= 0) {
                    febs.alert.warn('请至少选择一条Line数据');
                    return false;
                }

                let params = [];
                let flg = false;
                $.each(checkStatus.data, function (i, v) {
                    let param = v;
                    if (param.sapStatus != 1) {
                        febs.alert.warn('所选数据需要已确认');
                        flg = true;
                        return;
                    } else {
                        param.basicMaterialNo = v.baiscMaterialNo
                        param.basicMaterialDesc = v.materialDesc
                        params.push(param)
                    }
                })
                if (flg) {
                    return;
                }
                febs.modal.open('物资移库', 'por/porDeviceManageView/transferMaterials', {
                    btn: '提交',
                    area: ['80%', '80%'],
                    data: {
                        source: 'material',
                        phDesc: linePhDesc,
                        porId: porId,
                        porType: porType,
                        lineInfos: params,
                        type: 'tips',
                    },
                    yes: function (e) {
                        $('#transferMaterials').find('#submit').trigger('click');
                        addWindowLinstener();
                    }
                })
            })

            setProTypeTransfer()

            //  物资移库tips
            function setProTypeTransfer() {
                professionsTransfer = xmSelect.render({
                    el: '#professionsTransfer',
                    data: [],
                    radio: true,
                    clickClose: true,
                    filterable: true,
                    height: '150px',
                    tree: {
                        show: true,
                        strict: false,

                    },
                    template({ item }) {
                        return '<div class="selectNameDiv">' + item.name + '</div>'
                            + '<div class="selectShowNameDiv">'
                            + '<span style="padding-right: 5px;" title="' + item.desc + '">' + item.desc + '</span>'
                            + '</div>'
                    },
                    // iconfont : {
                    //     parent : 'hidden' //隐藏父节点图标
                    // },
                })
            }

            // tips查询 查的是已添加的数据 暂定  TODO:加条件
            $('#transferQuery').on('click', function (d) {
                let shipId = porShipNoSelect.getValue('valueStr');
                let dstShipId = dstShipNoSelect.getValue('valueStr');
                let professionId = professionsTransfer.getValue('value').length === 0 ? '' : professionsTransfer.getValue('value')[0];
                let porType = proTypeListTransfer.getValue('value').length === 0 ? '' : proTypeListTransfer.getValue('value')[0];
                let porNo = $('#porHeadNoTransfer').val();// 以下支持模糊匹配
                let basicMaterialNoOrDesc = $('#basicMaterialNoOrDesc').val();
                let dataArr = JSON.parse(localStorage.getItem('porMaterialInfos'))
                // 根据条件做全匹配或模糊匹配
                if (shipId != '') {
                    dataArr = dataArr.filter(item => shipId == item.porShipId)
                }
                if (dstShipId != '') {
                    dataArr = dataArr.filter(item => dstShipId == item.dstShipId)
                }
                if (professionId != '') {
                    dataArr = dataArr.filter(item => professionId == item.porId)
                }
                if (porType != '') {
                    dataArr = dataArr.filter(item => porType == item.porType)
                }
                if (porNo != '') {
                    const regex = new RegExp(porNo);
                    dataArr = dataArr.filter(item => regex.test(item.porNo))
                }
                if (basicMaterialNoOrDesc != '') {
                    const regex = new RegExp(basicMaterialNoOrDesc);
                    dataArr = dataArr.filter(item => regex.test(item.basicMaterialNo) || regex.test(item.basicMaterialDesc))
                }

                createTransferTable((dataArr != null ? dataArr : []))
            })

            // 取得存在缓存的数据展示在页面上
            reloadTipsTable()

            function reloadTipsTable() {
                let dataArr = JSON.parse(localStorage.getItem('porMaterialInfos'))

                createTransferTable((dataArr != null ? dataArr : []));
            }

            function createTransferTable(dataArr) {
                transferTableIns = febs.table.init({
                    elem: $view.find('#applyStorgeList'),
                    id: 'applyStorgeList',
                    data: dataArr,
                    autoSort: true,
                    sort: true,
                    // toolbar: '#deviceProtocolListMangerToolbar',
                    css: [ // 重设当前表格样式
                        '.layui-table-tool-temp{padding-right: 145px;} .layui-table-cell .layui-form-checkbox[lay-skin=primary] {top: -8px}'
                    ].join(''),
                    defaultToolbar: [],
                    height: '#applyStorge-1',
                    cols: [
                        [
                            { type: 'checkbox' },
                            { field: 'porShipNo', title: '当前船', width: 95, align: 'center' },
                            {
                                field: 'porNo', title: 'porNo', align: 'center', width: 90, templet: function (d) {
                                    return '<div style="text-align: left">' + d.porNo + '</div>'
                                }
                            },
                            {
                                field: 'porDesc', title: 'por描述', width: 150, align: 'center', edit: function (d) {
                                    if (d.sapStatus == 0) {
                                        if (!permiFlag) {
                                            return null;
                                        }
                                        return "text"
                                    }
                                },
                                templet: function (d) {
                                    return '<div style="text-align: left">' + d.porDesc + '</div>'
                                }
                            },

                            { field: 'lineNo', title: 'LineNo', width: 50, align: 'center' },
                            { field: 'basicMaterialNo', title: '物资编码', width: 180, align: 'center' },
                            { field: 'basicMaterialDesc', title: '物资描述', minWidth: 90, align: 'center' },
                            {
                                field: 'amount',
                                title: '<span style="color: #1b83f0;font-weight: bold">移库数量</span>',
                                width: 90,
                                align: 'center',
                                edit: function (d) {
                                    if (d.appendixFlg == '0') {
                                        return 'text'
                                    }
                                },
                                templet: function (d) {
                                    if (d.appendixFlg == '0') {
                                        return d.amount
                                    } else {
                                        return '<span lay-event="updateAppendix" style="text-decoration: underline;">' + (d.amount) + '</span>'
                                    }

                                }
                            },
                            {
                                field: 'appendixFlg',
                                title: '附件号标记',
                                align: 'center',
                                width: 100,
                                minWidth: 70,
                                templet: function (d) {
                                    if (d.appendixFlg == 1) {
                                        return `<span class="layui-badge" style="background-color: #4dbeba" >是</span>`
                                    } else {
                                        return `<span class="layui-badge" style="background-color: #e7d9cc">否</span>`

                                    }
                                }
                            },
                            { field: 'dstShipNo', title: '目标船', width: 90, align: 'center' },
                        ]
                    ],
                    page: false,
                    done: function (res, curr, count, origin) {
                    }
                })
            }

            table.on('tool(applyStorgeList)', function (obj) {
                let data = obj.data;
                switch (obj.event) {
                    case 'updateAppendix':
                        updateAppendixList(data);
                        break;
                }

            })

            function updateAppendixList(obj) {
                let appendixCodes = []
                $.each(obj.appendixTransVoList, function (i, v) {
                    appendixCodes.push(v.appendixCode)
                })
                febs.modal.open('编辑目标船附件号', 'por/porLineMaterialManageAdd/transferMaterialAppendixs', {
                    btn: ['确定'],
                    area: ['550px', '400px'],
                    data: {
                        shipId: obj.shipId,
                        porId: obj.phId,
                        plmpId: obj.lineId,
                        headStatus: obj.sapStatus,
                        amount: obj.amount,
                        type: 'update',
                        source: 'material',
                        dstShipId: obj.dstShipId,
                        appendixCodes: appendixCodes
                    },
                    yes: function (index, layero) {
                        $('#transferMaterialAppendixs').find('#appendixSubmit').trigger('click');
                        layer.close(index)
                        $('#transferQuery').trigger('click');
                    }
                });
            }

            table.on('edit(applyStorgeList)', function (obj) { //注：edit是固定事件名，test是table原始容器的属性 lay-filter="对应的值"
                if (!/^(0|[1-9]\d*)$/.test(obj.value)) {
                    layer.tips('输入的数量不正确，请重新编辑', this, { tips: 1 });
                    return obj.reedit(); // 重新编辑 -- v2.8.0 新增
                }
                if (parseInt(obj.value) == 0 || parseInt(obj.value) > parseInt(obj.data.oldAmount)) {
                    layer.tips('输入的数量不正确，请重新编辑', this, { tips: 1 });
                    return obj.reedit(); // 重新编辑 -- v2.8.0 新增
                }
            });
            $('#transferCancle').on('click', function (d) {
                $('#transferMaterialTipsContent').fadeOut();
            })

            $('#transferSubmit').on('click', function (d) {
                let checkStatus = table.checkStatus('applyStorgeList');
                let dataArr = [];
                if (checkStatus.data.length != 0) {
                    dataArr = checkStatus.data
                } else {
                    dataArr = JSON.parse(localStorage.getItem('porMaterialInfos'))
                }

                if (dataArr == null || dataArr.length == 0) {
                    febs.alert.warn("没有要提交的数据!")
                    return false;
                } else {
                    commonJS.loading_open("正在校验移库物资是否满足SAP物资库存数量...")
                    febs.postArray(ctx + 'por/materialPorApplyInfo/stockValid', dataArr, function (d) {
                        if (d.code == 200) {
                            commonJS.loading_close();
                            febs.modal.open('物资申请单提交', 'por/porDeviceManageView/previousApplySubDetailSubmit', {
                                btn: ['确认', '取消'],
                                area: ['450px', '310px'],
                                data: {
                                    tableIns: POPUP_DATA.tableIns,
                                    materialList: dataArr,
                                    source: 'material'
                                },
                                yes: function (e) {
                                    $('#previousShipPorApplyInfoSubmit').find('#addPreviousShipPorApplySubmit').trigger('click');
                                }
                            });
                        }
                    })
                    //测试用
                    // febs.modal.open('物资申请单提交', 'por/porDeviceManageView/previousApplySubDetailSubmit', {
                    //     btn:['确认','取消'],
                    //     area: ['450px','310px'],
                    //     data:{
                    //         tableIns:POPUP_DATA.tableIns,
                    //         materialList: dataArr,
                    //         source: 'material'
                    //     },
                    //     yes: function(e){
                    //         $('#previousShipPorApplyInfoSubmit').find('#addPreviousShipPorApplySubmit').trigger('click');
                    //     }
                    // });
                }
            })

            // 删除 删除缓存
            $('#deleteStorge').on('click', function (d) {
                let checkStatus = table.checkStatus('applyStorgeList');
                if (checkStatus.data.length == 0) {
                    febs.alert.error('请选择一条数据!')
                    return false;
                }
                febs.modal.confirm('确认', '是否删除选中的数据', function () {
                    let storgeDatas = JSON.parse(localStorage.getItem('porMaterialInfos'));
                    let datas = [];
                    $.each(storgeDatas, function (i, v) {
                        let flg = false;
                        $.each(checkStatus.data, function (a, b) {
                            if (v.lineId == b.lineId && v.dstShipId == b.dstShipId) {
                                flg = true;
                            }
                        })
                        if (!flg) {
                            datas.push(v)
                        }
                    })
                    if (datas.length == 0) {
                        localStorage.removeItem('porMaterialInfos')
                    } else {
                        localStorage.setItem('porMaterialInfos', JSON.stringify(datas))
                    }
                    table.reload('applyStorgeList', {
                        data: datas,
                    })
                })
            })
            let applyStorgeHeight = 0;
            let searchHeight = 0;
            let pageHeight = 0;
            let wFlag = false;
            let sFlag = false;
            let top;
            $view.find('#transferMaterialTipsContent').resizable({
                containment: "#febs-proMaterialManger",
                handles: "w,s",
                grid: 1,
                minWidth: 600,
                minHeight: 230,
                maxHeight: 700,
                start: function (event, ui) {
                    applyStorgeHeight = $view.find("#applyStorge").height();
                    searchHeight = $view.find("#searchDiv").height();
                    pageHeight = $view.find("#transferMaterialTipsContent").height();
                    top = $view.find("#transferMaterialTipsContent").css('top')
                    wFlag = $(event.originalEvent.target).hasClass('ui-resizable-w');
                    sFlag = $(event.originalEvent.target).hasClass('ui-resizable-s');
                },
                stop: function (event, ui) {

                },
                resize: function (event, ui) {
                    $view.find("#transferMaterialTipsContent").css('top', top)
                    let offsetTop = ui.originalSize.height - ui.size.height;
                    if (offsetTop !== 0 && sFlag) {
                        $view.find("#applyStorge").height(applyStorgeHeight - offsetTop)
                        $view.find("#applyStorge").find('.layui-table-main').height(applyStorgeHeight - offsetTop)
                    }
                    let offsetLeft = ui.originalSize.width - ui.size.width;
                    if (offsetLeft !== 0 && wFlag) {
                        let newSearchHeight = $view.find("#searchDiv").height();
                        $view.find("#transferMaterialTipsContent").height(pageHeight - searchHeight + newSearchHeight);
                    }
                }
            })


            $view.find('#queryOther1').on('click', function () {
                const params = getQueryParams();
                table.reloadData(tableIns.config.id, {
                    where: params,
                    scrollPos: 'fixed'
                })
            })

            $view.find('#queryOther2').on('click', function () {
                var paramTwo = getQueryParamsTwo()
                table.reloadData(tableInsTwo.config.id, {
                    where: paramTwo,
                    scrollPos: 'fixed'
                })
            })

            // 初始化拖拽调整功能
            function initTableResize() {
                var $mainContainer = $('#mainTableContainer');
                var $detailContainer = $('#detailTableContainer');
                var $previousContainer = $('#previousTableContainer');

                // 第二个表格高度固定为150px，不可调整
                // 移除第二个表格的resizable功能

                // 第一个表格的拖拽调整 - 只影响第一个和第三个表格
                $mainContainer.resizable({
                    handles: "s",
                    grid: 1,
                    minHeight: 150,
                    delay: 0, // 无延迟
                    distance: 1, // 最小拖拽距离
                    start: function () {
                        // 记录初始高度
                        this.startMainHeight = $mainContainer.height();
                        this.startPreviousHeight = $previousContainer.height();
                        // 第二个表格高度固定为150px，不参与计算
                        this.totalAvailableHeight = this.startMainHeight + this.startPreviousHeight;

                        // 临时移除flex属性，使用固定高度进行拖拽
                        $mainContainer.css({
                            'flex': 'none',
                            'height': this.startMainHeight + 'px'
                        });
                        $previousContainer.css({
                            'flex': 'none',
                            'height': this.startPreviousHeight + 'px'
                        });
                    },
                    resize: function (event, ui) {
                        var heightChange = ui.size.height - this.startMainHeight;
                        var newPreviousHeight = this.startPreviousHeight - heightChange;

                        // 确保第三个表格最小高度
                        if (newPreviousHeight < 150) {
                            newPreviousHeight = 150;
                            ui.size.height = this.startMainHeight + this.startPreviousHeight - 150;
                        }

                        // 确保第一个表格最小高度
                        if (ui.size.height < 150) {
                            ui.size.height = 150;
                            newPreviousHeight = this.startMainHeight + this.startPreviousHeight - 150;
                        }

                        // 调整第三个表格的高度，第二个表格保持150px固定高度不变
                        $previousContainer.css('height', newPreviousHeight + 'px');

                        // 表格会自动适应内容，通过外层滚动容器实现滚动
                    },
                    stop: function (event, ui) {
                        // 拖拽结束后，可以选择保持固定高度或恢复flex布局
                        // 这里我们保持固定高度，因为用户已经手动调整了
                    }
                });

            }

            // 直接调用初始化拖拽功能
            initTableResize();
        })

    </script>
</body>

</html>